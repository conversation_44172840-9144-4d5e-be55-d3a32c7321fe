import {
  Room,
  Booking,
  RoomInventoryStatus,
  RoomConfig,
  RoomAvailability,
  HotelAvailabilityAPIResponse,
} from "../types";

// Mock room configurations based on API response structure
export const mockRoomConfigs: RoomConfig[] = [
  {
    id: "prod_01JWDT5BNBDPZ7MMNHAGK95TWT",
    title: "Junior Suite",
    handle: "room-config-junior-suite",
    subtitle: null,
    description: "Comfortable junior suite with modern amenities",
    is_giftcard: false,
    status: "published",
    thumbnail: null,
    weight: null,
    length: null,
    height: null,
    width: null,
    origin_country: null,
    hs_code: null,
    mid_code: null,
    material: null,
    discountable: true,
    external_id: null,
    metadata: {
      type: "standard",
      bed_type: "king",
      hotel_id: "01JWDSWC6RGPD00W1XRW9ER415",
      max_cots: 2,
      amenities: ["King bed", "Bathroom", "TV", "Safe", "Minibar"],
      room_size: "35-40 sqm",
      max_adults: 2,
      max_infants: 1,
      max_children: 1,
      price_set_id: "pset_01JWNXF3VHZ3DX6TPPY3X5FEZG",
      max_occupancy: 4,
      max_extra_beds: 1,
      max_adults_beyond_capacity: 1,
    },
    type_id: null,
    type: null,
    collection_id: null,
    collection: null,
    created_at: "2025-05-29T10:42:15.852Z",
    updated_at: "2025-05-29T10:42:15.852Z",
    deleted_at: null,
    variants: [],
  },
  {
    id: "prod_01JWDT767PMNZ3RJS4WAJ1HV3H",
    title: "Superior Suite with 1 Bedroom",
    handle: "room-config-superior-suite-1br",
    subtitle: null,
    description: "Spacious suite with separate bedroom and living area",
    is_giftcard: false,
    status: "published",
    thumbnail: null,
    weight: null,
    length: null,
    height: null,
    width: null,
    origin_country: null,
    hs_code: null,
    mid_code: null,
    material: null,
    discountable: true,
    external_id: null,
    metadata: {
      type: "standard",
      bed_type: "king",
      hotel_id: "01JWDSWC6RGPD00W1XRW9ER415",
      max_cots: 2,
      amenities: [
        "King bed",
        "Living room",
        "Bathroom",
        "Balcony",
        "TV",
        "Safe",
      ],
      room_size: "50-60 sqm",
      max_adults: 3,
      max_infants: 2,
      max_children: 2,
      price_set_id: "pset_01JWNXF4BGJEQYAV7KTYC8XXAA",
      max_occupancy: 7,
      max_extra_beds: 2,
      max_adults_beyond_capacity: 2,
    },
    type_id: null,
    type: null,
    collection_id: null,
    collection: null,
    created_at: "2025-05-29T10:43:15.830Z",
    updated_at: "2025-05-29T10:43:15.830Z",
    deleted_at: null,
    variants: [],
  },
];

// Mock rooms based on API response structure
export const mockRooms: Room[] = [
  {
    id: "variant_01JWDTGKEPQRV5R0KKDH9P066Z",
    title: "Junior Suite 101",
    sku: null,
    barcode: null,
    ean: null,
    upc: null,
    allow_backorder: false,
    manage_inventory: false,
    hs_code: null,
    origin_country: null,
    mid_code: null,
    material: null,
    weight: null,
    length: null,
    height: null,
    width: null,
    metadata: {
      floor: "1",
      notes: "",
      status: "available",
      is_active: true,
      left_room: "",
      right_room: "",
      room_number: "101",
      opposite_room: "",
      connected_room: "",
      availability_type: null,
    },
    variant_rank: 0,
    product_id: "prod_01JWDT5BNBDPZ7MMNHAGK95TWT",
    created_at: "2025-05-29T10:48:24.278Z",
    updated_at: "2025-05-29T10:48:24.278Z",
    deleted_at: null,
    room_config_id: "prod_01JWDT5BNBDPZ7MMNHAGK95TWT",
    config_name: "Junior Suite",
    room_number: "101",
    name: "Junior Suite 101",
  },
  {
    id: "variant_01JXQSFYHXMKWK7RVBXMG6YP1E",
    title: "Junior Suite 102",
    sku: null,
    barcode: null,
    ean: null,
    upc: null,
    allow_backorder: false,
    manage_inventory: false,
    hs_code: null,
    origin_country: null,
    mid_code: null,
    material: null,
    weight: null,
    length: null,
    height: null,
    width: null,
    metadata: {
      floor: "1",
      notes: "",
      status: "available",
      is_active: true,
      left_room: "",
      right_room: "",
      room_number: "102",
      opposite_room: "",
      connected_room: "",
      availability_type: null,
    },
    variant_rank: 0,
    product_id: "prod_01JWDT5BNBDPZ7MMNHAGK95TWT",
    created_at: "2025-05-29T10:48:24.278Z",
    updated_at: "2025-05-29T10:48:24.278Z",
    deleted_at: null,
    room_config_id: "prod_01JWDT5BNBDPZ7MMNHAGK95TWT",
    config_name: "Junior Suite",
    room_number: "102",
    name: "Junior Suite 102",
  },
  {
    id: "variant_01JWDTH0ZX09FM5N5HJDSA6BA1",
    title: "Superior Suite 201",
    sku: null,
    barcode: null,
    ean: null,
    upc: null,
    allow_backorder: false,
    manage_inventory: false,
    hs_code: null,
    origin_country: null,
    mid_code: null,
    material: null,
    weight: null,
    length: null,
    height: null,
    width: null,
    metadata: {
      floor: "2",
      notes: "",
      status: "available",
      is_active: true,
      left_room: "",
      right_room: "",
      room_number: "201",
      opposite_room: "",
      connected_room: "",
      availability_type: null,
    },
    variant_rank: 0,
    product_id: "prod_01JWDT767PMNZ3RJS4WAJ1HV3H",
    created_at: "2025-05-29T10:48:38.141Z",
    updated_at: "2025-05-29T10:48:38.141Z",
    deleted_at: null,
    room_config_id: "prod_01JWDT767PMNZ3RJS4WAJ1HV3H",
    config_name: "Superior Suite with 1 Bedroom",
    room_number: "201",
    name: "Superior Suite 201",
  },
  {
    id: "variant_01JXQE6P19ZZAT6GK586YYS2RA",
    title: "Superior Suite 202",
    sku: null,
    barcode: null,
    ean: null,
    upc: null,
    allow_backorder: false,
    manage_inventory: false,
    hs_code: null,
    origin_country: null,
    mid_code: null,
    material: null,
    weight: null,
    length: null,
    height: null,
    width: null,
    metadata: {
      floor: "2",
      notes: "VIP room with special amenities",
      status: "available",
      is_active: true,
      left_room: "",
      right_room: "",
      room_number: "202",
      opposite_room: "",
      connected_room: "",
      availability_type: null,
    },
    variant_rank: 0,
    product_id: "prod_01JWDT767PMNZ3RJS4WAJ1HV3H",
    created_at: "2025-06-14T14:41:22.480Z",
    updated_at: "2025-06-14T14:41:22.480Z",
    deleted_at: null,
    room_config_id: "prod_01JWDT767PMNZ3RJS4WAJ1HV3H",
    config_name: "Superior Suite with 1 Bedroom",
    room_number: "202",
    name: "Superior Suite 202",
  },
];

const today = new Date();
today.setHours(0, 0, 0, 0);

const tomorrow = new Date(today);
tomorrow.setDate(tomorrow.getDate() + 1);

const dayAfter = new Date(today);
dayAfter.setDate(dayAfter.getDate() + 2);

const day3 = new Date(today);
day3.setDate(day3.getDate() + 3);

const day4 = new Date(today);
day4.setDate(day4.getDate() + 4);

const day5 = new Date(today);
day5.setDate(day5.getDate() + 5);

export const mockBookings: Booking[] = [
  // 🔴 BOOKED - Junior Suite rooms
  {
    id: "BK001",
    room_id: "variant_01JWDTGKEPQRV5R0KKDH9P066Z",
    guestName: "John Smith",
    checkIn: new Date(today),
    checkOut: new Date(tomorrow),
    status: RoomInventoryStatus.BOOKED,
    confirmationNumber: "CNF001",
    notes: "Early check-in requested",
    order_id: "order_01JXSBB93RF7H0NG26W7P00A0H",
  },
  {
    id: "BK002",
    room_id: "variant_01JXQSFYHXMKWK7RVBXMG6YP1E",
    guestName: "Alice Johnson",
    checkIn: new Date(dayAfter),
    checkOut: new Date(day4),
    status: RoomInventoryStatus.BOOKED,
    confirmationNumber: "CNF002",
    order_id: "order_01JXSBB93RF7H0NG26W7P00A0B",
  },
  // 🔵 RESERVED - Superior Suite rooms
  {
    id: "BK003",
    room_id: "variant_01JWDTH0ZX09FM5N5HJDSA6BA1",
    guestName: "Bob Wilson",
    checkIn: new Date(tomorrow),
    checkOut: new Date(dayAfter),
    status: RoomInventoryStatus.RESERVED,
    confirmationNumber: "CNF003",
    notes: "VIP guest - special amenities",
  },
  {
    id: "BK004",
    room_id: "variant_01JXQE6P19ZZAT6GK586YYS2RA",
    guestName: "Emma Davis",
    checkIn: new Date(day3),
    checkOut: new Date(day5),
    status: RoomInventoryStatus.RESERVED,
    confirmationNumber: "CNF004",
  },
  // 🟠 MAINTENANCE - Room maintenance
  {
    id: "MAINT001",
    room_id: "variant_01JWDTGKEPQRV5R0KKDH9P066Z",
    guestName: "", // No guest name for maintenance
    checkIn: new Date(day4),
    checkOut: new Date(day5),
    status: RoomInventoryStatus.MAINTENANCE,
    notes: "AC repair and carpet replacement",
  },
  // 🟠 CLEANING - Shows as "Maintenance" with orange color
  {
    id: "CLEAN001",
    room_id: "variant_01JXQSFYHXMKWK7RVBXMG6YP1E",
    guestName: "", // No guest name for cleaning
    checkIn: new Date(day5),
    checkOut: new Date(day5),
    status: RoomInventoryStatus.CLEANING,
    notes: "Deep cleaning after checkout",
  },
  // 🟣 ON_DEMAND - Special availability
  {
    id: "DEMAND001",
    room_id: "variant_01JXQE6P19ZZAT6GK586YYS2RA",
    guestName: "", // No guest name for on-demand
    checkIn: new Date(day5),
    checkOut: new Date(day5),
    status: RoomInventoryStatus.ON_DEMAND,
    notes: "Available for immediate booking",
  },
];

// Mock availability data based on API response structure
export const mockAvailability: RoomAvailability[] = [
  {
    room_id: "variant_01JWDTGKEPQRV5R0KKDH9P066Z",
    room_number: "101",
    room_name: "Junior Suite 101",
    config_name: "Junior Suite",
    from_date: "2025-06-29",
    to_date: "2025-06-30",
    status: "booked",
    quantity: 0,
    dynamic_price: null,
    notes: "Booking confirmed: John Smith",
    order_id: "order_01JXSBB93RF7H0NG26W7P00A0H",
  },
  {
    room_id: "variant_01JXQSFYHXMKWK7RVBXMG6YP1E",
    room_number: "102",
    room_name: "Junior Suite 102",
    config_name: "Junior Suite",
    from_date: "2025-07-01",
    to_date: "2025-07-04",
    status: "booked",
    quantity: 0,
    dynamic_price: null,
    notes: "Booking confirmed: Alice Johnson",
    order_id: "order_01JXSBB93RF7H0NG26W7P00A0B",
  },
  {
    room_id: "variant_01JWDTH0ZX09FM5N5HJDSA6BA1",
    room_number: "201",
    room_name: "Superior Suite 201",
    config_name: "Superior Suite with 1 Bedroom",
    from_date: "2025-06-30",
    to_date: "2025-07-01",
    status: "reserved",
    quantity: 0,
    dynamic_price: null,
    notes: "VIP guest - special amenities",
    order_id: "",
  },
  {
    room_id: "variant_01JXQE6P19ZZAT6GK586YYS2RA",
    room_number: "202",
    room_name: "Superior Suite 202",
    config_name: "Superior Suite with 1 Bedroom",
    from_date: "2025-07-02",
    to_date: "2025-07-05",
    status: "reserved",
    quantity: 0,
    dynamic_price: null,
    notes: "",
    order_id: "",
  },
  {
    room_id: "variant_01JWDTGKEPQRV5R0KKDH9P066Z",
    room_number: "101",
    room_name: "Junior Suite 101",
    config_name: "Junior Suite",
    from_date: "2025-07-03",
    to_date: "2025-07-04",
    status: "maintenance",
    quantity: 1,
    dynamic_price: null,
    notes: "AC repair and carpet replacement",
    order_id: "",
  },
  {
    room_id: "variant_01JXQSFYHXMKWK7RVBXMG6YP1E",
    room_number: "102",
    room_name: "Junior Suite 102",
    config_name: "Junior Suite",
    from_date: "2025-07-05",
    to_date: "2025-07-05",
    status: "cleaning",
    quantity: 1,
    dynamic_price: null,
    notes: "Deep cleaning after checkout",
    order_id: "",
  },
  {
    room_id: "variant_01JXQE6P19ZZAT6GK586YYS2RA",
    room_number: "202",
    room_name: "Superior Suite 202",
    config_name: "Superior Suite with 1 Bedroom",
    from_date: "2025-07-05",
    to_date: "2025-07-05",
    status: "on_demand",
    quantity: 1,
    dynamic_price: null,
    notes: "Available for immediate booking",
    order_id: "",
  },
];

// Complete API response structure
export const mockAPIResponse: HotelAvailabilityAPIResponse = {
  room_configs: mockRoomConfigs,
  rooms: mockRooms,
  availability: mockAvailability,
};

export const roomTypes = ["Junior Suite", "Superior Suite with 1 Bedroom"];
