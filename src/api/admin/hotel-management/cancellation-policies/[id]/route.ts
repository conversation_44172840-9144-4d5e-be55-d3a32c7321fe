import {
  AuthenticatedMedusaRequest,
  MedusaResponse,
} from "@camped-ai/framework/http";
import { z } from "zod";
import { RefundType } from "../../../../../modules/hotel-management/cancellation-policy";
import {
  GetCancellationPolicyWorkflow,
  UpdateCancellationPolicyWorkflow,
  DeleteCancellationPolicyWorkflow,
} from "../../../../../workflows/hotel-management/cancellation-policy";

// Validation schema for updating a cancellation policy
export const PostAdminUpdateCancellationPolicy = z.object({
  name: z.string().optional(),
  description: z.string().nullable().optional().default(null),
  days_before_checkin: z.number().int().min(0).optional(),
  refund_type: z
    .enum([RefundType.PERCENTAGE, RefundType.FIXED, RefundType.NO_REFUND])
    .optional(),
  refund_amount: z.number().optional(),
  is_active: z.boolean().optional(),
  metadata: z.record(z.any()).nullable().optional().default(null),
});

export type PostAdminUpdateCancellationPolicyType = z.infer<
  typeof PostAdminUpdateCancellationPolicy
>;

// GET - Get a specific cancellation policy
export const GET = async (
  req: AuthenticatedMedusaRequest,
  res: MedusaResponse
) => {
  try {
    // Use the workflow to get the policy
    const { result } = await GetCancellationPolicyWorkflow(req.scope).run({
      input: {
        id: req.params.id,
      },
    });

    return res.json({
      cancellation_policy: result,
    });
  } catch (error) {
    return res.status(404).json({
      message: "Cancellation policy not found",
      error: error.message,
    });
  }
};

// POST - Update a cancellation policy
export const POST = async (
  req: AuthenticatedMedusaRequest<PostAdminUpdateCancellationPolicyType>,
  res: MedusaResponse
) => {
  try {
    console.log("Update request body:", req.body);
    console.log("Update validated body:", req.validatedBody);

    // Try to use validatedBody first, fallback to manual validation
    let validatedBody;
    if (req.validatedBody) {
      validatedBody = req.validatedBody;
    } else {
      // Manual validation as fallback
      validatedBody = PostAdminUpdateCancellationPolicy.parse(req.body);
    }

    console.log("Final validated body:", validatedBody);

    // Validate that we have at least one field to update
    const hasUpdateFields = Object.keys(validatedBody).some((key) =>
      [
        "name",
        "description",
        "days_before_checkin",
        "refund_type",
        "refund_amount",
        "is_active",
        "metadata",
      ].includes(key)
    );

    if (!hasUpdateFields) {
      return res.status(400).json({
        message: "No valid fields provided for update",
      });
    }

    // Create a clean update object with only the fields we want to update
    const updateData: any = {};

    // Only include fields that are explicitly provided
    if (validatedBody.name !== undefined) updateData.name = validatedBody.name;
    if (validatedBody.description !== undefined)
      updateData.description = validatedBody.description;
    if (validatedBody.days_before_checkin !== undefined)
      updateData.days_before_checkin = validatedBody.days_before_checkin;
    if (validatedBody.refund_type !== undefined)
      updateData.refund_type = validatedBody.refund_type;
    if (validatedBody.is_active !== undefined)
      updateData.is_active = validatedBody.is_active;
    if (validatedBody.metadata !== undefined)
      updateData.metadata = validatedBody.metadata;

    // Handle refund amount validation
    if (validatedBody.refund_amount !== undefined) {
      updateData.refund_amount = validatedBody.refund_amount;
    }

    // Validate refund amount based on refund type if both are provided
    if (updateData.refund_type && updateData.refund_amount !== undefined) {
      if (updateData.refund_type === RefundType.PERCENTAGE) {
        if (updateData.refund_amount < 0 || updateData.refund_amount > 100) {
          return res.status(400).json({
            message:
              "Refund amount must be between 0 and 100 for percentage refund type",
          });
        }
      } else if (updateData.refund_type === RefundType.FIXED) {
        if (updateData.refund_amount < 0) {
          return res.status(400).json({
            message:
              "Refund amount must be greater than 0 for fixed refund type",
          });
        }
      } else if (updateData.refund_type === RefundType.NO_REFUND) {
        // For NO_REFUND type, set refund_amount to 0
        updateData.refund_amount = 0;
      }
    } else if (updateData.refund_type === RefundType.NO_REFUND) {
      // For NO_REFUND type, always set refund_amount to 0
      updateData.refund_amount = 0;
    }

    // Use the workflow to update the policy
    console.log("Updating policy with ID:", req.params.id);
    const { result } = await UpdateCancellationPolicyWorkflow(req.scope).run({
      input: {
        id: req.params.id,
        ...updateData,
      },
    });

    return res.json({
      cancellation_policy: result,
    });
  } catch (error) {
    console.error("Update cancellation policy error:", error);

    // Handle different error types appropriately
    if (error.type === "not_found") {
      return res.status(404).json({
        message: "Cancellation policy not found",
        error: error.message,
      });
    }

    if (error.type === "invalid_data") {
      return res.status(400).json({
        message: "Invalid data provided",
        error: error.message,
      });
    }

    return res.status(500).json({
      message: "Failed to update cancellation policy",
      error: error.message,
    });
  }
};

// DELETE - Delete a cancellation policy
export const DELETE = async (
  req: AuthenticatedMedusaRequest,
  res: MedusaResponse
) => {
  try {
    // Use the workflow to delete the policy
    await DeleteCancellationPolicyWorkflow(req.scope).run({
      input: {
        id: req.params.id,
      },
    });

    return res.status(204).send();
  } catch (error) {
    console.error("Delete cancellation policy error:", error);

    // Handle different error types appropriately
    if (error.type === "not_found") {
      return res.status(404).json({
        message: "Cancellation policy not found",
        error: error.message,
      });
    }

    return res.status(500).json({
      message: "Failed to delete cancellation policy",
      error: error.message,
    });
  }
};
