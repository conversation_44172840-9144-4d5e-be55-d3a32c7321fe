export const userCreatedDirect = {
  subject: "You've Been Invited to PB40 Ops",
  body: `
       <!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8" />
  <meta name="viewport" content="width=device-width, initial-scale=1.0"/>
  <title>You've Been Invited to PB40 Ops</title>
  <style>
    :root {
      --bg-color: #f9fafb;
      --container-bg: #ffffff;
      --text-primary: #1f2937;
      --text-secondary: #4b5563;
      --text-muted: #6b7280;
      --border-color: #e5e7eb;
      --border-light: #f3f4f6;
      --shadow-color: rgba(0, 0, 0, 0.05);
      --credentials-bg: #f1f5f9;
      --warning-bg: #fff7e6;
      --warning-border: #ffedc2;
      --warning-text: #8a6d3b;
      --primary-color: #2563eb;
      --primary-hover: #1e40af;
      --password-bg: #1e293b;
      --password-text: #ffffff;
    }

    @media (prefers-color-scheme: dark) {
      :root {
        --bg-color: #111827;
        --container-bg: #1f2937;
        --text-primary: #f9fafb;
        --text-secondary: #d1d5db;
        --text-muted: #9ca3af;
        --border-color: #374151;
        --border-light: #4b5563;
        --shadow-color: rgba(0, 0, 0, 0.3);
        --credentials-bg: #2d3748;
        --warning-bg: #4b3f28;
        --warning-border: #6b5b2a;
        --warning-text: #fcd34d;
        --primary-color: #3b82f6;
        --primary-hover: #2563eb;
        --password-bg: #f9fafb;
        --password-text: #1e293b;
      }
    }

    body {
      font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
      background-color: var(--bg-color);
      color: var(--text-primary);
      margin: 0;
      padding: 0;
      line-height: 1.6;
    }

    .container {
      max-width: 650px;
      margin: 40px auto;
      padding: 32px;
      background-color: var(--container-bg);
      border: 1px solid var(--border-color);
      border-radius: 12px;
      box-shadow: 0px 8px 20px var(--shadow-color);
    }

    .logo {
      text-align: center;
      margin-bottom: 20px;
    }

    .logo img {
      max-width: 100px;
    }

    h1 {
      text-align: center;
      color: var(--text-primary);
      margin-bottom: 20px;
    }

    p {
      text-align: center;
      color: var(--text-secondary);
    }

    .btn {
      display: block;
      width: max-content;
      margin: 20px auto;
      padding: 12px 28px;
      background-color: var(--primary-color);
      color: white;
      text-decoration: none;
      font-weight: bold;
      border-radius: 8px;
      font-size: 16px;
      transition: background-color 0.3s;
    }

    .btn:hover {
      background-color: var(--primary-hover);
    }

    .credentials-box {
      background: linear-gradient(135deg, var(--credentials-bg), #e2e8f0);
      border: 1px solid var(--border-color);
      padding: 16px;
      border-radius: 6px;
      margin-top: 20px;
      text-align: left;
    }

    .credentials-box p {
      margin: 8px 0;
      color: var(--text-primary);
    }

    .password-code {
      background-color: var(--password-bg);
      color: var(--password-text);
      padding: 6px 12px;
      border-radius: 6px;
      font-family: monospace;
      font-weight: bold;
      font-size: 14px;
      display: inline-block;
      margin-left: 10px;
    }

    .security-notice {
      background-color: var(--warning-bg);
      border: 1px solid var(--warning-border);
      color: var(--warning-text);
      padding: 15px;
      margin: 20px 0;
      border-radius: 6px;
      text-align: left;
    }

    .access-details {
      margin: 20px 0;
    }

    .access-details h3 {
      color: var(--text-primary);
      margin-bottom: 15px;
      font-size: 18px;
      text-align: left;
    }

    .first-step {
      background-color: var(--credentials-bg);
      border: 1px solid var(--border-color);
      padding: 15px;
      border-radius: 6px;
      margin: 20px 0;
      text-align: left;
    }

    .first-step p {
      margin: 0;
      color: var(--text-primary);
      text-align: left;
    }

    .footer {
      font-size: 13px;
      line-height: 1.4;
      color: var(--text-muted);
      text-align: center;
      margin-top: 30px;
    }

    .footer a {
      color: var(--primary-color);
      text-decoration: none;
    }

    .footer a:hover {
      color: var(--primary-hover);
      text-decoration: underline;
    }

    hr {
      border: none;
      border-top: 1px solid var(--border-light);
      margin: 30px 0;
    }
  </style>
</head>
<body>
  <div class="container">
    <div class="logo">
      <img src="https://dev-assets.store.flinkk.io/powderbyrne/logo.png" alt="Brand Logo" />
    </div>

    <h1>You've Been Invited to PB40 Ops</h1>
    <p>Dear <strong>{{ user.first_name }}</strong>,</p>
    <p>You now have access to PB40 Ops — the system powering our inventory, pricing, supplier data, and concierge operations.</p>

    <p>This platform is the core of how we deliver precision and client excellence. From managing room allocations to keeping supplier details accurate, every action inside PB40 Ops directly impacts our clients and business outcomes.</p>

    <div class="access-details">
      <h3>Your Access Details</h3>
      <div class="credentials-box">
        <p><strong>System:</strong> PB40 Ops</p>
        <p><strong>Login URL:</strong> <a href="{{ resetLink }}" target="_blank">{{ frontendURL }}</a></p>
        <p><strong>Username:</strong> {{ user.email }}</p>
        <p><strong>Temporary Password:</strong><span class="password-code">{{ tempPassword }}</span></p>
      </div>
    </div>

    <div class="first-step">
      <p><strong>First Step:</strong> Log in and reset your password under Settings → Profile → Change Password.</p>
    </div>

    <a href="{{ resetLink }}" class="btn">Access PB40 Ops</a>

    <div class="security-notice">
      <strong>⚠️ Important:</strong> Do not share your credentials. All actions inside PB40 Ops are logged for security and accountability.
    </div>

    <p style="margin-top: 30px; text-align: center; font-weight: 500;">Welcome aboard — let's get started.</p>

    <hr style="margin: 40px 0;" />

    <p class="footer">
      <strong>Team Flinkk</strong><br>
      Need assistance? Contact our <a href="{{ frontendURL }}/contact-us" target="_blank">support team</a>.
    </p>
  </div>
</body>
</html>

    `,
};