import { MedusaRequest, MedusaResponse } from "@camped-ai/framework/http";
import { withClient } from "src/utils/db";

/**
 * Endpoint to create category table if it doesn't exist
 * POST /admin/create-category-table
 */
export const POST = async (req: MedusaRequest, res: MedusaResponse) => {
  try {
    console.log("🔧 Creating category table...");

    await withClient(async (client) => {
      // Check if table already exists
      const tableExists = await client.query(`
        SELECT EXISTS (
          SELECT FROM information_schema.tables 
          WHERE table_schema = 'public' 
          AND table_name = 'product_service_category'
        );
      `);

      if (tableExists.rows[0].exists) {
        console.log("ℹ️ Category table already exists");
        return res.json({
          success: true,
          message: "Category table already exists",
          tableExists: true
        });
      }

      console.log("📝 Creating product_service_category table...");

      // Create the table
      await client.query(`
        CREATE TABLE "product_service_category" (
          "id" text NOT NULL,
          "name" text NOT NULL,
          "description" text NULL,
          "is_active" boolean NOT NULL DEFAULT true,
          "created_at" timestamptz NOT NULL DEFAULT now(),
          "updated_at" timestamptz NOT NULL DEFAULT now(),
          "deleted_at" timestamptz NULL,
          CONSTRAINT "product_service_category_pkey" PRIMARY KEY ("id")
        );
      `);

      console.log("📝 Creating indexes...");

      // Create indexes
      await client.query(`
        CREATE UNIQUE INDEX "IDX_product_service_category_name" 
        ON "product_service_category" ("name") 
        WHERE "deleted_at" IS NULL;
      `);

      await client.query(`
        CREATE INDEX "IDX_product_service_category_is_active" 
        ON "product_service_category" ("is_active") 
        WHERE "deleted_at" IS NULL;
      `);

      console.log("✅ Category table created successfully");

      // Verify table creation
      const columns = await client.query(`
        SELECT column_name, data_type, is_nullable 
        FROM information_schema.columns 
        WHERE table_name = 'product_service_category'
        ORDER BY ordinal_position;
      `);

      console.log("📋 Table structure:", columns.rows);
    });

    res.json({
      success: true,
      message: "Category table created successfully",
      tableExists: true
    });

  } catch (error) {
    console.error("❌ Failed to create category table:", error);
    
    res.status(500).json({
      success: false,
      message: "Failed to create category table",
      error: error instanceof Error ? error.message : "Unknown error",
      stack: error instanceof Error ? error.stack : undefined
    });
  }
};

/**
 * GET endpoint to check table status
 */
export const GET = async (req: MedusaRequest, res: MedusaResponse) => {
  try {
    console.log("🔍 Checking category table status...");

    const tableInfo = await withClient(async (client) => {
      // Check if table exists
      const tableExists = await client.query(`
        SELECT EXISTS (
          SELECT FROM information_schema.tables 
          WHERE table_schema = 'public' 
          AND table_name = 'product_service_category'
        );
      `);

      if (!tableExists.rows[0].exists) {
        return {
          exists: false,
          columns: [],
          indexes: []
        };
      }

      // Get table structure
      const columns = await client.query(`
        SELECT column_name, data_type, is_nullable, column_default
        FROM information_schema.columns 
        WHERE table_name = 'product_service_category'
        ORDER BY ordinal_position;
      `);

      // Get indexes
      const indexes = await client.query(`
        SELECT indexname, indexdef
        FROM pg_indexes 
        WHERE tablename = 'product_service_category';
      `);

      return {
        exists: true,
        columns: columns.rows,
        indexes: indexes.rows
      };
    });

    res.json({
      success: true,
      message: tableInfo.exists ? "Category table exists" : "Category table does not exist",
      tableInfo
    });

  } catch (error) {
    console.error("❌ Failed to check category table:", error);
    
    res.status(500).json({
      success: false,
      message: "Failed to check category table",
      error: error instanceof Error ? error.message : "Unknown error"
    });
  }
};
