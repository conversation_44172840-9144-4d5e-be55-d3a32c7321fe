import { MedusaRequest, MedusaResponse } from "@camped-ai/framework/http";
import { Modules } from "@camped-ai/framework/utils";
import { ICartModuleService } from "@camped-ai/framework/types";

/**
 * Admin API endpoint for listing carts that haven't been converted to orders
 *
 * This endpoint filters carts where completed_at is null, meaning they are active carts
 * that haven't been converted to orders yet. It also performs additional filtering
 * to ensure no completed carts are returned.
 *
 * When a cart is converted to an order, its completed_at field is set to a date value.
 * This endpoint only returns carts where completed_at is null.
 *
 * Results are sorted by created_at in descending order (latest carts first).
 * Pagination is supported via limit and offset query parameters.
 */
export const GET = async (req: MedusaRequest, res: MedusaResponse) => {
  try {
    const cartModuleService: ICartModuleService = req.scope.resolve(
      Modules.CART
    );

    // Extract query parameters
    const { hotel_id, guest_name, from_date, to_date, limit, offset } =
      req.query;

    // Build filter object for database query
    // Only use filters that are directly on the cart entity, not on metadata
    const filters: Record<string, any> = {};

    // Filter for carts where completed_at is null
    filters.completed_at = null;

    // Add date filters if provided
    if (from_date) {
      filters.created_at = filters.created_at || {};
      filters.created_at.$gte = new Date(from_date as string);
    }

    if (to_date) {
      filters.created_at = filters.created_at || {};
      filters.created_at.$lte = new Date(to_date as string);
    }

    // We'll filter by hotel_id and guest_name in memory after fetching the carts

    // Set up pagination and sorting
    const config: any = {
      relations: ["items", "shipping_address", "billing_address"],
      order: { created_at: "DESC" }, // Sort by created_at in descending order (latest first)
    };

    // Add pagination if provided
    if (limit) {
      config.take = parseInt(limit as string, 10);
    }

    if (offset) {
      config.skip = parseInt(offset as string, 10);
    }

    // Get total count for pagination
    const [_, totalCount] = await cartModuleService.listAndCountCarts(filters, {
      take: 1,
      order: { created_at: "DESC" }, // Use the same sorting for consistency
    });

    // Fetch carts with their items
    const allCarts = await cartModuleService.listCarts(filters, config);

    // Filter carts in memory
    const carts = allCarts.filter((cart) => {
      // Skip carts without metadata
      if (!cart.metadata) return false;
      if (typeof cart.metadata !== "object") return false;

      // Double-check that completed_at is null (should already be filtered in the database query)
      if (cart.completed_at) return false;

      // Additional check: Filter out carts that have been converted to orders
      if (cart.metadata.parent_order_id) return false;

      // Apply hotel_id filter if provided
      if (hotel_id) {
        const metadataHotelId = cart.metadata.hotel_id;
        if (
          !metadataHotelId ||
          metadataHotelId.toString() !== hotel_id.toString()
        ) {
          return false;
        }
      }

      // Apply guest_name filter if provided
      if (guest_name) {
        const metadataGuestName = cart.metadata.guest_name;
        if (!metadataGuestName || typeof metadataGuestName !== "string") {
          return false;
        }

        const searchTerm = guest_name.toString().toLowerCase();
        if (!metadataGuestName.toLowerCase().includes(searchTerm)) {
          return false;
        }
      }

      return true;
    });

    // We'll use the total count from the database for pagination
    const hotelService = req.scope.resolve("hotel");
    const hotels = await hotelService.listHotels();

    // Process carts to extract relevant information
    const processedCarts = carts.map((cart) => {
      // Extract data from line items metadata first (most accurate), then cart metadata as fallback
      const firstItem = cart.items && cart.items[0];
      const itemMetadata = firstItem?.metadata || {};
      const cartMetadata = cart.metadata || {};

      // Calculate number of rooms based on line items count
      const numberOfRooms = cart.items?.length || cartMetadata.number_of_rooms || itemMetadata.number_of_rooms || 1;

      // Extract hotel information - prioritize line item metadata
      const hotelId = itemMetadata.hotel_id || cartMetadata.hotel_id;
      const hotelName = itemMetadata.hotel_name ||
                       cartMetadata.hotel_name ||
                       hotels.find((m) => m.id === hotelId)?.name ||
                       "Unknown Hotel";

      // Extract guest information - prioritize line item metadata
      const guestName = itemMetadata.guest_name || cartMetadata.guest_name || "Guest";
      const guestEmail = itemMetadata.guest_email || cartMetadata.guest_email || "No email provided";

      // Extract room information - prioritize line item metadata
      const roomType = itemMetadata.room_config_name ||
                      itemMetadata.room_name ||
                      itemMetadata.room_type ||
                      cartMetadata.room_config_name ||
                      cartMetadata.room_type ||
                      "Standard Room";

      // Extract check-in and check-out dates - prioritize line item metadata
      const checkInDate = itemMetadata.check_in_date || cartMetadata.check_in_date;
      const checkOutDate = itemMetadata.check_out_date || cartMetadata.check_out_date;

      // Debug logging for troubleshooting
      console.log("🛒 Cart processing debug:", {
        cartId: cart.id,
        itemsCount: cart.items?.length || 0,
        itemMetadata: itemMetadata,
        cartMetadata: cartMetadata,
        extractedData: {
          hotelName,
          guestName,
          roomType,
          numberOfRooms,
          checkInDate,
          checkOutDate
        }
      });

      // Calculate total amount - prioritize line item metadata
      const totalAmount = itemMetadata.total_amount || cartMetadata.total_amount || 0;
      const currencyCode = itemMetadata.currency_code || cartMetadata.currency_code || "USD";

      return {
        id: cart.id,
        cart_id: cart.id,
        guest_name: guestName,
        guest_email: guestEmail,
        hotel_id: hotelId,
        hotel_name: hotelName,
        room_type: roomType,
        check_in_date: checkInDate,
        check_out_date: checkOutDate,
        number_of_rooms: numberOfRooms,
        total_amount: totalAmount,
        currency_code: currencyCode,
        created_at: cart.created_at,
        updated_at: cart.updated_at,
        metadata: cart.metadata || {},
        // Include line items for frontend debugging if needed
        line_items: cart.items?.map(item => ({
          id: item.id,
          title: item.title,
          quantity: item.quantity,
          unit_price: item.unit_price,
          metadata: item.metadata
        })) || []
      };
    });

    // Return the processed carts and count
    return res.status(200).json({
      carts: processedCarts,
      count: totalCount, // Use the total count from the database for pagination
    });
  } catch (error) {
    console.error("Error retrieving carts:", error);
    return res.status(500).json({ error: "Internal Server Error" });
  }
};
