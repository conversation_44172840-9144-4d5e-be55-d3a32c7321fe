import { DollarSign, Euro, PoundSterling } from "lucide-react";
import type { Currency } from "../hooks/use-admin-currencies";

/**
 * Get currency symbol for a given currency code
 * Note: This is a fallback function. Use Currency object from API when available.
 */
export function getCurrencySymbol(currencyCode: string): string {
  // This should only be used as a last resort fallback
  // Prefer using the Currency object from useAdminCurrencies hook
  return currencyCode.toUpperCase();
}

/**
 * Format currency for dropdown display
 */
export function formatCurrencyOption(currency: Currency): string {
  return `${currency.currency_code} (${currency.symbol})`;
}

/**
 * Get appropriate Lucide icon component for currency
 */
export function getCurrencyIcon(currencyCode: string) {
  const code = currencyCode.toUpperCase();
  
  switch (code) {
    case "USD":
    case "CAD":
    case "AUD":
    case "NZD":
    case "HKD":
    case "SGD":
    case "MXN":
      return DollarSign;
    case "EUR":
      return Euro;
    case "GBP":
    case "EGP":
      return PoundSterling;
    default:
      return DollarSign; // Default fallback
  }
}

/**
 * Format amount with currency symbol and proper decimal places
 * @param amount - Amount in smallest currency unit (e.g., cents for USD)
 * @param currency - Currency object with decimal_digits and symbol
 * @param options - Formatting options
 */
export function formatCurrencyAmount(
  amount: number,
  currency: Currency,
  options: {
    showSymbol?: boolean;
    showCode?: boolean;
    symbolPosition?: "before" | "after";
  } = {}
): string {
  const {
    showSymbol = true,
    showCode = false,
    symbolPosition = "before",
  } = options;

  // Convert from smallest unit to display format
  const displayAmount = amount / Math.pow(10, currency.decimal_digits);
  const formattedAmount = displayAmount.toFixed(currency.decimal_digits);

  let result = formattedAmount;

  if (showSymbol && symbolPosition === "before") {
    result = `${currency.symbol}${formattedAmount}`;
  } else if (showSymbol && symbolPosition === "after") {
    result = `${formattedAmount} ${currency.symbol}`;
  }

  if (showCode) {
    result = `${result} ${currency.currency_code}`;
  }

  return result;
}

/**
 * Parse amount from string, handling different currency formats
 * @param value - String value to parse (e.g., "$10.50")
 * @param currency - Currency object with symbol and decimal_digits
 * @returns Amount in smallest currency unit (e.g., 1050 cents for "$10.50")
 */
export function parseCurrencyAmount(value: string, currency: Currency): number {
  // Remove currency symbols and non-numeric characters except decimal point
  const cleanValue = value
    .replace(new RegExp(`[${currency.symbol}]`, "g"), "")
    .replace(/[^\d.-]/g, "");

  const parsed = parseFloat(cleanValue);
  if (isNaN(parsed)) return 0;

  // Convert to smallest currency unit
  return Math.round(parsed * Math.pow(10, currency.decimal_digits));
}

/**
 * Convert amount to smallest currency unit (e.g., cents for USD)
 */
export function toSmallestUnit(amount: number, currency: Currency): number {
  return Math.round(amount * Math.pow(10, currency.decimal_digits));
}

/**
 * Convert amount from smallest currency unit to standard unit
 */
export function fromSmallestUnit(amount: number, currency: Currency): number {
  return amount / Math.pow(10, currency.decimal_digits);
}

/**
 * Validate currency code
 */
export function isValidCurrencyCode(code: string): boolean {
  return /^[A-Z]{3}$/.test(code.toUpperCase());
}

/**
 * Get currency display name
 * Note: This is a fallback function. Use Currency object from API when available.
 */
export function getCurrencyName(currencyCode: string): string {
  // This should only be used as a last resort fallback
  // Prefer using the Currency object from useAdminCurrencies hook
  return currencyCode.toUpperCase();
}

/**
 * Format currency amount for display using Intl.NumberFormat
 * @param amount - Amount in smallest currency unit (e.g., cents)
 * @param currencyCode - Currency code (e.g., "USD")
 * @param locale - Locale for formatting (defaults to "en-US")
 * @returns Formatted currency string
 */
export function formatCurrencyForDisplay(
  amount: number,
  currencyCode: string,
  locale: string = "en-US"
): string {
  // Get decimal digits for the currency (default to 2)
  const decimalDigits = getCurrencyDecimalDigits(currencyCode);

  // Convert from smallest unit to display format
  const displayAmount = amount / Math.pow(10, decimalDigits);

  return new Intl.NumberFormat(locale, {
    style: "currency",
    currency: currencyCode.toUpperCase(),
  }).format(displayAmount);
}

/**
 * Get decimal digits for a currency code
 * @param currencyCode - Currency code (e.g., "USD")
 * @returns Number of decimal digits for the currency
 */
function getCurrencyDecimalDigits(currencyCode: string): number {
  const code = currencyCode.toUpperCase();

  // Currencies with 0 decimal places
  const zeroDecimalCurrencies = [
    "BIF", "CLP", "DJF", "GNF", "JPY", "KMF", "KRW",
    "MGA", "PYG", "RWF", "UGX", "VND", "VUV", "XAF", "XOF", "XPF"
  ];

  // Currencies with 3 decimal places
  const threeDecimalCurrencies = ["BHD", "IQD", "JOD", "KWD", "OMR", "TND"];

  if (zeroDecimalCurrencies.includes(code)) return 0;
  if (threeDecimalCurrencies.includes(code)) return 3;

  // Default to 2 decimal places for most currencies
  return 2;
}
