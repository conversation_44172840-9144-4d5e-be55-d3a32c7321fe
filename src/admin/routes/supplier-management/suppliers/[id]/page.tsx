import { defineRouteConfig } from "@camped-ai/admin-sdk";
import {
  Edit,
  ArrowLeft as ArrowLeftLucide,
  Trash2,
  Phone,
  Mail,
  Copy,
} from "lucide-react";
import {
  Container,
  Heading,
  Text,
  Button,
  Badge,
  Toaster,
  toast,
  IconButton,
  Prompt,
} from "@camped-ai/ui";
import { useState } from "react";
import { useNavigate, useParams } from "react-router-dom";
import PermissionBasedSidebarHider from "../../../../widgets/permission-based-sidebar-hider";
import {
  useSupplier,
  useDeleteSupplier,
} from "../../../../hooks/vendor-management/use-suppliers";
import { useCategories } from "../../../../hooks/supplier-products-services/use-categories";
import { useRbac } from "../../../../hooks/use-rbac";
import { TwoColumnPage } from "../../../../../components/layout/pages/two-column-page/two-column-page";
import { SectionRow } from "../../../../../components/common/section/section-row";

import { DocumentUpload } from "../../../../components/vendor-management";
import {
  REGIONS,
  PAYMENT_METHODS,
  LANGUAGES,
} from "../../../../constants/supplier-form-options";
import {
  findOptionLabelWithFallback,
  getPayoutTermsLabel,
} from "../../../../utils/form-helpers";

const SupplierDetailPage = () => {
  const { id } = useParams();
  const navigate = useNavigate();
  const { hasPermission } = useRbac();

  const [deleteConfirmOpen, setDeleteConfirmOpen] = useState(false);

  // Copy to clipboard functionality
  const copyToClipboard = async (text: string, type: string) => {
    try {
      await navigator.clipboard.writeText(text);
      toast.success("Copied", {
        description: `${type} copied to clipboard`,
      });
    } catch (error) {
      console.error("Failed to copy:", error);
      toast.error("Failed to copy", {
        description: "Unable to copy to clipboard",
      });
    }
  };

  // Use the supplier management hooks
  const { data: supplierData, isLoading } = useSupplier(id!);
  const { data: categoriesData } = useCategories({ is_active: true });
  const deleteSupplier = useDeleteSupplier();
  const supplier: any = supplierData?.supplier;

  // Helper function to get category names from IDs
  const getCategoryNames = (categoryIds: string[]): string[] => {
    if (!categoriesData?.categories || !Array.isArray(categoryIds)) return [];
    return categoryIds
      .map((id) => {
        const category = categoriesData.categories.find((cat) => cat.id === id);
        return category ? category.name : null;
      })
      .filter((name): name is string => name !== null);
  };

  const handleDeleteSupplier = () => {
    if (!supplier) return;
    setDeleteConfirmOpen(true);
  };

  const confirmDeleteSupplier = () => {
    if (!supplier) return;

    deleteSupplier.mutate(supplier.id, {
      onSuccess: () => {
        setDeleteConfirmOpen(false);
        // Navigate back to suppliers list
        navigate("/supplier-management/suppliers");
      },
    });
  };

  if (isLoading) {
    return (
      <>
        <PermissionBasedSidebarHider />
        <div className="h-screen flex items-center justify-center bg-background">
          <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-primary"></div>
        </div>
      </>
    );
  }

  if (!supplier) {
    return (
      <>
        <PermissionBasedSidebarHider />
        <Container className="py-8">
          <div className="text-center py-12 bg-muted rounded-lg">
            <Text className="text-muted-foreground mb-4">
              Supplier not found
            </Text>
            <Button
              variant="primary"
              size="small"
              onClick={() => navigate("/supplier-management/suppliers")}
            >
              Back to Suppliers
            </Button>
          </div>
        </Container>
      </>
    );
  }

  // Main information sections
  const BasicInformationSection = () => (
    <Container className="divide-y p-0">
      <div className="flex items-center justify-between px-6 py-4">
        <div>
          <Heading level="h2">Basic Information</Heading>
          <Text className="text-ui-fg-subtle">
            Core supplier details and identification
          </Text>
        </div>
      </div>

      <SectionRow
        title="Supplier Type"
        value={supplier.supplier_type === "Company" ? "Company" : "Individual"}
      />

      <SectionRow
        title="Categories"
        value={
          supplier.categories && supplier.categories.length > 0
            ? (
                <div className="flex flex-wrap gap-2">
                  {getCategoryNames(supplier.categories).map((categoryName, idx) => (
                    <Badge key={idx} color="blue" className="rounded-full text-xs px-2 py-1">
                      {categoryName}
                    </Badge>
                  ))}
                </div>
              )
            : "—"
        }
      />
    </Container>
  );

  const BusinessInformationSection = () => (
    <Container className="divide-y p-0">
      <div className="flex items-center justify-between px-6 py-4">
        <div>
          <Heading level="h2">Business & Region</Heading>
          <Text className="text-ui-fg-subtle">
            Regional, operational, and financial details
          </Text>
        </div>
      </div>
      
      <SectionRow
        title="Timezone"
        value={supplier.timezone || "—"}
      />
      <SectionRow
        title="Language Preference"
        value={
          supplier.language_preference && supplier.language_preference.length > 0 ? (
            <div className="flex flex-wrap gap-2">
              {supplier.language_preference.map((langCode: string) => {
                const language = LANGUAGES.find(
                  (lang) =>
                    lang.value.toLowerCase() === langCode.toLowerCase() ||
                    lang.label.toLowerCase() === langCode.toLowerCase()
                );
                const displayName = language ? language.label : langCode;

                return (
                  <Badge
                    key={langCode}
                    color="blue"
                    className="rounded-full text-xs px-2 py-1"
                  >
                    {displayName}
                  </Badge>
                );
              })}
            </div>
          ) : (
            "—"
          )
        }
      />
    </Container>
  );

  const FinancialInformationSection = () => (
    <Container className="divide-y p-0">
      <div className="flex items-center justify-between px-6 py-4">
        <div>
          <Heading level="h2">Financial Information</Heading>
          <Text className="text-ui-fg-subtle">
            Payment and financial details
          </Text>
        </div>
      </div>

      <SectionRow
        title="Default Currency"
        value={supplier.default_currency || "—"}
      />
      <SectionRow
        title="Payment Method"
        value={
          findOptionLabelWithFallback(
            PAYMENT_METHODS,
            supplier.payment_method || ""
          )
        }
      />
      <SectionRow
        title="Payout Terms"
        value={
          getPayoutTermsLabel(
            supplier.payout_terms || ""
          )
        }
      />
      <SectionRow
        title="Tax ID"
        value={
          supplier.tax_id ? (
            <div className="flex items-center gap-2">
              <Text>{supplier.tax_id}</Text>
              <Copy
                className="h-4 w-4 text-gray-400 cursor-pointer hover:text-gray-600"
                onClick={() =>
                  copyToClipboard(supplier.tax_id!, "Tax ID")
                }
              />
            </div>
          ) : (
            "—"
          )
        }
      />
      {supplier.bank_account_details && (
        <SectionRow
          title="Bank Account Details"
          value={
            <div className="flex items-center gap-2">
              <Text className="text-sm">
                {supplier.bank_account_details}
              </Text>
              <Copy
                className="h-4 w-4 text-gray-400 cursor-pointer hover:text-gray-600"
                onClick={() =>
                  copyToClipboard(
                    supplier.bank_account_details!,
                    "Account details"
                  )
                }
              />
            </div>
          }
        />
      )}
    </Container>
  );

  // Sidebar sections
  const CategoriesSection = () => {
    if (!supplier.categories || supplier.categories.length === 0) {
      return null;
    }

    return (
      <Container className="divide-y p-0">
        <div className="flex items-center justify-between px-6 py-4">
          <Heading level="h2">Categories</Heading>
        </div>
        <div className="px-6 py-4">
          <div className="flex flex-wrap gap-2">
            {(() => {
              const categoryNames = getCategoryNames(supplier.categories || []);
              return categoryNames.map((categoryName, index) => (
                <Badge
                  key={index}
                  color="blue"
                  className="rounded-full text-sm px-3 py-1.5 font-medium"
                >
                  {categoryName}
                </Badge>
              ));
            })()}
            {getCategoryNames(supplier.categories || []).length === 0 && (
              <Text className="text-gray-400 text-sm">No categories assigned</Text>
            )}
          </div>
        </div>
      </Container>
    );
  };

  const ContactsSection = () => (
    <Container className="divide-y p-0">
      <div className="flex items-center justify-between px-6 py-4">
        <Heading level="h2">Contacts</Heading>
      </div>
      <div className="px-6 py-4">
        <div className="space-y-4">
          {supplier.contacts && supplier.contacts.length > 0 ? (
            supplier.contacts.map((contact: any, index: number) => (
              <div key={contact.id || index} className="space-y-2">
                <div className="flex items-center gap-2">
                  <Text className="font-medium">{contact.name}</Text>
                  {contact.is_primary && (
                    <Badge color="blue" className="text-xs">
                      Primary
                    </Badge>
                  )}
                </div>
                <div className="space-y-1">
                  <div className="flex items-center gap-2">
                    <Mail className="h-4 w-4 text-gray-400" />
                    <Text className="text-sm">{contact.email}</Text>
                    <Copy
                      className="h-4 w-4 text-gray-400 cursor-pointer hover:text-gray-600"
                      onClick={() => copyToClipboard(contact.email, "Email")}
                    />
                  </div>
                  {contact.phone_number && (
                    <div className="flex items-center gap-2">
                      <Phone className="h-4 w-4 text-gray-400" />
                      <Text className="text-sm">{contact.phone_number}</Text>
                      <Copy
                        className="h-4 w-4 text-gray-400 cursor-pointer hover:text-gray-600"
                        onClick={() =>
                          copyToClipboard(contact.phone_number, "Phone number")
                        }
                      />
                    </div>
                  )}
                </div>
              </div>
            ))
          ) : (
            <Text className="text-muted-foreground">
              No contact information available
            </Text>
          )}
        </div>
      </div>
    </Container>
  );

  const DocumentsSection = () => (
    <Container className="divide-y p-0">
      <div className="flex items-center justify-between px-6 py-4">
        <Heading level="h2">Documents</Heading>
      </div>
      <div className="px-6 py-4">
        <DocumentUpload
          supplierId={id!}
          readOnly={!hasPermission("supplier_management:edit")}
        />
      </div>
    </Container>
  );

  return (
    <>
      <PermissionBasedSidebarHider />
      <Toaster />

      {/* Header with navigation and actions */}
      <Container className="flex items-center justify-between mb-2">
        <div className="flex items-center gap-4">
          <IconButton onClick={() => navigate("/supplier-management/suppliers")}>
            <ArrowLeftLucide className="w-4 h-4" />
          </IconButton>
          <div className="flex-1">
            <Heading level="h1" >{supplier.name}</Heading>
          </div>
        </div>

        <div className="flex items-center gap-2">
          <Badge color={supplier.status === "Active" ? "green" : "red"}>
            {supplier.status}
          </Badge>

          {hasPermission("supplier_management:edit") && (
            <Button
              variant="secondary"
              size="small"
              onClick={() =>
                navigate(`/supplier-management/suppliers/${supplier.id}/edit`)
              }
            >
              <Edit className="w-4 h-4 mr-1" />
              Edit
            </Button>
          )}
          {hasPermission("supplier_management:delete") && (
            <Button
              variant="secondary"
              size="small"
              onClick={handleDeleteSupplier}
              className="text-red-600"
            >
              <Trash2 className="w-4 h-4 mr-2" />
              Delete
            </Button>
          )}
        </div>
      </Container>

      <TwoColumnPage
        widgets={{
          before: [],
          after: [],
          sideBefore: [],
          sideAfter: [],
        }}
        data={supplier}
        showJSON={false}
        showMetadata={false}
        hasOutlet={false}
      >
        <TwoColumnPage.Main>
          <BasicInformationSection />
          <BusinessInformationSection />
          <FinancialInformationSection />
        </TwoColumnPage.Main>

        <TwoColumnPage.Sidebar>
          <ContactsSection />
          <DocumentsSection />
        </TwoColumnPage.Sidebar>
      </TwoColumnPage>

      {/* Delete Confirmation Prompt */}
      <Prompt open={deleteConfirmOpen} onOpenChange={setDeleteConfirmOpen}>
        <Prompt.Content>
          <Prompt.Header>
            <Prompt.Title>Delete Supplier</Prompt.Title>
            <Prompt.Description>
              Are you sure you want to delete "{supplier?.name}"? This action
              cannot be undone.
            </Prompt.Description>
          </Prompt.Header>
          <Prompt.Footer>
            <Prompt.Cancel onClick={() => setDeleteConfirmOpen(false)}>
              Cancel
            </Prompt.Cancel>
            <Prompt.Action
              onClick={confirmDeleteSupplier}
              disabled={deleteSupplier.isPending}
            >
              {deleteSupplier.isPending ? "Deleting..." : "Delete"}
            </Prompt.Action>
          </Prompt.Footer>
        </Prompt.Content>
      </Prompt>
    </>
  );
};

export const config = defineRouteConfig({
  label: "Supplier Details",
});

export default SupplierDetailPage;
