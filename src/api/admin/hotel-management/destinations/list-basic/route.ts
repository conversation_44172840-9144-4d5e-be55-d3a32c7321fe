import { AuthenticatedMedusaRequest, MedusaResponse } from "@camped-ai/framework/http";
import { ContainerRegistrationKeys, Modules } from "@camped-ai/framework/utils";
import { RBAC_MODULE } from "../../../../../modules/rbac";
import RbacModuleService from "../../../../../modules/rbac/service";
import { UserRole } from "../../../../../modules/rbac/types";

/**
 * GET /admin/hotel-management/destinations/list-basic
 * Get basic destination information for dropdowns and selection lists
 * Returns only essential fields: id, name, country
 */
export const GET = async (
  req: AuthenticatedMedusaRequest,
  res: MedusaResponse
) => {
  try {
    const query = req.scope.resolve(ContainerRegistrationKeys.QUERY);
    const rbacService: RbacModuleService = req.scope.resolve(RBAC_MODULE);
    const userService = req.scope.resolve(Modules.USER);

    const { limit = 100, offset = 0 } = req.query || {};

    // Get current user with RBAC data
    let userWithRole = null;
    if (req.auth_context?.actor_id) {
      try {
        userWithRole = await userService.retrieveUser(req.auth_context.actor_id);
      } catch (error) {
        console.warn("Could not load user RBAC data:", error);
      }
    }

    // Get basic destination data
    const {
      data: allDestinations,
      metadata: { count: totalCount },
    } = await query.graph({
      entity: "destination",
      fields: [
        "id",
        "name",
        "country",
        "is_active"
      ],
      filters: {
        is_active: true, // Only show active destinations
      },
      pagination: {
        skip: Number(offset),
        take: Number(limit),
      },
    });

    // Filter destinations based on user permissions
    let filteredDestinations = allDestinations;
    let filteredCount = totalCount;

    if (userWithRole) {
      const rbacData = userWithRole.metadata?.rbac as any;
      
      // All users with valid roles can see all destinations
      // Access control is now permission-based, not destination-based
      if (rbacData?.role !== UserRole.ADMIN) {
        // Non-admin users see all destinations but with permission-based operations
        filteredDestinations = allDestinations;
        filteredCount = allDestinations.length;
      }
    }

    // Return minimal data structure
    const basicDestinations = filteredDestinations.map((destination: any) => ({
      id: destination.id,
      name: destination.name,
      country: destination.country,
    }));

    res.json({
      destinations: basicDestinations,
      count: filteredCount,
      limit: Number(limit),
      offset: Number(offset),
    });
  } catch (error) {
    console.error("Error in basic destination listing:", error);
    res.status(500).json({
      type: "server_error",
      message: "Failed to list destinations",
    });
  }
};
