import { useState } from "react";
import {
  Button,
  FocusModal,
  Text,
  Heading,
  Toaster,
  toast,
} from "@camped-ai/ui";
import { FileIcon, DownloadIcon, UploadIcon, CheckCircleIcon, XCircleIcon } from "lucide-react";
import { useNavigate } from "react-router-dom";
import * as XLSX from 'xlsx';
import { useBulkImportHotels } from "../../hooks/hotel-management/use-hotel-management";

type BulkImportModalProps = {
  open: boolean;
  onClose: () => void;
};

// Validation error type
type ValidationError = {
  row: number;
  field: string;
  message: string;
  value?: any;
};

const BulkImportModal = ({ open, onClose }: BulkImportModalProps) => {
  const [file, setFile] = useState<File | null>(null);
  const [uploadResult, setUploadResult] = useState<any>(null);
  const [filePreview, setFilePreview] = useState<any>(null);
  const [previewData, setPreviewData] = useState<{headers: string[], rows: any[]}>({headers: [], rows: []});
  const [previewError, setPreviewError] = useState<string>("");
  const [validationErrors, setValidationErrors] = useState<ValidationError[]>([]);
  const [importSuccess, setImportSuccess] = useState(false);
  const [fileUploaded, setFileUploaded] = useState(false);
  const navigate = useNavigate();

  // Use the bulk import mutation
  const bulkImportMutation = useBulkImportHotels();
  const isUploading = bulkImportMutation.isPending;

  // Function to truncate long text for preview
  const truncateText = (text: string, maxLength: number = 50): string => {
    if (!text || text.length <= maxLength) return text;
    return text.substring(0, maxLength) + '...';
  };

  // Function to determine if a field should be truncated based on header name
  const shouldTruncateField = (headerName: string): boolean => {
    const truncateFields = ['description', 'notes', 'amenities', 'rules', 'safety_measures', 'tags'];
    return truncateFields.some(field => headerName.toLowerCase().includes(field));
  };

  // Validation function
  const validateImportData = (headers: string[], rows: any[]): ValidationError[] => {
    const errors: ValidationError[] = [];
    const requiredFields = ['name', 'destination'];

    // Check for required headers
    const missingHeaders = requiredFields.filter(field =>
      !headers.some(header => header.toLowerCase().includes(field.toLowerCase()))
    );

    if (missingHeaders.length > 0) {
      errors.push({
        row: 0,
        field: 'headers',
        message: `Missing required columns: ${missingHeaders.join(', ')}`,
      });
    }

    // Find column indices
    const nameIndex = headers.findIndex(h => h.toLowerCase().includes('name'));
    const destinationIndex = headers.findIndex(h => h.toLowerCase().includes('destination'));
    const ratingIndex = headers.findIndex(h => h.toLowerCase().includes('rating'));

    // Validate each row
    rows.forEach((row, index) => {
      const rowNumber = index + 2; // +2 because Excel rows start at 1 and we skip header

      // Check required fields
      if (nameIndex >= 0 && (!row[nameIndex] || String(row[nameIndex]).trim() === '')) {
        errors.push({
          row: rowNumber,
          field: 'name',
          message: 'Name is required',
          value: row[nameIndex]
        });
      }

      if (destinationIndex >= 0 && (!row[destinationIndex] || String(row[destinationIndex]).trim() === '')) {
        errors.push({
          row: rowNumber,
          field: 'destination',
          message: 'Destination is required',
          value: row[destinationIndex]
        });
      }

      // Validate rating field (optional, but if provided must be between 1-5)
      if (ratingIndex >= 0 && row[ratingIndex] !== null && row[ratingIndex] !== undefined && String(row[ratingIndex]).trim() !== '') {
        const ratingValue = parseFloat(String(row[ratingIndex]));
        if (isNaN(ratingValue) || ratingValue < 1 || ratingValue > 5) {
          errors.push({
            row: rowNumber,
            field: 'rating',
            message: 'Rating must be between 1 and 5',
            value: row[ratingIndex]
          });
        }
      }
    });

    return errors;
  };

  const handleDownloadTemplate = async () => {
    try {
      // Create a temporary link element to trigger download without opening new tab
      const link = document.createElement('a');
      link.href = "/admin/hotels/template";
      link.download = "hotel-import-template.xlsx";
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
    } catch (error) {
      console.error("Error downloading template:", error);
      toast.error("Error", {
        description: "Failed to download template",
      });
    }
  };

  // Function to parse Excel/CSV files
  const parseExcelFile = (file: File) => {
    const reader = new FileReader();
    reader.onload = (e) => {
      try {
        const data = e.target?.result;
        if (!data) {
          setPreviewError("Could not read file data");
          return;
        }

        // Parse the file using XLSX
        const workbook = XLSX.read(data, { type: 'array' });
        const firstSheetName = workbook.SheetNames[0];
        const worksheet = workbook.Sheets[firstSheetName];

        // Convert to JSON
        const jsonData = XLSX.utils.sheet_to_json(worksheet, { header: 1 });

        if (jsonData.length === 0) {
          setPreviewError("No data found in file");
          return;
        }

        // Extract headers (first row)
        const headers = jsonData[0] as string[];

        // Extract rows (limit to 5 for preview)
        const rows = jsonData.slice(1, 6) as any[];

        // Validate the data
        const errors = validateImportData(headers, jsonData.slice(1) as any[]);
        setValidationErrors(errors);

        setPreviewData({ headers, rows });
        setFilePreview({
          name: file.name,
          size: (file.size / 1024).toFixed(2) + ' KB',
          type: file.type,
          previewAvailable: true
        });

        // Mark file as successfully uploaded and processed
        setFileUploaded(true);
      } catch (error) {
        console.error('Error parsing file:', error);
        setPreviewError("Could not parse file. Make sure it's a valid Excel or CSV file.");
        setFilePreview({
          name: file.name,
          size: (file.size / 1024).toFixed(2) + ' KB',
          type: file.type,
          previewAvailable: false,
          error: 'Could not generate preview'
        });
      }
    };
    reader.onerror = () => {
      setPreviewError("Error reading file");
    };
    reader.readAsArrayBuffer(file);
  };

  const handleFileChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    if (e.target.files && e.target.files.length > 0) {
      const selectedFile = e.target.files[0];
      setFile(selectedFile);
      setFilePreview(null); // Reset preview when file changes
      setPreviewData({headers: [], rows: []});
      setPreviewError("");

      // Generate preview for Excel/CSV files
      if (selectedFile.name.endsWith('.xlsx') || selectedFile.name.endsWith('.xls') || selectedFile.name.endsWith('.csv')) {
        parseExcelFile(selectedFile);
      }
    }
  };

  const handleUpload = async () => {
    if (!file) {
      toast.error("Error", {
        description: "Please select a file to upload",
      });
      return;
    }

    // Use the mutation to import hotels
    bulkImportMutation.mutate(file, {
      onSuccess: (result) => {
        // Process the result
        setUploadResult(result);

        if (result.results.successful > 0) {
          toast.success("Success", {
            description: `${result.results.successful} hotels imported successfully`,
          });

          // Set import success state
          setImportSuccess(true);
        } else {
          // No successful imports
          setImportSuccess(false);
        }

        if (result.results.failed > 0) {
          toast.error("Warning", {
            description: `${result.results.failed} hotels failed to import`,
          });
        }
      },
      onError: (error: any) => {
        console.error("Error uploading file:", error);
        toast.error("Error", {
          description: error.message || "Failed to upload file",
        });
        setImportSuccess(false);
      }
    });
  };

  const resetModalState = () => {
    setUploadResult(null);
    setFile(null);
    setFilePreview(null);
    setPreviewData({headers: [], rows: []});
    setPreviewError("");
    setValidationErrors([]);
    setImportSuccess(false);
    setFileUploaded(false);
  };

  const resetFileSelection = () => {
    setFile(null);
    setFilePreview(null);
    setPreviewData({headers: [], rows: []});
    setPreviewError("");
    setValidationErrors([]);
    setFileUploaded(false);
    // Clear the file input
    const fileInput = document.querySelector('input[type="file"]') as HTMLInputElement;
    if (fileInput) {
      fileInput.value = '';
    }
  };

  const handleViewHotels = () => {
    resetModalState();
    // Use a direct close with refresh flag
    if (open) {
      const event = new CustomEvent('closeHotelModal', { detail: { refresh: true } });
      window.dispatchEvent(event);
    }
    navigate("/hotel-management/hotels");
  };

  return (
    <FocusModal
      open={open}
      onOpenChange={(isOpen) => {
        if (!isOpen) {
          // Check if we had a successful import
          const hadSuccessfulImport = localStorage.getItem('hotelImportSuccess') === 'true';

          resetModalState();

          // If we had a successful import, emit a custom event to refresh the data
          if (hadSuccessfulImport) {
            const event = new CustomEvent('closeHotelModal', {
              detail: { refresh: true }
            });
            window.dispatchEvent(event);

            // Clear the flag after using it
            localStorage.removeItem('hotelImportSuccess');
          } else {
            // Just close without refreshing
            onClose();
          }
        }
      }}
    >
      <FocusModal.Content className="flex flex-col h-full">
        <FocusModal.Header className="flex-shrink-0">
          <div className="flex justify-between w-full items-center py-4">
            <Heading level="h2" className="text-xl font-semibold">
              Import Hotels
            </Heading>
             {/* Progress Indicator */}
          {!uploadResult && (
            <div className="px-6">
              <div className="flex items-center justify-between">
                <div className="flex items-center space-x-4">
                  {/* Step 1 */}
                  <div className="flex items-center">
                    <div className="w-8 h-8 bg-blue-600 text-white rounded-full flex items-center justify-center text-sm font-semibold">
                      1
                    </div>
                    <span className="ml-2 text-sm font-medium text-blue-600">Download</span>
                  </div>
                  <div className="w-8 h-0.5 bg-gray-300"></div>

                  {/* Step 2 */}
                  <div className="flex items-center">
                    <div className={`w-8 h-8 rounded-full flex items-center justify-center text-sm font-semibold ${
                      file ? 'bg-green-600 text-white' : 'bg-gray-300 text-gray-600'
                    }`}>
                      2
                    </div>
                    <span className={`ml-2 text-sm font-medium ${
                      file ? 'text-green-600' : 'text-gray-500'
                    }`}>Upload</span>
                  </div>
                  <div className={`w-8 h-0.5 ${file ? 'bg-green-300' : 'bg-gray-300'}`}></div>

                  {/* Step 3 */}
                  <div className="flex items-center">
                    <div className={`w-8 h-8 rounded-full flex items-center justify-center text-sm font-semibold ${
                      filePreview ? 'bg-purple-600 text-white' : 'bg-gray-300 text-gray-600'
                    }`}>
                      3
                    </div>
                    <span className={`ml-2 text-sm font-medium ${
                      filePreview ? 'text-purple-600' : 'text-gray-500'
                    }`}>Review</span>
                  </div>
                  <div className={`w-8 h-0.5 ${filePreview ? 'bg-purple-300' : 'bg-gray-300'}`}></div>

                  {/* Step 4 */}
                  <div className="flex items-center">
                    <div className={`w-8 h-8 rounded-full flex items-center justify-center text-sm font-semibold ${
                      file && filePreview ? 'bg-orange-600 text-white' : 'bg-gray-300 text-gray-600'
                    }`}>
                      4
                    </div>
                    <span className={`ml-2 text-sm font-medium ${
                      file && filePreview ? 'text-orange-600' : 'text-gray-500'
                    }`}>Import</span>
                  </div>
                </div>
              </div>
            </div>
          )}
          </div>

         
        </FocusModal.Header>

        <FocusModal.Body className="flex flex-col flex-grow overflow-hidden">
          <div className="flex-grow overflow-y-auto p-6 space-y-6">
            <Toaster />

            {!uploadResult ? (
              <>
                <div className="bg-gradient-to-r from-blue-50 to-indigo-50 p-6 rounded-lg border border-blue-200 shadow-sm">
                  <div className="flex flex-col gap-4">
                    <div className="flex items-start gap-3">
                      <div className="flex-shrink-0 w-8 h-8 bg-blue-100 rounded-full flex items-center justify-center">
                        <span className="text-blue-600 font-semibold text-sm">1</span>
                      </div>
                      <div className="flex-1">
                        <Heading level="h3" className="text-lg font-medium text-gray-900 mb-2">
                          Download Import Template
                        </Heading>
                        <Text className="text-gray-600 mb-3">
                          Get the Excel template with pre-configured fields for hotel data.
                          The template includes sample data and validation rules to help you format your data correctly.
                        </Text>
                        <div className="bg-white p-3 rounded-md border border-blue-200 mb-3">
                          <Text className="text-sm font-medium text-gray-700 mb-1">Template includes:</Text>
                          <ul className="text-sm text-gray-600 space-y-1">
                            <li>• <strong>Hotel Details:</strong> Name, Destination, Description, Featured status (Handle auto-generated from name)</li>
                            <li>• <strong>Contact Info:</strong> Address, Phone, Email, Website</li>
                            <li>• <strong>Features:</strong> Rating, Amenities, Rules, Safety Measures, Pets Allowed</li>
                            <li>• <strong>Destinations Reference:</strong> Valid destination names for lookup</li>
                          </ul>
                        </div>
                        <Button
                          variant="secondary"
                          onClick={handleDownloadTemplate}
                          className="flex items-center gap-2 bg-blue-600 text-white hover:bg-blue-700"
                        >
                          <DownloadIcon className="w-4 h-4" />
                          <span>Download Excel Template</span>
                        </Button>
                      </div>
                    </div>
                  </div>
                </div>

                <div className="bg-white p-6 rounded-lg border border-gray-200 shadow-sm">
                  <div className="flex flex-col gap-4">
                    <div className="flex items-start gap-3">
                      <div className="flex-shrink-0 w-8 h-8 bg-green-100 rounded-full flex items-center justify-center">
                        <span className="text-green-600 font-semibold text-sm">2</span>
                      </div>
                      <div className="flex-1">
                        <Heading level="h3" className="text-lg font-medium text-gray-900 mb-2">
                          Upload Your Data File
                        </Heading>
                        <Text className="text-gray-600 mb-4">
                          Upload the completed Excel file with your hotel data.
                        </Text>
                      </div>
                    </div>

                    {!fileUploaded ? (
                      <div className="border-2 border-dashed border-gray-300 rounded-lg p-6 text-center hover:border-gray-400 transition-colors">
                        <input
                          type="file"
                          accept=".xlsx,.xls,.csv"
                          onChange={handleFileChange}
                          className="hidden"
                          id="file-upload"
                        />
                        <div className="flex flex-col items-center gap-4">
                          <div>
                            <Text className="text-lg font-medium text-gray-700 mb-2">
                              Upload your hotel data file
                            </Text>
                            <Text className="text-sm text-gray-500 mb-4">
                              Excel (.xlsx, .xls) and CSV files • Maximum 5MB
                            </Text>
                          </div>
                          <Button
                            variant="secondary"
                            onClick={() => document.getElementById('file-upload')?.click()}
                            className="flex items-center gap-2"
                          >
                            <UploadIcon className="w-4 h-4" />
                            Choose File
                          </Button>
                        </div>
                      </div>
                    ) : (
                      <div className="bg-blue-50 p-6 rounded-lg border border-blue-200">
                        <div className="flex items-start justify-between">
                          <div className="flex items-center gap-3">
                            <div className="flex-shrink-0 w-8 h-8 bg-blue-100 rounded-full flex items-center justify-center">
                              <CheckCircleIcon className="w-5 h-5 text-blue-600" />
                            </div>
                            <div>
                              <Text className="text-blue-800 font-medium mb-1">
                                File Uploaded Successfully
                              </Text>
                              <Text className="text-blue-700 text-sm">
                                Your file has been processed and is ready for import
                              </Text>
                            </div>
                          </div>
                          <Button
                            variant="secondary"
                            size="small"
                            onClick={resetFileSelection}
                            className="flex items-center gap-2 text-blue-700 border-blue-300 hover:bg-blue-100"
                          >
                            <UploadIcon className="w-4 h-4" />
                            Choose Different File
                          </Button>
                        </div>
                      </div>
                    )}

                    {fileUploaded && file && (
                      <div className="mt-4">
                        <div className="bg-gray-50 p-4 rounded-lg border border-gray-200">
                          <div className="flex items-center gap-2">
                            <FileIcon className="w-4 h-4 text-gray-600" />
                            <Text className="font-medium text-gray-700">{file.name}</Text>
                            <Text className="text-gray-500 text-sm ml-2">
                              ({(file.size / 1024).toFixed(2)} KB)
                            </Text>
                          </div>
                        </div>
                      </div>
                    )}

                    {fileUploaded && filePreview && (
                      <div className="mt-6">
                        <div className="bg-white p-6 rounded-lg border border-gray-200 shadow-sm">
                          <div className="flex items-start gap-3 mb-4">
                            <div className="flex-shrink-0 w-8 h-8 bg-purple-100 rounded-full flex items-center justify-center">
                              <span className="text-purple-600 font-semibold text-sm">3</span>
                            </div>
                            <div className="flex-1">
                              <Heading level="h3" className="text-lg font-medium text-gray-900 mb-2">
                                Review Your Data
                              </Heading>
                              <Text className="text-gray-600 mb-4">
                                Preview your hotel data before importing to ensure everything looks correct.
                              </Text>
                            </div>
                          </div>

                          {filePreview.previewAvailable ? (
                            <div>
                              {/* Validation Results */}
                              {validationErrors.length > 0 ? (
                                <div className="bg-red-50 p-4 rounded-lg border border-red-200 mb-4">
                                  <div className="flex items-start gap-3">
                                    <div className="flex-shrink-0 w-6 h-6 bg-red-100 rounded-full flex items-center justify-center">
                                      <XCircleIcon className="w-4 h-4 text-red-600" />
                                    </div>
                                    <div className="flex-1">
                                      <Text className="text-red-800 font-medium mb-2">
                                        Validation Errors Found ({validationErrors.length})
                                      </Text>
                                      <Text className="text-red-700 text-sm mb-3">
                                        Please fix these issues before importing:
                                      </Text>
                                      <div className="space-y-2 max-h-32 overflow-y-auto">
                                        {validationErrors.slice(0, 5).map((error, index) => (
                                          <div key={index} className="bg-white p-2 rounded border border-red-200">
                                            <Text className="text-sm font-medium text-red-800">
                                              {error.row === 0 ? 'Header Issue' : `Row ${error.row}`}: {error.field}
                                            </Text>
                                            <Text className="text-xs text-red-600">{error.message}</Text>
                                          </div>
                                        ))}
                                        {validationErrors.length > 5 && (
                                          <Text className="text-xs text-red-600">
                                            ... and {validationErrors.length - 5} more errors
                                          </Text>
                                        )}
                                      </div>
                                    </div>
                                  </div>
                                </div>
                              ) : (
                                <div className="bg-green-50 p-4 rounded-lg border border-green-200 mb-4">
                                  <div className="flex items-start gap-3">
                                    <div className="flex-shrink-0 w-6 h-6 bg-green-100 rounded-full flex items-center justify-center">
                                      <CheckCircleIcon className="w-4 h-4 text-green-600" />
                                    </div>
                                    <div className="flex-1">
                                      <Text className="text-green-800 font-medium mb-1">Data Validation Passed</Text>
                                      <Text className="text-green-700 text-sm">
                                        ✓ Required columns: name, destination<br/>
                                        ✓ Hotel features: rating, amenities, rules, safety measures<br/>
                                        ✓ File format and size are valid
                                      </Text>
                                    </div>
                                  </div>
                                </div>
                              )}

                              {previewData.headers.length > 0 ? (
                                <div className="mb-6">
                                  <div className="flex items-center gap-2 mb-3">
                                    <div className="w-2 h-2 bg-blue-500 rounded-full"></div>
                                    <Text className="font-semibold text-gray-800">Hotels Data Preview</Text>
                                    <span className="bg-blue-100 text-blue-800 text-xs font-medium px-2 py-1 rounded-full">
                                      {previewData.rows.length} rows
                                    </span>
                                  </div>
                                  <div className="border border-gray-200 rounded-lg overflow-hidden shadow-sm">
                                    <div className="overflow-x-auto">
                                      <table className="min-w-full divide-y divide-gray-200">
                                        <thead className="bg-gradient-to-r from-blue-50 to-indigo-50">
                                          <tr>
                                            {previewData.headers.map((header: string, index: number) => (
                                              <th
                                                key={index}
                                                className="px-4 py-3 text-left text-xs font-semibold text-blue-800 uppercase tracking-wider"
                                              >
                                                {header}
                                              </th>
                                            ))}
                                          </tr>
                                        </thead>
                                        <tbody className="bg-white divide-y divide-gray-100">
                                          {previewData.rows.map((row: any, rowIndex: number) => (
                                            <tr key={rowIndex} className={rowIndex % 2 === 0 ? 'bg-white hover:bg-blue-25' : 'bg-blue-25 hover:bg-blue-50'}>
                                              {Array.from({ length: previewData.headers.length }).map((_, colIndex) => {
                                                const cellValue = row[colIndex] !== undefined ? String(row[colIndex]) : '-';
                                                const headerName = previewData.headers[colIndex];
                                                const shouldTruncate = shouldTruncateField(headerName);
                                                const displayValue = shouldTruncate ? truncateText(cellValue) : cellValue;

                                                const isTruncated = shouldTruncate && cellValue.length > 50;

                                                return (
                                                  <td
                                                    key={colIndex}
                                                    className={`px-4 py-3 text-sm text-gray-700 font-medium`}
                                                    title={isTruncated ? `Full text: ${cellValue}` : undefined}
                                                  >
                                                    <div className="flex items-center gap-1">
                                                      <span>{displayValue}</span>
                                                    </div>
                                                  </td>
                                                );
                                              })}
                                            </tr>
                                          ))}
                                        </tbody>
                                      </table>
                                    </div>
                                  </div>
                                  {previewData.rows.length > 0 && (
                                    <div className="mt-3 flex items-center gap-2">
                                        <div className="w-2 h-2 bg-blue-400 rounded-full"></div>
                                        <Text className="text-blue-600 text-sm font-medium">
                                          Showing first {previewData.rows.length} rows • Ready for import
                                        </Text>
                                    </div>
                                  )}
                                </div>
                              ) : previewError ? (
                                <Text className="text-red-500">{previewError}</Text>
                              ) : (
                                <Text className="text-gray-500">Loading preview...</Text>
                              )}
                            </div>
                          ) : (
                            <Text className="text-gray-500">
                              {filePreview.error || "Preview not available for this file type."}
                            </Text>
                          )}
                        </div>
                      </div>
                    )}
                  </div>
                </div>
              </>
            ) : (
              <>
                {/* Success Header */}
                <div className="bg-gradient-to-r from-green-50 to-emerald-50 p-6 rounded-lg border border-green-200 shadow-sm">
                  <div className="flex items-center gap-4">
                    <div className="flex-shrink-0 w-12 h-12 bg-green-100 rounded-full flex items-center justify-center">
                      <CheckCircleIcon className="w-8 h-8 text-green-600" />
                    </div>
                    <div className="flex-1">
                      <Heading level="h3" className="text-xl font-semibold text-green-800 mb-1">
                        Import Completed Successfully!
                      </Heading>
                      <Text className="text-green-700">
                        Your hotels have been imported and are now available in the system.
                      </Text>
                    </div>
                  </div>
                </div>

                {/* Detailed Results */}
                <div className="bg-white p-6 rounded-lg border border-gray-200 shadow-sm">
                  <div className="flex flex-col gap-4">
                    <Heading level="h3" className="text-lg font-medium text-gray-800 mb-2">
                      Import Summary
                    </Heading>
                    <div className="flex flex-col gap-2">
                      <div className="flex items-center gap-2">
                        <CheckCircleIcon className="w-5 h-5 text-green-500" />
                        <Text>Successfully imported: {uploadResult.results.successful}</Text>
                      </div>
                      {uploadResult.results.failed > 0 && (
                        <div className="flex items-center gap-2">
                          <XCircleIcon className="w-5 h-5 text-red-500" />
                          <Text>Failed to import: {uploadResult.results.failed}</Text>
                        </div>
                      )}
                    </div>
                  </div>
                </div>

                {uploadResult.results.errors && uploadResult.results.errors.length > 0 && (
                  <div className="bg-white p-6 rounded-lg border border-gray-200 shadow-sm">
                    <div className="flex flex-col gap-4">
                      <Heading level="h3" className="text-lg font-medium">
                        Errors
                      </Heading>
                      <div className="flex flex-col gap-2">
                        {uploadResult.results.errors.map((error: any, index: number) => (
                          <div key={index} className="p-3 border border-gray-200 rounded-md">
                            <Text className="font-medium">Row {error.row}</Text>
                            <Text className="text-red-500">{error.error}</Text>
                          </div>
                        ))}
                      </div>
                    </div>
                  </div>
                )}
              </>
            )}
          </div>

          <div className="flex-shrink-0 py-6 px-6 bg-gradient-to-r from-gray-50 to-gray-100 border-t border-gray-200">
            {!uploadResult ? (
              <div className="flex gap-3 justify-end">
                <Button variant="secondary" onClick={() => {
                  resetModalState();
                  // Use a direct close without triggering the parent's refresh logic
                  if (open) {
                    // Just close the modal without refreshing
                    const event = new CustomEvent('closeHotelModal', { detail: { refresh: false } });
                    window.dispatchEvent(event);
                  }
                }} className="px-6">
                  Cancel
                </Button>
                <Button
                  variant="primary"
                  onClick={handleUpload}
                  disabled={!file || isUploading || validationErrors.length > 0}
                  className={`flex items-center gap-2 px-6 py-2 ${
                    !file || isUploading || validationErrors.length > 0
                      ? 'bg-gray-400 cursor-not-allowed'
                      : 'bg-gradient-to-r from-blue-600 to-indigo-600 hover:from-blue-700 hover:to-indigo-700 shadow-lg'
                  }`}
                  title={validationErrors.length > 0 ? 'Please fix validation errors before importing' : ''}
                >
                  {isUploading ? (
                    <>
                      <div className="w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin"></div>
                      <span>Importing...</span>
                    </>
                  ) : (
                    <>
                      <UploadIcon className="w-4 h-4" />
                      <span>Import Data</span>
                    </>
                  )}
                </Button>
              </div>
            ) : (
              <div className="flex gap-3 justify-end">
                <Button
                  variant="secondary"
                  onClick={() => {
                    // Reset the upload result and file to allow re-upload
                    resetModalState();
                  }}
                  className="flex items-center gap-2 px-6"
                >
                  <UploadIcon className="w-4 h-4" />
                  Upload Another File
                </Button>
                <Button
                  variant="secondary"
                  onClick={() => {
                    // Check if we had a successful import
                    const hadSuccessfulImport = localStorage.getItem('hotelImportSuccess') === 'true';

                    resetModalState();
                    // Use a direct close with refresh flag based on whether we had a successful import
                    if (open) {
                      const event = new CustomEvent('closeHotelModal', {
                        detail: { refresh: hadSuccessfulImport }
                      });
                      window.dispatchEvent(event);

                      // Clear the flag after using it
                      localStorage.removeItem('hotelImportSuccess');
                    }
                  }}
                  className="px-6"
                >
                  Close
                </Button>
                <Button
                  variant="primary"
                  onClick={handleViewHotels}
                  className="flex items-center gap-2 px-6 bg-gradient-to-r from-green-600 to-emerald-600 hover:from-green-700 hover:to-emerald-700 shadow-lg"
                >
                  <CheckCircleIcon className="w-4 h-4" />
                  View Hotels
                </Button>
              </div>
            )}
          </div>
        </FocusModal.Body>
      </FocusModal.Content>
    </FocusModal>
  );
};

export default BulkImportModal;
