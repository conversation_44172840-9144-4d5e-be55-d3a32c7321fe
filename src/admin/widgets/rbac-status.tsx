import { defineWidgetConfig } from "@camped-ai/admin-sdk";
import { Container, Badge, Button } from "@camped-ai/ui";
import { Box } from "@mui/material";
import { useRbac } from "../hooks/use-rbac";
import { RoleBadge, RbacSetup } from "../components/rbac";

const RbacStatusWidget = () => {
  const { currentUser, loading, checkSetupStatus } = useRbac();

  if (loading) {
    return (
      <Container className="p-4">
        <Box sx={{ animation: 'pulse 2s infinite' }}>
          <Box sx={{ height: 16, bgcolor: 'grey.200', borderRadius: 1, width: '50%', mb: 1 }}></Box>
          <Box sx={{ height: 12, bgcolor: 'grey.200', borderRadius: 1, width: '75%' }}></Box>
        </Box>
      </Container>
    );
  }

  // No RBAC data - might need setup
  if (!currentUser?.rbac) {
    return (
      <Container className="p-4">
        <Box sx={{ display: 'flex', flexDirection: 'column', gap: 1.5 }}>
          <Box display="flex" justifyContent="space-between" alignItems="center">
            <h3 style={{ fontWeight: 500 }}>Access Control</h3>
            <Badge variant="warning">Setup Needed</Badge>
          </Box>

          <Box sx={{ fontSize: '0.875rem', color: 'text.secondary' }}>
            Role-based access control is not configured for your account.
          </Box>

          <Button
            size="small"
            onClick={() => window.location.href = "/admin/settings/users"}
          >
            Configure Access
          </Button>
        </Box>
      </Container>
    );
  }

  return (
    <Container className="p-4">
      <Box sx={{ display: 'flex', flexDirection: 'column', gap: 1.5 }}>
        <Box display="flex" justifyContent="space-between" alignItems="center">
          <h3 style={{ fontWeight: 500 }}>Your Access Level</h3>
          <RoleBadge
            role={currentUser.rbac.role}
            isActive={currentUser.rbac.is_active}
          />
        </Box>

        <Box sx={{ display: 'flex', flexDirection: 'column', gap: 1, fontSize: '0.875rem' }}>
          <Box display="flex" justifyContent="space-between">
            <span style={{ color: '#666' }}>Role:</span>
            <span style={{ fontWeight: 500 }}>
              {currentUser.rbac.role.replace("_", " ").toUpperCase()}
            </span>
          </Box>

          {currentUser.rbac.assigned_hotels.length > 0 && (
            <Box display="flex" justifyContent="space-between">
              <span style={{ color: '#666' }}>Hotels:</span>
              <span style={{ fontWeight: 500 }}>
                {currentUser.rbac.assigned_hotels.length}
              </span>
            </Box>
          )}

          <Box display="flex" justifyContent="space-between">
            <span style={{ color: '#666' }}>Status:</span>
            <Badge
              variant={currentUser.rbac.is_active ? "success" : "danger"}
              size="small"
            >
              {currentUser.rbac.is_active ? "Active" : "Inactive"}
            </Badge>
          </Box>
        </Box>

        {currentUser.rbac.assigned_hotels.length > 0 && (
          <Box sx={{ borderTop: 1, borderColor: 'divider', pt: 1.5 }}>
            <Box sx={{ fontSize: '0.75rem', color: 'text.disabled', mb: 0.5 }}>Assigned Hotels:</Box>
            <Box display="flex" flexWrap="wrap" gap={0.5}>
              {currentUser.rbac.assigned_hotels.slice(0, 3).map((hotelId) => (
                <Badge key={hotelId} variant="secondary" size="small">
                  {hotelId.length > 10 ? `${hotelId.substring(0, 10)}...` : hotelId}
                </Badge>
              ))}
              {currentUser.rbac.assigned_hotels.length > 3 && (
                <Badge variant="secondary" size="small">
                  +{currentUser.rbac.assigned_hotels.length - 3} more
                </Badge>
              )}
            </Box>
          </Box>
        )}
      </Box>
    </Container>
  );
};

export const config = defineWidgetConfig({
  zone: "dashboard.side",
});

export default RbacStatusWidget;
