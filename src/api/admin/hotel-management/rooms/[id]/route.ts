import { MedusaRequest, MedusaResponse } from "@camped-ai/framework/http";
import { enhanceRoomsWithAdditionalOptions } from "../utils/resolve-additional-options";
import { Modules } from "@camped-ai/framework/utils";
import { ROOM_INVENTORY_MODULE } from "../../../../../modules/hotel-management/room-inventory";

export const GET = async (req: MedusaRequest, res: MedusaResponse) => {
  try {
    const query = req.scope.resolve("query");
    const roomId = req.params.id;

    // Get the product variant (room)
    const { data: variants } = await query.graph({
      entity: "product_variant",
      filters: { id: [roomId] },
      fields: ["id", "title", "product_id", "metadata", "inventory_quantity"],
    });

    if (!variants || variants.length === 0) {
      return res.status(404).json({ message: "Room not found" });
    }

    const variant = variants[0];

    // Get the product (room configuration)
    const { data: products } = await query.graph({
      entity: "product",
      filters: { id: [variant.product_id] },
      fields: ["id", "title", "metadata", "categories"],
    });

    if (!products || products.length === 0) {
      return res.status(404).json({ message: "Room configuration not found" });
    }

    const product = products[0];

    // Map to room object
    const room = {
      id: variant.id,
      name: variant.title,
      room_number: variant.metadata?.room_number || "",
      status: variant.metadata?.status || "available",
      floor: variant.metadata?.floor || "",
      notes: variant.metadata?.notes || "",
      is_active: variant.metadata?.is_active !== false,
      room_config_id: product.metadata?.room_config_id || "",
      hotel_id: product.categories?.[0] || "",
      availability_type: variant.metadata?.availability_type || "standard",
      variant, // Include variant data for additional options resolution
    };

    // Enhance room with additional options (with fallback to room type)
    const [enhancedRoom] = await enhanceRoomsWithAdditionalOptions(req.scope, [room]);

    res.json({ room: enhancedRoom });
  } catch (error) {
    res.status(400).json({
      message:
        error instanceof Error ? error.message : "Failed to retrieve room",
    });
  }
};

export const PUT = async (req: MedusaRequest, res: MedusaResponse) => {
  try {
    const roomId = req.params.id;
    const { availability_type, ...otherUpdates } = req.body;

    const productModuleService = req.scope.resolve(Modules.PRODUCT);
    const roomInventoryService = req.scope.resolve(ROOM_INVENTORY_MODULE);

    // Get current room data
    const variant = await productModuleService.retrieveProductVariant(roomId);
    if (!variant) {
      return res.status(404).json({ message: "Room not found" });
    }

    // Update room metadata
    const updatedMetadata = {
      ...variant.metadata,
      ...otherUpdates,
    };

    // Handle availability type change
    if (
      availability_type &&
      availability_type !== variant.metadata?.availability_type
    ) {
      updatedMetadata.availability_type = availability_type;

      // Update inventory status based on availability type
      const today = new Date();
      const nextYear = new Date(today);
      nextYear.setFullYear(today.getFullYear() + 1);

      let newStatus = "available";
      if (availability_type === "on_demand") {
        newStatus = "on_demand";
      }

      // Update existing inventory entries
      await roomInventoryService.updateInventoryStatus(
        roomId,
        today,
        nextYear,
        newStatus,
        `Availability type changed to ${availability_type} on ${new Date().toISOString()}`
      );
    }

    // Update the variant
    const updatedVariant = await productModuleService.updateProductVariants({
      id: roomId,
      metadata: updatedMetadata,
    });

    res.json({
      room: {
        id: updatedVariant.id,
        availability_type: updatedMetadata.availability_type,
        message: "Room updated successfully",
      },
    });
  } catch (error) {
    res.status(400).json({
      message: error instanceof Error ? error.message : "Failed to update room",
    });
  }
};
