import { defineRouteConfig } from "@camped-ai/admin-sdk";
import { ArrowLeft } from "@camped-ai/icons";
import { Container, Heading, Button, Toaster, toast } from "@camped-ai/ui";
import { useState, lazy, Suspense } from "react";
import { useNavigate, useParams } from "react-router-dom";
import PermissionBasedSidebarHider from "../../../../../widgets/permission-based-sidebar-hider";
import { useRbac } from "../../../../../hooks/use-rbac";
import { useSupplier, useUpdateSupplier } from "../../../../../hooks/vendor-management/use-suppliers";

// Dynamically import page client for better performance
const SupplierEditForm = lazy(() => import("./page-client"));

const EditSupplierPage = () => {
  const navigate = useNavigate();
  const { id } = useParams<{ id: string }>();
  const { hasPermission } = useRbac();
  const { data: supplierResponse, isLoading } = useSupplier(id!);
  const updateSupplier = useUpdateSupplier();
  const [isSubmitting, setIsSubmitting] = useState(false);

  // Check permission and redirect if not authorized
  if (!hasPermission("supplier_management:update")) {
    navigate("/supplier-management");
    return null;
  }

  // Handle form submission
  const handleSubmit = async (data: any) => {
    setIsSubmitting(true);
    try {
      // Transform contacts data - ensure proper structure and types
      const transformedContacts = data.contacts.map((contact: any) => ({
        ...(contact.id && { id: contact.id }), // Only include id if it exists
        name: contact.name,
        email: contact.email,
        phone_number:
          contact.phone_number && contact.phone_number.trim()
            ? String(contact.phone_number).trim()
            : undefined,
        is_primary: Boolean(contact.is_primary),
      }));

      // Helper function to only include non-empty values
      const cleanValue = (value: any) => {
        if (value === null || value === undefined || value === "") {
          return undefined;
        }
        return value;
      };

      // Transform data for API
      const supplierData = {
        id: id!,
        // Basic Info
        name: data.name,
        supplier_type: data.supplier_type,
        website: cleanValue(data.website),
        status: data.status,

        // Business & Region Information
        timezone: cleanValue(data.timezone),
        language_preference:
          data.language_preference && data.language_preference.length > 0
            ? data.language_preference
            : undefined,

        // Financial Information
        payment_method: cleanValue(data.payment_method),
        payout_terms: cleanValue(data.payout_terms),
        tax_id: cleanValue(data.tax_id),
        default_currency: data.default_currency,
        bank_account_details: cleanValue(data.bank_account_details),

        // Categories
        categories:
          data.categories && data.categories.length > 0
            ? data.categories
            : undefined,

        // Contacts
        contacts: transformedContacts,
      };

      await updateSupplier.mutateAsync(supplierData);
      toast.success("Supplier updated successfully!");
      navigate(`/supplier-management/suppliers/${id}`);
    } catch (error: any) {
      console.error("Error updating supplier:", error);
      toast.error(error.message || "Failed to update supplier");
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleCancel = () => {
    navigate(`/supplier-management/suppliers/${id}`);
  };

  if (isLoading || !supplierResponse?.supplier) {
    return (
      <>
        <PermissionBasedSidebarHider />
        <Container className="divide-y p-0">
          <div className="flex items-center justify-center py-8">
            <Heading level="h3">Loading supplier details...</Heading>
          </div>
        </Container>
      </>
    );
  }

  if (!supplierResponse?.supplier) {
    return (
      <>
        <PermissionBasedSidebarHider />
        <Container className="divide-y p-0">
          <div className="flex items-center justify-center py-8">
            <Heading level="h3">Supplier not found</Heading>
          </div>
        </Container>
      </>
    );
  }

  return (
    <>
      <PermissionBasedSidebarHider />
      <Toaster />

      {/* Header */}
      <Container className="divide-y p-0">
        <div className="flex items-center justify-between px-6 py-4">
          <div className="flex items-center gap-3">
            <Button
              variant="secondary"
              size="small"
              onClick={() => navigate(`/supplier-management/suppliers/${id}`)}
            >
              <ArrowLeft className="w-4 h-4" />
              Back
            </Button>
            <Heading level="h1">Edit Supplier</Heading>
          </div>
        </div>
      </Container>

      {/* Form */}
      <Suspense
        fallback={
          <Container className="p-6">
            <div className="flex items-center justify-center py-12">
              <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
              <span className="ml-3 text-sm text-gray-600">Loading supplier edit form...</span>
            </div>
          </Container>
        }
      >
        <SupplierEditForm
          supplier={supplierResponse.supplier}
          onSubmit={handleSubmit}
          onCancel={handleCancel}
          isSubmitting={isSubmitting}
        />
      </Suspense>
    </>
  );
};

export const config = defineRouteConfig({
  label: "Edit Supplier",
});

export default EditSupplierPage;