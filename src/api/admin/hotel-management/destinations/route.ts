import {
  AuthenticatedMedusaRequest,
  MedusaResponse,
} from "@camped-ai/framework/http";
import { z } from "zod";
import {
  PostAdminCreateDestination,
  PostAdminDeleteDestination,
  PostAdminUpdateDestination,
} from "./validators";
import { CreateDestinationWorkflow } from "src/workflows/hotel-management/destination/create-destination";
import { UpdateDestinationWorkflow } from "src/workflows/hotel-management/destination/update-destination";
import { DeleteDestinationWorkflow } from "src/workflows/hotel-management/destination/delete-destination";
import { ContainerRegistrationKeys, Modules } from "@camped-ai/framework/utils";
import { RBAC_MODULE } from "../../../../modules/rbac";
import RbacModuleService from "../../../../modules/rbac/service";
import { UserRole } from "../../../../modules/rbac/types";

type PostAdminCreateDestinationType = z.infer<
  typeof PostAdminCreateDestination
>;
type PostAdminDeleteDestinationType = z.infer<
  typeof PostAdminDeleteDestination
>;
type PostAdminUpdateDestinationType = z.infer<
  typeof PostAdminUpdateDestination
>;

export const GET = async (
  req: AuthenticatedMedusaRequest,
  res: MedusaResponse
) => {
  try {
    const query = req.scope.resolve(ContainerRegistrationKeys.QUERY);
    const rbacService: RbacModuleService = req.scope.resolve(RBAC_MODULE);
    const userService = req.scope.resolve(Modules.USER);

    // Check permission to view destinations
    let userWithRole = null;
    if (req.auth_context?.actor_id) {
      try {
        userWithRole = await userService.retrieveUser(
          req.auth_context.actor_id
        );

        // Check if user has permission to view destinations
        const hasPermission = await rbacService.hasPermission(
          userWithRole,
          "destinations:view" as any
        );
        if (!hasPermission) {
          return res.status(403).json({
            error: "Insufficient permissions",
            required_permission: "destinations:view",
          });
        }
      } catch (error) {
        console.warn("Could not load user RBAC data:", error);
        return res.status(403).json({ error: "Authentication required" });
      }
    }

    const {
      limit = 20,
      offset = 0,
      is_featured,
      is_active,
      search,
    } = req.query || {};
    const filters: Record<string, any> = {};

    if (is_featured !== undefined) {
      filters.is_featured = is_featured === "true";
    }
    if (is_active !== undefined) {
      filters.is_active = is_active === "true";
    }

    // Add search functionality
    if (search && typeof search === "string" && search.trim()) {
      const searchTerm = search.trim();
      filters.$or = [
        { name: { $ilike: `%${searchTerm}%` } },
        { country: { $ilike: `%${searchTerm}%` } },
        { location: { $ilike: `%${searchTerm}%` } },
        { description: { $ilike: `%${searchTerm}%` } },
      ];
    }

    // Get all destinations first
    const {
      data: allDestinations,
      metadata: { count: totalCount, take, skip },
    } = await query.graph({
      entity: "destination",
      fields: ["*"],
      filters,
      pagination: {
        skip: Number(offset),
        take: Number(limit),
      },
    });

    // Filter destinations based on user permissions
    let filteredDestinations = allDestinations;
    let filteredCount = totalCount;

    if (userWithRole) {
      const rbacData = userWithRole.metadata?.rbac as any;

      // All users with valid roles can see all destinations
      // Access control is now permission-based, not destination-based
      if (rbacData?.role !== UserRole.ADMIN) {
        // Non-admin users see all destinations but with permission-based operations
        filteredDestinations = allDestinations;
        filteredCount = allDestinations.length;
      }
    }

    res.json({
      destinations: filteredDestinations,
      count: filteredCount,
      limit: take,
      offset: skip,
    });
  } catch (error) {
    console.error("Error in destination listing:", error);
    res.status(500).json({
      type: "server_error",
      message: "Failed to list destinations",
    });
  }
};

export const POST = async (
  req: AuthenticatedMedusaRequest<PostAdminCreateDestinationType>,
  res: MedusaResponse
) => {
  try {
    // Check permission to create destinations
    const rbacService: RbacModuleService = req.scope.resolve(RBAC_MODULE);
    const userService = req.scope.resolve(Modules.USER);

    if (req.auth_context?.actor_id) {
      const user = await userService.retrieveUser(req.auth_context.actor_id);
      const userWithRole = {
        ...user,
        created_at: user.created_at.toISOString(),
        updated_at: user.updated_at.toISOString(),
      };
      const hasPermission = await rbacService.hasPermission(
        userWithRole,
        "destinations:create" as any
      );

      if (!hasPermission) {
        return res.status(403).json({
          error: "Insufficient permissions",
          required_permission: "destinations:create",
        });
      }
    } else {
      return res.status(401).json({ error: "Authentication required" });
    }

    const { result } = await CreateDestinationWorkflow(req.scope).run({
      //@ts-ignore
      input: req.body,
    });
    res.json({ destination: result });
  } catch (error) {
    console.error("Error creating destination:", error);
    res.status(500).json({ error: "Failed to create destination" });
  }
};

export const PUT = async (
  req: AuthenticatedMedusaRequest<PostAdminUpdateDestinationType>,
  res: MedusaResponse
) => {
  try {
    // Check permission to edit destinations
    const rbacService: RbacModuleService = req.scope.resolve(RBAC_MODULE);
    const userService = req.scope.resolve(Modules.USER);

    if (req.auth_context?.actor_id) {
      const user = await userService.retrieveUser(req.auth_context.actor_id);
      const userWithRole = {
        ...user,
        created_at: user.created_at.toISOString(),
        updated_at: user.updated_at.toISOString(),
      };
      const hasPermission = await rbacService.hasPermission(
        userWithRole,
        "destinations:edit" as any
      );

      if (!hasPermission) {
        return res.status(403).json({
          error: "Insufficient permissions",
          required_permission: "destinations:edit",
        });
      }
    } else {
      return res.status(401).json({ error: "Authentication required" });
    }

    const { result } = await UpdateDestinationWorkflow(req.scope).run({
      //@ts-ignore
      input: req.body,
    });
    res.json({ destination: result });
  } catch (error) {
    console.error("Error updating destination:", error);
    res.status(500).json({ error: "Failed to update destination" });
  }
};

export const DELETE = async (
  req: AuthenticatedMedusaRequest<PostAdminDeleteDestinationType>,
  res: MedusaResponse
) => {
  try {
    // Check permission to delete destinations
    const rbacService: RbacModuleService = req.scope.resolve(RBAC_MODULE);
    const userService = req.scope.resolve(Modules.USER);

    if (req.auth_context?.actor_id) {
      const user = await userService.retrieveUser(req.auth_context.actor_id);
      const userWithRole = {
        ...user,
        created_at: user.created_at.toISOString(),
        updated_at: user.updated_at.toISOString(),
      };
      const hasPermission = await rbacService.hasPermission(
        userWithRole,
        "destinations:delete" as any
      );

      if (!hasPermission) {
        return res.status(403).json({
          error: "Insufficient permissions",
          required_permission: "destinations:delete",
        });
      }
    } else {
      return res.status(401).json({ error: "Authentication required" });
    }

    const { ids } = req.body;
    const { result } = await DeleteDestinationWorkflow(req.scope).run({
      input: { ids: ids.split(",") },
    });
    res.json({ ids: result });
  } catch (error) {
    console.error("Error deleting destination:", error);
    res.status(500).json({ error: "Failed to delete destination" });
  }
};
