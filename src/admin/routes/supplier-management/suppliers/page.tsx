import { defineRouteConfig } from "@camped-ai/admin-sdk";
import { Buildings } from "@camped-ai/icons";
import { Container, Heading, Text } from "@camped-ai/ui";
import PermissionBasedSidebarHider from "../../../widgets/permission-based-sidebar-hider";
import { RoleGuard } from "../../../components/rbac/RoleGuard";
import { lazy, Suspense } from "react";
import AdminPageHelmet from "../../../components/AdminPageHelmet";

// Dynamically import page client for better performance
const SuppliersPageClient = lazy(() => import("./page-client"));

const SupplierListPage = () => {
  return (
    <>
      <AdminPageHelmet />
      <PermissionBasedSidebarHider />
      <RoleGuard
        requirePermission="supplier_management:view"
        fallback={
          <Container>
            <div className="p-8 text-center">
              <Heading level="h1">Access Denied</Heading>
              <Text className="mt-2">
                You don't have permission to view supplier management.
              </Text>
            </div>
          </Container>
        }
      >
        <Suspense
          fallback={
            <Container className="p-6">
              <div className="flex items-center justify-center py-12">
                <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
                <span className="ml-3 text-sm text-gray-600">Loading suppliers...</span>
              </div>
            </Container>
          }
        >
          <SuppliersPageClient />
        </Suspense>
      </RoleGuard>
    </>
  );
};

export const config = defineRouteConfig({
  label: "Suppliers",
  icon: Buildings,
});

export default SupplierListPage;
