import { MedusaRequest, MedusaResponse } from "@camped-ai/framework/http";
import { ContainerRegistrationKeys } from "@camped-ai/framework/utils";
import * as ExcelJS from 'exceljs';
import { createObjectCsvStringifier } from 'csv-writer';
import J<PERSON><PERSON><PERSON> from 'jszip';

/**
 * GET endpoint to export destinations data
 */
export const GET = async (req: MedusaRequest, res: MedusaResponse) => {
  try {
    // Get query service to include FAQ relationships
    const query = req.scope.resolve(ContainerRegistrationKeys.QUERY);

    // Parse query parameters
    const { fields, is_active, is_featured, country, format } = req.query;

    // Build filter object
    const filters: Record<string, any> = {};

    if (is_active && is_active !== 'all') {
      filters.is_active = is_active === 'true';
    }

    if (is_featured && is_featured !== 'all') {
      filters.is_featured = is_featured === 'true';
    }

    if (country && country !== 'all') {
      filters.country = country;
    }

    console.log('Export filters:', filters);

    // Fetch destinations with FAQs included
    const { data: destinations } = await query.graph({
      entity: "destination",
      filters,
      fields: ["*", "faqs.*"],
    });

    // Parse fields to include
    const fieldsToInclude = fields ? (fields as string).split(',') : [
      'id', 'name', 'handle', 'description', 'is_active', 'is_featured',
      'country', 'location', 'tags', 'website', 'created_at', 'updated_at'
    ];

    // Process destinations data
    const processedData = destinations.map(destination => {
      const data: Record<string, any> = {};

      fieldsToInclude.forEach(field => {
        if (field === 'tags' && destination[field]) {
          // Handle tags field specially - export as comma-separated values without brackets/quotes
          if (Array.isArray(destination[field])) {
            // Join array elements with comma (no space) to match import template format
            data[field] = destination[field].join(',');
          } else if (typeof destination[field] === 'string') {
            // If it's already a string, check if it's JSON format and convert if needed
            const tagString = (destination[field] as string).trim();
            if (tagString.startsWith('[') && tagString.endsWith(']')) {
              try {
                const parsed = JSON.parse(tagString);
                if (Array.isArray(parsed)) {
                  data[field] = parsed.join(',');
                } else {
                  data[field] = tagString;
                }
              } catch (e) {
                // If JSON parsing fails, treat as comma-separated string
                data[field] = tagString;
              }
            } else {
              // Already in correct format
              data[field] = tagString;
            }
          } else {
            // For any other type, try to convert to array first, then join
            try {
              if (destination[field] && typeof destination[field] === 'object') {
                // If it's an object but not an array, try to extract values
                const values = Object.values(destination[field]);
                if (values.length > 0) {
                  data[field] = values.join(',');
                } else {
                  data[field] = '';
                }
              } else {
                data[field] = '';
              }
            } catch (e) {
              data[field] = '';
            }
          }
        } else {
          data[field] = destination[field];
        }
      });

      return data;
    });

    // Export based on format
    if (format === 'csv') {
      // CSV export - create ZIP with destinations and FAQs files
      const zip = new JSZip();

      // Create destinations CSV
      const headers = fieldsToInclude.map(field => ({
        id: field,
        title: field.replace(/_/g, ' ').replace(/\b\w/g, l => l.toUpperCase())
      }));

      const csvStringifier = createObjectCsvStringifier({
        header: headers
      });

      const destinationsCsvContent = processedData.length === 0
        ? csvStringifier.getHeaderString()
        : csvStringifier.getHeaderString() + csvStringifier.stringifyRecords(processedData);

      zip.file('destinations.csv', destinationsCsvContent);

      // Create FAQs CSV
      const faqHeaders = [
        { id: 'destination_name', title: 'Destination Name' },
        { id: 'question', title: 'Question' },
        { id: 'answer', title: 'Answer' }
      ];

      const faqCsvStringifier = createObjectCsvStringifier({
        header: faqHeaders
      });

      // Process FAQ data
      const faqData = [];
      destinations.forEach(destination => {
        if (destination.faqs && destination.faqs.length > 0) {
          destination.faqs.forEach(faq => {
            faqData.push({
              destination_name: destination.name,
              question: faq.question,
              answer: faq.answer
            });
          });
        }
      });

      const faqCsvContent = faqData.length === 0
        ? faqCsvStringifier.getHeaderString()
        : faqCsvStringifier.getHeaderString() + faqCsvStringifier.stringifyRecords(faqData);

      zip.file('faqs.csv', faqCsvContent);

      // Generate ZIP and send response
      const zipBuffer = await zip.generateAsync({ type: 'nodebuffer' });

      res.setHeader('Content-Type', 'application/zip');
      res.setHeader('Content-Disposition', 'attachment; filename=destinations-export.zip');
      return res.send(zipBuffer);
    } else {
      // Excel export (default)
      const workbook = new ExcelJS.Workbook();
      const worksheet = workbook.addWorksheet('Destinations');

      // Add headers
      const headers = fieldsToInclude.map(field => ({
        header: field.replace(/_/g, ' ').replace(/\b\w/g, l => l.toUpperCase()),
        key: field,
        width: 20
      }));

      worksheet.columns = headers;

      // Style the header row
      worksheet.getRow(1).font = { bold: true };
      worksheet.getRow(1).fill = {
        type: 'pattern',
        pattern: 'solid',
        fgColor: { argb: 'FFE0E0E0' }
      };

      // Add data
      processedData.forEach(data => {
        worksheet.addRow(data);
      });

      // Auto-fit columns
      worksheet.columns.forEach(column => {
        const lengths = column.values?.filter(v => v !== undefined).map(v => v.toString().length);
        if (lengths && lengths.length > 0) {
          const maxLength = Math.max(column.header.length, ...lengths);
          column.width = maxLength < 10 ? 10 : maxLength < 50 ? maxLength : 50;
        }
      });

      // Create FAQs sheet
      const faqsSheet = workbook.addWorksheet('FAQs');

      // Define FAQ columns to match import template
      faqsSheet.columns = [
        { header: 'Destination Name', key: 'destination_name', width: 25 },
        { header: 'Question', key: 'question', width: 50 },
        { header: 'Answer', key: 'answer', width: 70 }
      ];

      // Style the FAQ header row
      faqsSheet.getRow(1).font = { bold: true, color: { argb: 'FF000000' } };
      faqsSheet.getRow(1).fill = { type: 'pattern', pattern: 'solid', fgColor: { argb: 'FFE0E0E0' } };
      faqsSheet.getRow(1).alignment = { vertical: 'middle', horizontal: 'center' };

      // Process and add FAQ data
      destinations.forEach(destination => {
        if (destination.faqs && destination.faqs.length > 0) {
          destination.faqs.forEach(faq => {
            faqsSheet.addRow({
              destination_name: destination.name,
              question: faq.question,
              answer: faq.answer
            });
          });
        }
      });

      // Set content type and headers for Excel file download
      res.setHeader('Content-Type', 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet');
      res.setHeader('Content-Disposition', 'attachment; filename=destinations-export.xlsx');

      // Write the workbook to the response
      await workbook.xlsx.write(res);
    }
  } catch (error) {
    console.error('Error exporting destinations:', error);
    res.status(500).json({ message: 'Error exporting destinations' });
  }
};
