import { Select } from "@camped-ai/ui";
import { Globe } from "lucide-react";
import { useProjectLanguages } from "../hooks/languages/useProjectLanguages";

export interface LanguageSelectorProps {
  selectedLanguage?: string;
  onLanguageChange?: (languageCode: string) => void;
  className?: string;
}

const LanguageSelector = ({
  selectedLanguage = "en",
  onLanguageChange,
  className = "",
}: LanguageSelectorProps) => {
  const { languages: tolgeeLanguages, loading, error } = useProjectLanguages();

  // Fallback mock data if Tolgee is not available
  const mockLanguages = [
    {
      id: 1488911118,
      name: "English",
      tag: "en",
      originalName: "English",
      flagEmoji: "🇬🇧",
      base: true,
    },
    {
      id: 1488911116,
      name: "German",
      tag: "de",
      originalName: "Deutsch",
      flagEmoji: "🇩🇪",
      base: false,
    },
    {
      id: 1488911117,
      name: "Japanese",
      tag: "ja",
      originalName: "日本語",
      flagEmoji: "🇯🇵",
      base: false,
    },
  ];

  // Use Tolgee data if available, otherwise use mock data
  const availableLanguages =
    tolgeeLanguages.length > 0 ? tolgeeLanguages : mockLanguages;

  // Find the currently selected language
  const currentLanguage =
    availableLanguages.find((lang) => lang.tag === selectedLanguage) ||
    availableLanguages[0];

  const handleLanguageSelect = (languageCode: string) => {
    onLanguageChange?.(languageCode);
  };

  if (loading) {
    return (
      <div className={`flex items-center gap-2 ${className}`}>
        <Globe size={16} className="text-gray-400" />
        <div className="w-20 h-6 bg-gray-200 rounded animate-pulse" />
      </div>
    );
  }

  if (error) {
    return (
      <div className={`flex items-center gap-2 text-gray-400 ${className}`}>
        <Globe size={16} />
        <span className="text-sm">Language unavailable</span>
      </div>
    );
  }

  return (
    <div className={className}>
      <Select value={selectedLanguage} onValueChange={handleLanguageSelect}>
        <Select.Trigger className="w-56">
          <Select.Value>
            <div className="flex items-center justify-between gap-2 w-full">
              <div className="flex items-center gap-2 flex-1">
                <span className="text-base">{currentLanguage.flagEmoji}</span>
                <span className="font-medium">{currentLanguage.name}</span>
              </div>
              {currentLanguage.base && (
                <Globe size={12} className="text-blue-600 shrink-0" />
              )}
            </div>
          </Select.Value>
        </Select.Trigger>
        <Select.Content>
          {availableLanguages.map((language) => (
            <Select.Item key={language.tag} value={language.tag}>
              <div className="flex items-center gap-3 w-full">
                <span className="text-base">{language.flagEmoji}</span>
                <div className="flex-1 text-left">
                  <div className="font-medium">{language.name}</div>
                  <div className="text-xs text-gray-500">
                    {language.originalName}
                  </div>
                </div>
                {language.base && <Globe size={12} className="text-blue-600" />}
              </div>
            </Select.Item>
          ))}
        </Select.Content>
      </Select>
    </div>
  );
};

export default LanguageSelector;
