name: Medusa Backend Deployment

# Trigger the workflow on any push to the 'main' branch
on:
  push:
    branches:
      - powderbyrne-prod

jobs:
  build-and-deploy:
    runs-on: ubuntu-latest

    steps:
      # Step 1: Checkout code from the repository
      - name: Checkout code
        uses: actions/checkout@v2

      - name: Configure NPM for GitHub Packages
        run: |
          echo "@camped-ai:registry=https://npm.pkg.github.com/" > .npmrc
          echo "//npm.pkg.github.com/:_authToken=${{ secrets.GIT_TOKEN }}" >> .npmrc

      # Step 2: Log in to Azure Container Registry (ACR)
      - name: Log in to Azure Container Registry
        uses: azure/docker-login@v1
        with:
          login-server: powderbyrneprod.azurecr.io
          username: powderbyrneprod
          password: ${{ secrets.POWDERBYRNE_PROD_ACR_PASSWORD }}

      # Step 3: Log in to Azure
      - name: Login to Azure
        uses: azure/login@v1
        with:
          creds: ${{ secrets.AZURE_CREDENTIALS }}

      # Step 4: Set up Node.js environment
      - name: Set up Node.js
        uses: actions/setup-node@v3
        with:
          node-version: 20

      # Step 5: Build the Docker image for the Medusa backend
      - name: Build Docker image
        run: |
          docker build -t powderbyrneprod-backend .

      # Step 6: Tag the Docker image for ACR
      - name: Tag Docker image for ACR
        run: |
          docker tag powderbyrneprod-backend:latest powderbyrneprod.azurecr.io/powderbyrneprod-backend:latest

      # Step 7: Push the Docker image to ACR
      - name: Push Docker image to ACR
        run: |
          docker push powderbyrneprod.azurecr.io/powderbyrneprod-backend:latest

      - name: Set up Kubectl
        uses: azure/setup-kubectl@v1
        with:
          version: 'latest' # Or specify a specific version

      - name: Get AKS Credentials
        run: |
          az aks get-credentials --resource-group camped-medusa --name shop-test --admin --overwrite-existing

      - name: Inject Secrets into Kubernetes Secrets YAML
        run: |
          cat <<EOF > k8s/secrets.yaml
          apiVersion: v1
          kind: Secret
          metadata:
            name: shop-powderbyrneprod-secrets
          type: Opaque
          stringData:
            SMTP_PASS: ${{ secrets.SMTP_PASS }}
            RAZORPAY_SECRET: ${{ secrets.RAZORPAY_SECRET }}
            RAZORPAY_WEBHOOK_SECRET: ${{ secrets.RAZORPAY_WEBHOOK_SECRET }}
            S3_ACCESS_KEY_ID: ${{ secrets.S3_ACCESS_KEY_ID }}
            S3_SECRET_ACCESS_KEY: ${{ secrets.S3_SECRET_ACCESS_KEY }}
            STRIPE_API_KEY: ${{ secrets.POWDERBYRNE_PROD_STRIPE_API_KEY }}
            STRIPE_WEBHOOK_SECRET_KEY: ${{ secrets.POWDERBYRNE_PROD_STRIPE_WEBHOOK_SECRET_KEY }}
            RAZORPAY_SECRET: ${{ secrets.RAZORPAY_SECRET }}
            AZURE_OPENAI_API_KEY: ${{ secrets.AZURE_OPENAI_API_KEY }}
            VITE_TOLGEE_API_KEY: ${{ secrets.VITE_TOLGEE_API_KEY }}
          EOF

      - name: Deploy to AKS
        run: |
          kubectl apply -f k8s/secrets.yaml
          kubectl apply -f k8s/deployment.yaml
          kubectl apply -f k8s/service.yaml
          kubectl patch deployment powderbyrneprod-backend-v2 -p "{\"spec\": {\"template\": {\"metadata\": { \"labels\": {  \"date\": \"$(date +'%s')\"}}}}}"
          kubectl rollout restart deployment/medusa-backend

      - name: Finalize and Verify Deployment
        run: |
          echo "Deployment to AKS completed successfully!"
