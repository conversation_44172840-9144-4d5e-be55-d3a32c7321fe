import { MedusaRequest, MedusaResponse } from "@camped-ai/framework/http";

// Define content types
type ContentType =
  | "description"
  | "name"
  | "tags"
  | "seo"
  | "email"
  | "general";

export const POST = async (req: MedusaRequest<any>, res: MedusaResponse) => {
  try {
    const { type, prompt, context, maxLength } = req.body;

    if (!type) {
      return res.status(400).json({
        message: "Content type is required",
      });
    }

    const apiKey =
      process.env.AZURE_OPENAI_API_KEY || process.env.OPENAI_API_KEY;

    if (!apiKey) {
      return res.status(400).json({
        message: "Azure OpenAI API key is not configured",
      });
    }

    // Use custom system prompt if provided, otherwise use default based on content type
    const systemPrompt =
      context?.systemPrompt || getSystemPromptForContentType(type);

    // Create a user prompt with context (skip context processing if custom system prompt is used)
    const userPrompt = context?.systemPrompt
      ? prompt
      : createUserPrompt(type, prompt, context);

    // Call the OpenAI API
    const response = await callOpenAI(
      apiKey,
      systemPrompt,
      userPrompt,
      maxLength
    );

    // Content generated successfully

    res.json({
      content: response,
      status: "success",
    });
  } catch (error) {
    res.status(500).json({
      content: "",
      status: "error",
      message: error.message || "Internal server error",
    });
  }
};

/**
 * Get the system prompt for a specific content type
 */
function getSystemPromptForContentType(type: ContentType): string {
  switch (type) {
    case "description":
      return "You are an expert copywriter specializing in creating compelling, detailed, and engaging descriptions. Your task is to create a description that is informative, highlights key features, and appeals to the target audience. Keep the tone professional but warm. Even with limited or missing context, create relevant, specific content that would be appropriate for the type of item being described. Focus on creating content that would be useful to potential customers or users.";

    case "name":
      return "You are a naming expert who creates catchy, memorable, and appropriate names. Your task is to generate a name that is unique, easy to remember, and reflects the essence of what is being named. Even with limited information, create a name that would be suitable for the type of item or entity described. Avoid generic names and aim for something distinctive and relevant.";

    case "tags":
      return "You are a categorization expert who creates relevant tags and keywords. Your task is to generate a set of tags that accurately represent the content and will help with search and discovery. Even with minimal context, provide tags that would be appropriate for the type of item described. Aim for a mix of specific and general tags to maximize discoverability.";

    case "seo":
      return "You are an SEO expert who creates search-engine optimized content. Your task is to generate content that includes relevant keywords, is engaging to readers, and will rank well in search engines. Even with limited context, create content that would be valuable and appropriate for the subject matter. Focus on natural language that incorporates likely search terms.";

    case "email":
      return "You are an email marketing expert who creates effective email content. Your task is to generate email content that is engaging, clear, and drives action. Even with minimal information, create content that would be appropriate for the context provided. Focus on creating value for the recipient and a clear call to action.";

    case "general":
    default:
      return "You are a helpful assistant who creates high-quality content. Your task is to generate content based on the provided context and instructions. Even when context is limited, create content that is relevant, specific, and valuable. Use your knowledge to fill in gaps appropriately while staying true to the available information.";
  }
}

/**
 * Create a user prompt with context
 */
function createUserPrompt(
  type: ContentType,
  prompt?: string,
  context?: Record<string, any>
): string {
  // Start with a base prompt based on the content type
  let basePrompt = "";

  switch (type) {
    case "description":
      basePrompt = "Create a compelling and detailed description";
      break;
    case "name":
      basePrompt = "Suggest a unique and memorable name";
      break;
    case "tags":
      basePrompt = "Suggest relevant tags or keywords (comma-separated)";
      break;
    case "email":
      basePrompt = "Suggest a professional email address";
      break;
    case "general":
    default:
      basePrompt = "Generate appropriate content";
      break;
  }

  // Use the provided prompt or the base prompt
  let userPrompt = prompt || basePrompt;

  // Add context information to the prompt
  userPrompt +=
    "\n\nHere is some context to help you generate better content:\n";

  // Always include context, even if some values are empty
  if (context && Object.keys(context).length > 0) {
    for (const [key, value] of Object.entries(context)) {
      userPrompt += `${key}: ${value || "Not specified"}\n`;
    }
  }

  // Add more detailed instructions based on content type
  switch (type) {
    case "description":
      userPrompt +=
        "\n\nCreate a compelling description that highlights the key features and benefits. Be specific, engaging, and informative. Include details that would be relevant to potential customers. Even if some context is missing, use what's available to create a relevant description.";
      break;

    case "name":
      userPrompt +=
        "\n\nSuggest a unique and memorable name that reflects the brand identity and purpose. Even with limited context, create a name that would be appropriate and appealing.";
      break;

    case "tags":
      userPrompt +=
        "\n\nSuggest 5-10 relevant tags or keywords (comma-separated) that would help with categorization and search. Use the available context to ensure the tags are appropriate.";
      break;

    case "seo":
      userPrompt +=
        "\n\nCreate SEO-friendly content that includes relevant keywords while maintaining natural readability. Focus on creating valuable content even if some context is missing.";
      break;

    case "email":
      userPrompt +=
        "\n\nCreate engaging email content that drives action. Use the available context to ensure the content is relevant and appropriate.";
      break;

    case "general":
    default:
      userPrompt +=
        "\n\nGenerate appropriate content based on the provided context. Even with limited information, create content that would be useful and relevant.";
      break;
  }

  return userPrompt;
}

/**
 * Call the OpenAI API
 */
async function callOpenAI(
  apiKey: string,
  systemPrompt: string,
  userPrompt: string,
  maxLength?: number
): Promise<string> {
  try {
    // Check if we're using Azure OpenAI or standard OpenAI
    const isAzureOpenAI = !!process.env.AZURE_OPENAI_API_ENDPOINT;

    let apiUrl: string;
    let headers: Record<string, string>;
    let model: string;

    if (isAzureOpenAI) {
      // Azure OpenAI configuration
      const deployment = process.env.AZURE_OPENAI_DEPLOYMENT || "camped";
      const apiVersion =
        process.env.AZURE_OPENAI_API_VERSION || "2024-08-01-preview";
      const endpoint = process.env.AZURE_OPENAI_API_ENDPOINT;

      apiUrl = `${endpoint}/openai/deployments/${deployment}/chat/completions?api-version=${apiVersion}`;
      headers = {
        "Content-Type": "application/json",
        "api-key": apiKey,
      };
      model = process.env.AZURE_OPENAI_MODEL || "gpt-4o";
    } else {
      // Standard OpenAI configuration
      apiUrl = process.env.OPENAI_API_URL || "https://api.openai.com/v1";
      headers = {
        "Content-Type": "application/json",
        Authorization: `Bearer ${apiKey}`,
      };
      model = process.env.OPENAI_MODEL || "gpt-3.5-turbo";
    }

    const defaultMaxTokens = parseInt(process.env.OPENAI_MAX_TOKENS || "500");
    const maxTokens = maxLength || defaultMaxTokens;

    const response = await fetch(apiUrl, {
      method: "POST",
      headers,
      body: JSON.stringify({
        model,
        messages: [
          {
            role: "system",
            content: systemPrompt,
          },
          {
            role: "user",
            content: userPrompt,
          },
        ],
        max_tokens: maxTokens,
        temperature: 0.7,
      }),
    });

    if (!response.ok) {
      const errorData = await response.json();
      throw new Error(
        `OpenAI API error: ${errorData.error?.message || response.statusText}`
      );
    }

    const data = await response.json();
    return data.choices[0]?.message?.content?.trim() || "";
  } catch (error) {
    throw error;
  }
}
