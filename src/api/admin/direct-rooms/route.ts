import { MedusaRequest, MedusaResponse } from "@camped-ai/framework/http";
import { Modules } from "@camped-ai/framework/utils";
import { ROOM_INVENTORY_MODULE } from "../../../modules/hotel-management/room-inventory";
import { z } from "zod";

// Validation schema for creating a room
export const PostAdminCreateRoom = z.object({
  name: z.string(),
  room_number: z.string(),
  status: z.enum(["available", "occupied", "maintenance", "cleaning"]),
  floor: z.string(),
  notes: z.string().optional(),
  is_active: z.boolean().default(true),
  // Room relationships
  left_room: z.string().optional(),
  opposite_room: z.string().optional(),
  connected_room: z.string().optional(),
  right_room: z.string().optional(),
  // References
  room_config_id: z.string(),
  hotel_id: z.string(),
  price: z.number().optional(),
  currency_code: z.string().optional(),
  // Availability type for on-demand rooms
  availability_type: z.string().optional(),
  // Additional Options (room-level overrides)
  max_extra_beds: z.number().min(0).max(5).optional(),
  max_cots: z.number().min(0).max(3).optional(),
  max_adults_beyond_capacity: z.number().min(0).max(5).optional(),
});

export type PostAdminCreateRoomType = z.infer<typeof PostAdminCreateRoom>;

// Validation schema for updating a room
export const PostAdminUpdateRoom = z.object({
  id: z.string(),
  name: z.string().optional(),
  room_number: z.string().optional(),
  status: z
    .enum(["available", "occupied", "maintenance", "cleaning"])
    .optional(),
  floor: z.string().optional(),
  notes: z.string().optional(),
  is_active: z.boolean().optional(),
  // Room relationships
  left_room: z.string().optional(),
  opposite_room: z.string().optional(),
  connected_room: z.string().optional(),
  right_room: z.string().optional(),
  // References - Added room_config_id and hotel_id for room type updates
  room_config_id: z.string().optional(),
  hotel_id: z.string().optional(),
  // Availability type for on-demand rooms
  availability_type: z.string().optional(),
  // Additional Options (room-level overrides)
  max_extra_beds: z.number().min(0).max(5).optional(),
  max_cots: z.number().min(0).max(3).optional(),
  max_adults_beyond_capacity: z.number().min(0).max(5).optional(),
});

export type PostAdminUpdateRoomType = z.infer<typeof PostAdminUpdateRoom>;

// GET endpoint to list rooms
export const GET = async (req: MedusaRequest, res: MedusaResponse) => {
  const query = req.scope.resolve("query");
  const { room_config_id, hotel_id } = req.query;

  // Don't use query filters for now
  // Just fetch all products and filter them in memory

  console.log(`Fetching all products`);

  let products = [];

  // If room_config_id is provided, we only need to fetch that specific product
  if (room_config_id) {
    console.log(`Fetching specific product with ID: ${room_config_id}`);
    try {
      // Use the query to get the product
      const { data: productData } = await query.graph({
        entity: "product",
        filters: { id: room_config_id as string },
        fields: ["id", "title", "description", "subtitle", "metadata"],
      });

      // Get variants for this product
      const { data: variantsData } = await query.graph({
        entity: "product_variant",
        filters: { product_id: room_config_id as string },
        fields: [
          "id",
          "title",
          "product_id",
          "metadata",
          "inventory_quantity",
          "options",
        ],
      });

      if (productData && productData.length > 0) {
        const product = productData[0];
        console.log(`Found product: ${product.title}`);
        products = [product];

        // If we have variants for this product, we can return the rooms directly
        if (variantsData && variantsData.length > 0) {
          console.log(
            `Found ${variantsData.length} variants for product ${product.title}`
          );

          // Map variants to room objects with room configuration details
          const rooms = variantsData.map((variant: any) => {

            // Extract room configuration details from product metadata
            const roomConfig = {
              id: product.id,
              name: product.title || "Unnamed Room Configuration",
              type: product.metadata?.type || "standard",
              description: product.description || "",
              room_size: product.metadata?.room_size || "",
              bed_type: product.metadata?.bed_type || "",
              max_extra_beds: product.metadata?.max_extra_beds || 0,
              max_cots: product.metadata?.max_cots || 0,
              max_adults: product.metadata?.max_adults || 1,
              max_adults_beyond_capacity: product.metadata?.max_adults_beyond_capacity || 0,
              max_children: product.metadata?.max_children || 0,
              max_infants: product.metadata?.max_infants || 0,
              max_occupancy: product.metadata?.max_occupancy || 1,
              amenities: Array.isArray(product.metadata?.amenities) ? product.metadata.amenities : [],
              hotel_id: product.metadata?.hotel_id || (hotel_id as string),
            };

            // Resolve additional options with fallback to room config
            const resolvedAdditionalOptions = {
              max_extra_beds: variant.metadata?.max_extra_beds !== undefined
                ? variant.metadata.max_extra_beds
                : (roomConfig.max_extra_beds || 0),
              max_cots: variant.metadata?.max_cots !== undefined
                ? variant.metadata.max_cots
                : (roomConfig.max_cots || 0),
              max_adults_beyond_capacity: variant.metadata?.max_adults_beyond_capacity !== undefined
                ? variant.metadata.max_adults_beyond_capacity
                : (roomConfig.max_adults_beyond_capacity || 0),
            };

            return {
              id: variant.id,
              name: variant.title,
              room_number: variant.metadata?.room_number || "",
              status: variant.metadata?.status || "available",
              floor: variant.metadata?.floor || "",
              notes: variant.metadata?.notes || "",
              is_active: variant.metadata?.is_active !== false,
              left_room: variant.metadata?.left_room || "",
              opposite_room: variant.metadata?.opposite_room || "",
              connected_room: variant.metadata?.connected_room || "",
              right_room: variant.metadata?.right_room || "",
              room_config_id: product.id,
              hotel_id: product.metadata?.hotel_id || (hotel_id as string),
              options: variant.options || {},
              metadata: variant.metadata || {},
              room_config: roomConfig, // Include room configuration details
              inventory_quantity: variant.inventory_quantity || 0,
              // Additional options with fallback logic
              ...resolvedAdditionalOptions,
              has_additional_options_override: (
                variant.metadata?.max_extra_beds !== undefined ||
                variant.metadata?.max_cots !== undefined ||
                variant.metadata?.max_adults_beyond_capacity !== undefined
              ),
            };
          });

          return res.json({ rooms });
        }
      } else {
        console.log(`Product not found with ID: ${room_config_id}`);
        return res.json({ rooms: [] });
      }
    } catch (error) {
      console.error(`Error fetching product: ${error.message}`);
      return res.json({ rooms: [] });
    }
  } else {
    // Otherwise, get all products and filter by hotel_id
    const { data: allProducts } = await query.graph({
      entity: "product",
      fields: ["id", "title", "description", "subtitle", "metadata"],
    });

    console.log(`Found ${allProducts?.length || 0} total products`);

    if (!allProducts || allProducts.length === 0) {
      console.log(`No products found`);
      return res.json({ rooms: [] });
    }

    // Filter products in memory
    products = allProducts.filter((product) => {
      // Check if hotel_id matches
      if (hotel_id) {
        console.log(
          `Comparing hotel_id: ${hotel_id} with product.metadata?.hotel_id: ${product.metadata?.hotel_id}`
        );
        // Convert both to strings for comparison to avoid type mismatches
        if (String(product.metadata?.hotel_id) !== String(hotel_id)) {
          return false;
        }
      }

      return true;
    });
  }

  console.log(`After filtering, found ${products.length} products`);

  if (products.length === 0) {
    console.log(`No products found after filtering`);
    return res.json({ rooms: [] });
  }

  // Get all product variants for these products
  const productIds = products.map((p) => p.id);
  console.log(`Fetching variants for product IDs: ${productIds.join(", ")}`);

  let variants = [];
  try {
    const { data: variantsData } = await query.graph({
      entity: "product_variant",
      filters: { product_id: productIds },
      fields: [
        "id",
        "title",
        "product_id",
        "metadata",
        "inventory_quantity",
        "options.id",
        "options.value",
        "options.option.title",
      ],
    });

    variants = variantsData || [];
    console.log(`Found ${variants.length} variants`);

    if (variants.length === 0) {
      console.log(`No variants found for products`);
      return res.json({ rooms: [] });
    }
  } catch (error) {
    console.error(`Error fetching variants: ${error.message}`);
    return res.json({ rooms: [] });
  }

  // Map variants to room objects with room configuration details
  const rooms = variants.map((variant) => {
    const product = products.find((p) => p.id === variant.product_id);
    console.log(
      `Processing variant: ${variant.id} (${variant.title}) for product: ${
        product?.title || "unknown"
      }`
    );

    // Get options for this variant
    const variantOptions = {};
    if (variant.options && Array.isArray(variant.options)) {
      variant.options.forEach((optionValue) => {
        if (optionValue.option) {
          variantOptions[optionValue.option.title] = optionValue.value;
        }
      });
    }

    // Extract room configuration details from product metadata
    const roomConfig = product ? {
      id: product.id,
      name: product.title || "Unnamed Room Configuration",
      type: product.metadata?.type || "standard",
      description: product.description || "",
      room_size: product.metadata?.room_size || "",
      bed_type: product.metadata?.bed_type || "",
      max_extra_beds: product.metadata?.max_extra_beds || 0,
      max_cots: product.metadata?.max_cots || 0,
      max_adults: product.metadata?.max_adults || 1,
      max_adults_beyond_capacity: product.metadata?.max_adults_beyond_capacity || 0,
      max_children: product.metadata?.max_children || 0,
      max_infants: product.metadata?.max_infants || 0,
      max_occupancy: product.metadata?.max_occupancy || 1,
      amenities: Array.isArray(product.metadata?.amenities) ? product.metadata.amenities : [],
      hotel_id: product.metadata?.hotel_id || (hotel_id as string),
    } : null;

    return {
      id: variant.id,
      name: variant.title,
      room_number: variant.metadata?.room_number || "",
      status: variant.metadata?.status || "available",
      floor: variant.metadata?.floor || "",
      notes: variant.metadata?.notes || "",
      is_active: variant.metadata?.is_active !== false,
      left_room: variant.metadata?.left_room || "",
      opposite_room: variant.metadata?.opposite_room || "",
      connected_room: variant.metadata?.connected_room || "",
      right_room: variant.metadata?.right_room || "",
      room_config_id: product?.id || "",
      hotel_id: product?.metadata?.hotel_id || (hotel_id as string),
      options: variantOptions,
      metadata: variant.metadata || {},
      created_at: variant.created_at,
      updated_at: variant.updated_at,
      room_config: roomConfig, // Include room configuration details
      inventory_quantity: variant.inventory_quantity || 0,
    };
  });

  console.log(`Returning ${rooms.length} actual rooms`);
  console.log(
    `Sample room:`,
    rooms.length > 0 ? JSON.stringify(rooms[0]) : "No rooms found"
  );

  res.json({ rooms });
};

// POST endpoint to create a room
export const POST = async (
  req: MedusaRequest<PostAdminCreateRoomType>,
  res: MedusaResponse
) => {
  try {
    const {
      name,
      room_number,
      status,
      floor,
      notes,
      is_active,
      left_room,
      opposite_room,
      connected_room,
      right_room,
      room_config_id,
      hotel_id,
      price,
      currency_code,
      availability_type,
      max_extra_beds,
      max_cots,
      max_adults_beyond_capacity,
    } = req.body;

    const productModuleService = req.scope.resolve(Modules.PRODUCT);

    // Try to resolve the room inventory service, but don't fail if it's not available
    let roomInventoryService = null;
    try {
      roomInventoryService = req.scope.resolve(ROOM_INVENTORY_MODULE);
    } catch (error) {
      console.warn(
        "Room inventory service not available. Skipping inventory creation."
      );
    }

    // First, check if a product exists for this room configuration
    const { data: products } = await req.scope.resolve("query").graph({
      entity: "product",
      filters: { id: room_config_id },
      fields: ["id", "title", "subtitle", "description", "metadata"],
    });

    if (!products || products.length === 0) {
      return res.status(404).json({
        message:
          "Room configuration not found. Please create a room configuration first.",
      });
    }

    const productId = products[0].id;



    // Create a product variant for the specific room
    console.log("Creating product variant for room:", room_number);

    const variantData = {
      title: name || `Room ${room_number}`,
      product_id: productId,
      inventory_quantity: status === "available" && is_active ? 1 : 0,
      manage_inventory: false,
      allow_backorder: false,

      metadata: {
        room_number: room_number,
        floor: floor,
        notes: notes,
        status: status,
        is_active: is_active,
        left_room: left_room,
        opposite_room: opposite_room,
        connected_room: connected_room,
        right_room: right_room,
        availability_type: availability_type,
        // Additional Options (room-level overrides) - only include if explicitly provided
        ...(max_extra_beds !== undefined && { max_extra_beds }),
        ...(max_cots !== undefined && { max_cots }),
        ...(max_adults_beyond_capacity !== undefined && { max_adults_beyond_capacity }),
      },
    };

    console.log("Variant data:", JSON.stringify(variantData, null, 2));

    const variant = await productModuleService.createProductVariants(
      variantData
    );
    console.log("Created variant:", variant);

    // Emit the room.created event
    try {
      const eventModuleService = req.scope.resolve(Modules.EVENT_BUS);
      await eventModuleService.emit({
        name: "room.created",
        data: {
          id: variant.id,
          variant: {
            ...variant,
            metadata: {
              ...variant.metadata,
              hotel_id: hotel_id,
              room_config_id: room_config_id,
            },
          },
        },
      });
      console.log(`Emitted room.created event for room ${room_number}`);
    } catch (eventError) {
      console.error("Error emitting room.created event:", eventError);
      // Continue even if event emission fails
    }

    // Return the created room (variant) with additional information
    res.json({
      room: {
        id: variant.id,
        name: name || `Room ${room_number}`,
        room_number: room_number,
        status: status,
        floor: floor,
        notes: notes,
        is_active: is_active,
        left_room: left_room,
        opposite_room: opposite_room,
        connected_room: connected_room,
        right_room: right_room,
        room_config_id: room_config_id,
        hotel_id: hotel_id,
        product_id: productId,
        variant_id: variant.id,
        inventory_item_id: variant.id,

      },
    });
  } catch (error) {
    console.error("Error creating room:", error);
    res.status(400).json({
      message: error instanceof Error ? error.message : "Failed to create room",
    });
  }
};

// PUT endpoint to update a room
export const PUT = async (
  req: MedusaRequest<PostAdminUpdateRoomType>,
  res: MedusaResponse
) => {
  try {
    console.log("PUT /admin/direct-rooms - Request body:", req.body);
    const { id, ...updateData } = req.body;
    console.log("Extracted updateData:", updateData);

    // Get the product variant
    const productModuleService = req.scope.resolve(Modules.PRODUCT);
    const variant = await productModuleService.retrieveProductVariant(id, {
      relations: ["product", "options"],
    });

    if (!variant) {
      return res.status(404).json({ message: "Room not found" });
    }

    // Check if room_config_id (room type) is being changed
    let targetProductId = variant.product_id;
    if (updateData.room_config_id && updateData.room_config_id !== variant.product_id) {
      // Verify the new room configuration exists
      const { data: newProducts } = await req.scope.resolve("query").graph({
        entity: "product",
        filters: { id: updateData.room_config_id },
        fields: ["id", "title"],
      });

      if (!newProducts || newProducts.length === 0) {
        return res.status(404).json({
          message: "New room configuration not found.",
        });
      }

      targetProductId = updateData.room_config_id;
      console.log(`Room type change detected: ${variant.product_id} -> ${targetProductId}`);
    }

    // Handle hotel_id update - update the product metadata if hotel_id is changing
    if (updateData.hotel_id) {
      const currentProduct = await productModuleService.retrieveProduct(targetProductId, {
        fields: ["id", "metadata"],
      });

      if (currentProduct && currentProduct.metadata?.hotel_id !== updateData.hotel_id) {
        console.log(`Hotel ID change detected: ${currentProduct.metadata?.hotel_id} -> ${updateData.hotel_id}`);

        // Update the product metadata with the new hotel_id
        await productModuleService.updateProducts(targetProductId, {
          metadata: {
            ...currentProduct.metadata,
            hotel_id: updateData.hotel_id,
          },
        });
      }
    }

    // Update the variant metadata
    const updatedMetadata = { ...variant.metadata };

    if (updateData.room_number)
      updatedMetadata.room_number = updateData.room_number;
    if (updateData.floor) updatedMetadata.floor = updateData.floor;
    if (updateData.notes !== undefined)
      updatedMetadata.notes = updateData.notes;
    if (updateData.status) updatedMetadata.status = updateData.status;
    if (updateData.is_active !== undefined)
      updatedMetadata.is_active = updateData.is_active;
    if (updateData.left_room !== undefined)
      updatedMetadata.left_room = updateData.left_room;
    if (updateData.opposite_room !== undefined)
      updatedMetadata.opposite_room = updateData.opposite_room;
    if (updateData.connected_room !== undefined)
      updatedMetadata.connected_room = updateData.connected_room;
    if (updateData.right_room !== undefined)
      updatedMetadata.right_room = updateData.right_room;
    if (updateData.availability_type !== undefined)
      updatedMetadata.availability_type = updateData.availability_type;

    // Update additional options (room-level overrides)
    if (updateData.max_extra_beds !== undefined)
      updatedMetadata.max_extra_beds = updateData.max_extra_beds;
    if (updateData.max_cots !== undefined)
      updatedMetadata.max_cots = updateData.max_cots;
    if (updateData.max_adults_beyond_capacity !== undefined)
      updatedMetadata.max_adults_beyond_capacity = updateData.max_adults_beyond_capacity;



    // Handle room type change (product_id change) - this requires special handling
    let updatedVariant;
    if (targetProductId !== variant.product_id) {
      console.log("Room type is changing - need to recreate variant with new product_id");

      // Create new variant with the new product_id
      const newVariantData = {
        title: updateData.name || variant.title,
        product_id: targetProductId,
        inventory_quantity: variant.inventory_quantity,
        manage_inventory: false,
        allow_backorder: variant.allow_backorder,
        metadata: updatedMetadata,
      };

      // Create the new variant
      const newVariant = await productModuleService.createProductVariants(newVariantData);

      // Delete the old variant
      await productModuleService.deleteProductVariants(id);

      // Use the new variant
      updatedVariant = newVariant;
      console.log(`Created new variant ${newVariant.id} and deleted old variant ${id}`);
    } else {
      // Normal update - just update metadata and title
      const variantUpdateData = {
        title: updateData.name,
        metadata: updatedMetadata,
      };

      console.log("Updating variant with data:", {
        id,
        ...variantUpdateData,
      });

      updatedVariant = await productModuleService.updateProductVariants(
        id,
        variantUpdateData
      );
    }

    // Get updated options for this variant
    const optionValues = [];
    // Note: We're skipping option values retrieval since it's causing issues
    // This would normally fetch the option values for the variant

    // Map option values to a more readable format
    const variantOptions = {};
    if (optionValues) {
      optionValues.forEach((ov) => {
        if (ov.option) {
          variantOptions[ov.option.title] = ov.value;
        }
      });
    }

    // Collect changes for event emission
    const changes = [];

    // Check for status change
    if (updateData.status && updateData.status !== variant.metadata?.status) {
      changes.push({
        type: "status",
        old_status: variant.metadata?.status,
        new_status: updateData.status,
      });
    }

    // Check for active status change
    if (
      updateData.is_active !== undefined &&
      updateData.is_active !== variant.metadata?.is_active
    ) {
      changes.push({
        type: "active",
        old_value: variant.metadata?.is_active,
        new_value: updateData.is_active,
      });
    }

    // Emit room-status-updated event if there are changes
    if (changes.length > 0) {
      try {
        const eventModuleService = req.scope.resolve(Modules.EVENT_BUS);
        await eventModuleService.emit({
          name: "room.status_updated",
          data: {
            id: variant.id,
            room_number:
              variant.metadata?.room_number || updateData.room_number,
            changes: changes,
          },
        });
        console.log(
          `Emitted room.status_updated event for room ${
            variant.metadata?.room_number || updateData.room_number
          }`
        );
      } catch (eventError) {
        console.error("Error emitting room.status_updated event:", eventError);
        // Continue even if event emission fails
      }
    }

    // Get the final variant ID (might have changed if room type was updated)
    const finalVariantId = updatedVariant.id;

    // Get the updated variant with product information to ensure we have the latest data
    const refreshedVariant = await productModuleService.retrieveProductVariant(finalVariantId, {
      relations: ["product"],
    });

    // Get the product to access its metadata for hotel_id
    const updatedProduct = await productModuleService.retrieveProduct(refreshedVariant.product_id, {
      fields: ["id", "metadata"],
    });

    // Map the updated variant to a room object
    const room = {
      id: refreshedVariant.id,
      name: refreshedVariant.title,
      room_number: refreshedVariant.metadata?.room_number || "",
      status: refreshedVariant.metadata?.status || "available",
      floor: refreshedVariant.metadata?.floor || "",
      notes: refreshedVariant.metadata?.notes || "",
      is_active: refreshedVariant.metadata?.is_active !== false,
      left_room: refreshedVariant.metadata?.left_room || "",
      opposite_room: refreshedVariant.metadata?.opposite_room || "",
      connected_room: refreshedVariant.metadata?.connected_room || "",
      right_room: refreshedVariant.metadata?.right_room || "",
      room_config_id: refreshedVariant.product_id,
      hotel_id: updatedProduct?.metadata?.hotel_id || "",
      options: variantOptions,
    };

    res.json({ room });
  } catch (error) {
    console.error("Error updating room:", error);
    res.status(400).json({
      message: error instanceof Error ? error.message : "Failed to update room",
    });
  }
};

// DELETE endpoint to delete a room
export const DELETE = async (req: MedusaRequest, res: MedusaResponse) => {
  try {
    const body = req.body as { ids?: string[] };
    const { ids } = body;

    if (!ids) {
      return res.status(400).json({ message: "Room IDs are required" });
    }

    const roomIds = ids;
    const productModuleService = req.scope.resolve(Modules.PRODUCT);

    // Delete each variant (room)
    for (const id of roomIds) {
      await productModuleService.deleteProductVariants(id);
    }

    res.json({ success: true, ids: roomIds });
  } catch (error) {
    console.error("Error deleting room:", error);
    res.status(400).json({
      message: error instanceof Error ? error.message : "Failed to delete room",
    });
  }
};
