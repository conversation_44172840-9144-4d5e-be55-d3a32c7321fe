import { defineRouteConfig } from "@camped-ai/admin-sdk";
import { Container, Heading, Text, Button } from "@camped-ai/ui";
import { useParams, useNavigate, Navigate } from "react-router-dom";
import { useEffect } from "react";
import PermissionBasedSidebarHider from "../../../../../widgets/permission-based-sidebar-hider";
import AdminPageHelmet from "../../../../../components/AdminPageHelmet";

const EditBookingPage = () => {
  const { id } = useParams();
  const navigate = useNavigate();

  // Redirect to booking details page
  useEffect(() => {
    navigate(`/hotel-management/bookings/${id}`);
  }, [id, navigate]);

  return (
    <>
      <AdminPageHelmet />
      <PermissionBasedSidebarHider />
      <Container>
        <div className="text-center py-8">
          <Heading level="h1">Redirecting...</Heading>
          <Text className="mt-2">
            The edit booking page has been replaced with specific actions.
          </Text>
          <Button
            className="mt-4"
            onClick={() => navigate(`/hotel-management/bookings/${id}`)}
          >
            Go to Booking Details
          </Button>
        </div>
      </Container>
    </>
  );
};

export const config = defineRouteConfig({
  label: "Edit Booking",
});

export default EditBookingPage;
