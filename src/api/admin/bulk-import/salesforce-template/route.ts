import { MedusaRequest, MedusaResponse } from "@camped-ai/framework/http";
import * as ExcelJS from 'exceljs';

/**
 * GET endpoint to download a Salesforce format bulk import template
 * Contains 3 sheets: Destinations, Hotels, and Rooms with Salesforce field names
 */
export const GET = async (_req: MedusaRequest, res: MedusaResponse) => {
  try {
    // Create a new workbook
    const workbook = new ExcelJS.Workbook();

    // Sheet 1: Destinations (Salesforce format)
    const destinationsSheet = workbook.addWorksheet('Destinations');
    setupSalesforceDestinationsSheet(destinationsSheet);

    // Sheet 2: Hotels (Salesforce format)
    const hotelsSheet = workbook.addWorksheet('Hotels');
    setupSalesforceHotelsSheet(hotelsSheet);

    // Sheet 3: Rooms (Salesforce format)
    const roomsSheet = workbook.addWorksheet('Rooms');
    setupSalesforceRoomsSheet(roomsSheet);

    // Sheet 4: Instructions
    const instructionsSheet = workbook.addWorksheet('Instructions');
    setupSalesforceInstructionsSheet(instructionsSheet);

    // Set content type and headers for Excel file download
    res.setHeader('Content-Type', 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet');
    res.setHeader('Content-Disposition', 'attachment; filename=salesforce-import-template.xlsx');

    // Write the workbook to the response
    await workbook.xlsx.write(res);

  } catch (error) {
    console.error('Error generating Salesforce import template:', error);
    res.status(500).json({ message: 'Error generating template' });
  }
};

/**
 * Setup Salesforce Destinations sheet
 */
function setupSalesforceDestinationsSheet(worksheet: ExcelJS.Worksheet) {
  // Define columns with only mapped Salesforce field names
  worksheet.columns = [
    { header: 'Name', key: 'name', width: 30 },
    { header: 'Country__c', key: 'country', width: 20 },
    { header: 'Current__c', key: 'is_active', width: 15 },
    { header: 'R_Website__c', key: 'website', width: 40 },
    { header: 'Description__c', key: 'description', width: 50 },
  ];

  // Style the header row
  const headerRow = worksheet.getRow(1);
  headerRow.font = { bold: true, color: { argb: 'FF000000' } };
  headerRow.fill = { type: 'pattern', pattern: 'solid', fgColor: { argb: 'FFE0E0E0' } };

  // Add sample data
  worksheet.addRow({
    name: 'Cortina d\'Ampezzo',
    country: 'Italy',
    is_active: 'TRUE',
    website: 'https://www.powderbyrne.com/our-destinations/italy/cortina',
    description: 'Beautiful mountain destination in the Italian Alps'
  });

  worksheet.addRow({
    name: 'Chamonix',
    country: 'France',
    is_active: 'TRUE',
    website: 'https://www.powderbyrne.com/our-destinations/france/chamonix',
    description: 'Premier ski destination in the French Alps'
  });

  // Auto-fit columns
  worksheet.columns.forEach(column => {
    if (column.width && column.width < 15) {
      column.width = 15;
    }
  });
}

/**
 * Setup Salesforce Hotels sheet
 */
function setupSalesforceHotelsSheet(worksheet: ExcelJS.Worksheet) {
  // Define columns with only mapped Salesforce field names
  worksheet.columns = [
    { header: 'Resort__c', key: 'destination', width: 30 },
    { header: 'Name', key: 'name', width: 30 },
    { header: 'Current__c', key: 'is_active', width: 15 },
    { header: 'H_Website__c', key: 'website', width: 40 },
    { header: 'H_Email__c', key: 'email', width: 30 },
    { header: 'Description__c', key: 'description', width: 50 },
  ];

  // Style the header row
  const headerRow = worksheet.getRow(1);
  headerRow.font = { bold: true, color: { argb: 'FF000000' } };
  headerRow.fill = { type: 'pattern', pattern: 'solid', fgColor: { argb: 'FFE0E0E0' } };

  // Add sample data
  worksheet.addRow({
    destination: 'Cortina d\'Ampezzo',
    name: 'Faloria Mountain Spa Resort',
    is_active: 'TRUE',
    website: 'https://www.powderbyrne.com/our-destinations/italy/cortina/faloria-mountain-spa-resort',
    email: '<EMAIL>',
    description: 'Visitor\'s tax is payable locally and will be charged to your extras bill.'
  });

  // Auto-fit columns
  worksheet.columns.forEach(column => {
    if (column.width && column.width < 15) {
      column.width = 15;
    }
  });
}

/**
 * Setup Salesforce Rooms sheet
 */
function setupSalesforceRoomsSheet(worksheet: ExcelJS.Worksheet) {
  // Define columns with only mapped Salesforce field names
  worksheet.columns = [
    { header: 'Id', key: 'id', width: 20 },
    { header: 'Resort_Hotel__c', key: 'hotel_name', width: 30 },
    { header: 'Room_Type__r.Name', key: 'room_config_name', width: 25 },
    { header: 'Name', key: 'room_number', width: 15 },
    { header: 'Connected_Room__c', key: 'connected_room', width: 20 },
    { header: 'Opposite_to__c', key: 'opposite_room', width: 20 },
    { header: 'Next_to__c', key: 'left_room', width: 20 },
  ];

  // Style the header row
  const headerRow = worksheet.getRow(1);
  headerRow.font = { bold: true, color: { argb: 'FF000000' } };
  headerRow.fill = { type: 'pattern', pattern: 'solid', fgColor: { argb: 'FFE0E0E0' } };

  // Add sample data with clear ID to room number mapping
  worksheet.addRow({
    id: 'a0cQP000003Ok6PYAS', // Room 101's ID
    hotel_name: 'Faloria Mountain Spa Resort',
    room_config_name: 'Deluxe Room',
    room_number: '101',
    connected_room: 'a0cQP000003Ok6QYAS', // Connected to room 102 (see row below)
    opposite_room: 'a0cQP000003Ok6RYAS', // Opposite to room 201 (see row below)
    left_room: 'a0cQP000003Ok6SYAS' // Next to room 103 (see row below)
  });

  worksheet.addRow({
    id: 'a0cQP000003Ok6QYAS', // Room 102's ID
    hotel_name: 'Faloria Mountain Spa Resort',
    room_config_name: 'Superior Suite',
    room_number: '102',
    connected_room: 'a0cQP000003Ok6PYAS', // Connected to room 101 (see row above)
    opposite_room: '', // No opposite room
    left_room: '' // No left room
  });

  worksheet.addRow({
    id: 'a0cQP000003Ok6SYAS', // Room 103's ID
    hotel_name: 'Faloria Mountain Spa Resort',
    room_config_name: 'Deluxe Room',
    room_number: '103',
    connected_room: '', // No connected room
    opposite_room: '', // No opposite room
    left_room: '' // No left room
  });

  worksheet.addRow({
    id: 'a0cQP000003Ok6RYAS', // Room 201's ID
    hotel_name: 'Faloria Mountain Spa Resort',
    room_config_name: 'Superior Suite',
    room_number: '201',
    connected_room: '', // No connected room
    opposite_room: 'a0cQP000003Ok6PYAS', // Opposite to room 101 (see first row)
    left_room: '' // No left room
  });

  // Auto-fit columns
  worksheet.columns.forEach(column => {
    if (column.width && column.width < 15) {
      column.width = 15;
    }
  });
}

/**
 * Setup Salesforce Instructions sheet
 */
function setupSalesforceInstructionsSheet(worksheet: ExcelJS.Worksheet) {
  worksheet.columns = [
    { header: 'Instructions for Salesforce Data Import', key: 'instructions', width: 80 }
  ];

  // Style the header
  const headerRow = worksheet.getRow(1);
  headerRow.font = { bold: true, size: 14, color: { argb: 'FF000000' } };
  headerRow.fill = { type: 'pattern', pattern: 'solid', fgColor: { argb: 'FFE0E0E0' } };

  // Add instructions
  const instructions = [
    '',
    'SALESFORCE DATA IMPORT TEMPLATE',
    '=====================================',
    '',
    'This template is designed for importing data exported from Salesforce.',
    'The field names match Salesforce custom field API names.',
    '',
    'FIELD MAPPINGS:',
    '',
    'DESTINATIONS:',
    '- Name → Destination Name',
    '- Country__c → Country',
    '- Current__c → Is Active (TRUE/FALSE)',
    '- R_Website__c → Website URL',
    '- Description__c → Description',
    '',
    'HOTELS:',
    '- Resort__c → Destination Name (must match destination from Destinations sheet)',
    '- Name → Hotel Name',
    '- Current__c → Is Active (TRUE/FALSE)',
    '- H_Email__c → Email Address',
    '- Description__c → Description',
    '',
    'ROOMS:',
    '- Id → Salesforce Room ID (used for room relationships)',
    '- Room_Type__r.Name → Room Configuration Name',
    '- Resort_Hotel__c → Hotel Name (must match hotel from Hotels sheet)',
    '- Name → Room Number',
    '- Connected_Room__c → Connected Room ID (copy from Id column of target room)',
    '- Opposite_to__c → Opposite Room ID (copy from Id column of target room)',
    '- Next_to__c → Left Room ID (copy from Id column of target room)',
    '',
    'IMPORT ORDER:',
    '1. Import Destinations first',
    '2. Import Hotels (references destinations)',
    '3. Import Rooms (room configurations are auto-created from Room_Type__r.Name)',
    '',
    'NOTES:',
    '- Boolean fields should use TRUE/FALSE values',
    '- Room configurations are AUTO-CREATED from Room_Type__r.Name - no separate RoomConfigs sheet needed',
    '- Room relationships: Copy the Id value from the target room row into the relationship field',
    '  Example: If room 101 connects to room 102, copy room 102\'s Id into room 101\'s Connected_Room__c field',
    '- Room relationships are processed in Phase 2 after all rooms are created',
    '- The Id column helps you identify which room ID to use for relationships',
    '- All sheets are optional - you can import just destinations, or destinations + hotels, etc.',
    '- Empty rows will be skipped',
    '- The system will auto-generate handles/slugs from names if not provided'
  ];

  instructions.forEach((instruction, index) => {
    const row = worksheet.addRow({ instructions: instruction });
    if (index === 1) { // Title row
      row.font = { bold: true, size: 12 };
    } else if (instruction.includes(':') && !instruction.includes('//')) { // Section headers
      row.font = { bold: true };
    }
  });
}
