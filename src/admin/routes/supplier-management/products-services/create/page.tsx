import { defineRouteConfig } from "@camped-ai/admin-sdk";
import { Container, Heading, Button, Toaster, toast } from "@camped-ai/ui";
import { useState, lazy, Suspense, startTransition } from "react";
import { useNavigate } from "react-router-dom";
import PermissionBasedSidebarHider from "../../../../widgets/permission-based-sidebar-hider";
import { RoleGuard } from "../../../../components/rbac/RoleGuard";
import { useCreateProductService } from "../../../../hooks/supplier-products-services/use-products-services";

// Dynamically import page client with auto-generate functionality
const ProductServiceForm = lazy(() => import("./page-client"));

const CreateProductServicePage = () => {
  const navigate = useNavigate();
  const createProductService = useCreateProductService();
  const [isSubmitting, setIsSubmitting] = useState(false);

  const handleSubmit = async (formData: any) => {
    setIsSubmitting(true);
    try {
      const submitData = {
        // Include name if provided, otherwise backend will auto-generate
        name: formData.name?.trim() || undefined,
        type: formData.type,
        description: formData.description,
        base_cost:
          formData.base_cost !== "" ? Number(formData.base_cost) : undefined,
        custom_fields:
          Object.keys(formData.custom_fields).length > 0
            ? formData.custom_fields
            : undefined,
        category_id: formData.category_id,
        unit_type_id: formData.unit_type_id,
        tag_ids: formData.tags,
        status: formData.status,
      };

      console.log("🚀 Submitting product/service data:", submitData);
      console.log("📝 Form name field:", formData.name);

      await createProductService.mutateAsync(submitData);

      toast.success("Product/Service created successfully!");
      navigate("/supplier-management/products-services");
    } catch (error: any) {
      console.error("Error creating product/service:", error);
      // Error handling is now done in the hook
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleCancel = () => {
    navigate("/supplier-management/products-services");
  };

  return (
    <>
      <PermissionBasedSidebarHider />
      <RoleGuard
        requirePermission="supplier_management:create"
        fallback={
          <Container className="p-6">
            <div className="text-center">
              <Heading level="h2">Access Denied</Heading>
              <p className="text-ui-fg-subtle mt-2">
                You don't have permission to create supplier management items.
              </p>
              <Button
                onClick={() => navigate("/supplier-management/products-services")}
                className="mt-4"
              >
                Back to Products & Services
              </Button>
            </div>
          </Container>
        }
      >
        <Suspense
          fallback={
            <Container className="p-6">
              <div className="flex items-center justify-center py-12">
                <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
                <span className="ml-3 text-sm text-gray-600">Loading form...</span>
              </div>
            </Container>
          }
        >
          <ProductServiceForm
            mode="create"
            onSubmit={handleSubmit}
            onCancel={handleCancel}
            isSubmitting={isSubmitting}
          />
        </Suspense>
      </RoleGuard>

      <Toaster />
    </>
  );
};

export const config = defineRouteConfig({
  label: "Add Product/Service",
});

export default CreateProductServicePage;
