import { Suspense } from "react";
import { Container } from "@camped-ai/ui";
import AdminPageHelmet from "../../../../components/AdminPageHelmet";
import PermissionBasedSidebarHider from "../../../../widgets/permission-based-sidebar-hider";
import { Toaster } from "@camped-ai/ui";
import { LazyLoadErrorBoundary, createSafeLazyComponent } from "../../../../components/common/lazy-load-error-boundary";
// Dynamically import page client for better performance
const PageClient = createSafeLazyComponent(() => import("./page-client"));

const DestinationsPage = () => {
  return (
    <>
      <AdminPageHelmet />
      <PermissionBasedSidebarHider />
      <Toaster />
      <Suspense
        fallback={
          <Container className="p-6">
            <div className="flex items-center justify-center py-12">
              <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
              <span className="ml-3 text-sm text-gray-600">
                Loading carts...
              </span>
            </div>
          </Container>
        }
      >
        <PageClient />
      </Suspense>
    </>
  );
};

export default DestinationsPage;

import type { UIMatch } from "react-router-dom";

export const handle = {
  breadcrumb: (match: UIMatch) => {
    // Prefer a human title passed via query param, fallback to slug
    const search = (match as any)?.pathname || "";
    // match doesn't expose search; use location if available via match.data? Fallback to window.location
    const query =
      typeof window !== "undefined"
        ? new URLSearchParams(window.location.search)
        : new URLSearchParams();
    const title = query.get("title");
    const slug =
      (match?.params as Record<string, string | undefined>)?.slug || "Destination";
    return title || slug;
  },
};
