import React, { Component, ErrorInfo, ReactNode } from "react";
import { Container, Text, Button } from "@camped-ai/ui";

interface LazyLoadErrorBoundaryState {
  hasError: boolean;
  error?: Error;
}

interface LazyLoadErrorBoundaryProps {
  children: ReactNode;
  fallbackMessage?: string;
}

/**
 * Error boundary specifically designed for lazy-loaded components
 * that might have Router context issues or other loading problems
 */
export class LazyLoadErrorBoundary extends Component<
  LazyLoadErrorBoundaryProps,
  LazyLoadErrorBoundaryState
> {
  constructor(props: LazyLoadErrorBoundaryProps) {
    super(props);
    this.state = { hasError: false };
  }

  static getDerivedStateFromError(error: Error): LazyLoadErrorBoundaryState {
    return { hasError: true, error };
  }

  componentDidCatch(error: Error, errorInfo: ErrorInfo) {
    console.error("Lazy load error:", error, errorInfo);
    
    // Log specific Router context errors
    if (error.message?.includes("useContext") || error.message?.includes("useNavigate")) {
      console.error("Router context error detected - this might be a timing issue with lazy loading");
    }
  }

  render() {
    if (this.state.hasError) {
      const message = this.props.fallbackMessage || 
        "Failed to load page component. This might be a routing context issue.";
        
      return (
        <Container className="p-6">
          <div className="flex flex-col items-center justify-center py-12 space-y-4">
            <Text className="text-red-600 text-center">
              {message}
            </Text>
            <div className="flex gap-2">
              <Button
                variant="secondary"
                onClick={() => window.location.reload()}
              >
                Reload Page
              </Button>
              <Button
                variant="transparent"
                onClick={() => this.setState({ hasError: false })}
              >
                Try Again
              </Button>
            </div>
          </div>
        </Container>
      );
    }

    return this.props.children;
  }
}

/**
 * Helper function to create a lazy-loaded component with proper error handling
 * and Router context timing fixes
 */
export const createSafeLazyComponent = (importFn: () => Promise<{ default: React.ComponentType<any> }>) => {
  return React.lazy(() => 
    new Promise<{ default: React.ComponentType<any> }>(resolve => {
      setTimeout(async () => {
        try {
          const module = await importFn();
          resolve(module);
        } catch (error) {
          console.error("Failed to load lazy component:", error);
          throw error;
        }
      }, 0);
    })
  );
};
