import { defineRouteConfig } from "@camped-ai/admin-sdk";
import { Toaster, toast, Container } from "@camped-ai/ui";
import { useState, lazy, Suspense } from "react";
import { useNavigate } from "react-router-dom";
import PermissionBasedSidebarHider from "../../../../widgets/permission-based-sidebar-hider";
import { useRbac } from "../../../../hooks/use-rbac";
import { useCreateSupplier, useUploadSupplierDocuments } from "../../../../hooks/vendor-management/use-suppliers";

// Dynamically import page client for better performance
const SupplierCreateForm = lazy(() => import("./page-client"));

const CreateSupplierPage = () => {
  const navigate = useNavigate();
  const { hasPermission } = useRbac();
  const createSupplier = useCreateSupplier();
  const uploadSupplierDocuments = useUploadSupplierDocuments();
  const [isSubmitting, setIsSubmitting] = useState(false);

  // Check permission and redirect if not authorized
  if (!hasPermission("supplier_management:create")) {
    navigate("/supplier-management");
    return null;
  }

  // Handle form submission
  const handleSubmit = async (data: any) => {
    setIsSubmitting(true);
    try {
      // Transform contacts data - ensure proper structure and types
      const transformedContacts = data.contacts.map((contact: any) => ({
        name: contact.name,
        email: contact.email,
        phone_number:
          contact.phone_number && contact.phone_number.trim()
            ? String(contact.phone_number).trim()
            : undefined,

        is_primary: Boolean(contact.is_primary),
      }));

      // Helper function to only include non-empty values
      const cleanValue = (value: any) => {
        if (value === null || value === undefined || value === "") {
          return undefined;
        }
        return value;
      };

      // Transform data for API
      const supplierData = {
        // Basic Info
        name: data.name,
        supplier_type: data.supplier_type,
        website: cleanValue(data.website),
        handle: cleanValue(data.handle),
        status: data.status,

        // Business & Region Information
        timezone: cleanValue(data.timezone),
        language_preference:
          data.language_preference && data.language_preference.length > 0
            ? data.language_preference
            : undefined,

        // Financial Information
        payment_method: cleanValue(data.payment_method),
        payout_terms: cleanValue(data.payout_terms),
        tax_id: cleanValue(data.tax_id),
        default_currency: data.default_currency,
        bank_account_details: cleanValue(data.bank_account_details),

        // Categories
        categories: data.categories && data.categories.length > 0 ? data.categories : undefined,

        // Products & Services
        products_services: data.products_services && data.products_services.length > 0 ? data.products_services : undefined,

        // Address
        address: cleanValue(data.address),

        // Contacts
        contacts: transformedContacts,
      };

      // Create supplier
      const created = await createSupplier.mutateAsync(supplierData);
      // Return the created supplier id for document upload
      const supplier_id = created?.supplier?.id;
      toast.success("Supplier created successfully!");
      navigate("/supplier-management/suppliers");
      return { supplier_id };
    } catch (error: any) {
      console.error("Error creating supplier:", error);
      toast.error(error.message || "Failed to create supplier");
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleCancel = () => {
    navigate("/supplier-management/suppliers");
  };

  return (
    <>
      <PermissionBasedSidebarHider />
      <Toaster />
      {/* Form */}
      <Suspense
        fallback={
          <Container className="p-6">
            <div className="flex items-center justify-center py-12">
              <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
              <span className="ml-3 text-sm text-gray-600">Loading supplier form...</span>
            </div>
          </Container>
        }
      >
        <SupplierCreateForm
          onSubmit={handleSubmit}
          onCancel={handleCancel}
          isSubmitting={isSubmitting}
        />
      </Suspense>
    </>
  );
};

export const config = defineRouteConfig({
  label: "Create Supplier",
});

export default CreateSupplierPage;
