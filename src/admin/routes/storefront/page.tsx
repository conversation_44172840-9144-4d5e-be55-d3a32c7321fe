import { defineRouteConfig } from "@camped-ai/admin-sdk";
import {
  BroomSparkle,
  ShoppingCart,
  ArrowUpRightOnBox,
} from "@camped-ai/icons";
import {
  Container,
  Button,
  Heading,
  Skeleton,
  Toaster,
  toast,
} from "@camped-ai/ui";
import { useQuery } from "@tanstack/react-query";
import { sdk } from "../../lib/sdk";
import { useState } from "react";
import PermissionBasedSidebarHider from "../../widgets/permission-based-sidebar-hider";
import AdminPageHelmet from "../../components/AdminPageHelmet";

const StorefrontPage = () => {
  const {
    data: storeData,
    isLoading: isStoreLoading,
    isError: isStoreError,
  } = useQuery<any>({
    queryFn: () => sdk.client.fetch(`/admin/stores`, {}),
    queryKey: [["store"]],
  });

  const storeId = storeData?.stores?.[0]?.id;

  const {
    data: storeDetailsData,
    isLoading: isStoreDetailsLoading,
    isError: isStoreDetailsError,
  } = useQuery<any>({
    queryFn: () => sdk.client.fetch(`/admin/store-details/${storeId}`, {}),
    queryKey: [["store_details", storeId]],
    enabled: !!storeId,
  });

  const [loading, setLoading] = useState(false);
  const deployHookURL = import.meta.env.VITE_VERCEL_DEPLOY_HOOK_URL ?? "";

  const triggerRedeploy = async () => {
    setLoading(true);
    try {
      const response = await fetch(deployHookURL, { method: "POST" });
      if (response.ok) {
        toast.success("Cleared Cache!", {
          description: "Wait for 2 to 3 mins for the changes to take effect.",
        });
      } else {
        toast.error("Failed to clear cache.", {
          description: "An error occurred while clearing the cache.",
        });
      }
    } catch (error) {
      console.error("Error triggering redeploy:", error);
      toast.error("Failed to clear cache.", {
        description: "An error occurred while clearing the cache.",
      });
    }
    setLoading(false);
  };

  if (isStoreLoading || isStoreDetailsLoading) {
    return (
      <>
        <PermissionBasedSidebarHider />
        <Container className="flex flex-col items-center justify-start h-screen space-y-2">
          <Skeleton className="h-1 w-full p-5" />
          <Skeleton className="h-screen w-full" />
        </Container>
      </>
    );
  }

  if (isStoreError || isStoreDetailsError) {
    return (
      <>
        <PermissionBasedSidebarHider />
        <div>An error occurred while fetching data.</div>
      </>
    );
  }

  const storefrontUrl = storeDetailsData?.storeDetails?.[0]?.storefront_url;

  if (!storefrontUrl) {
    return (
      <>
        <PermissionBasedSidebarHider />
        <Container
          style={{
            display: "flex",
            flexDirection: "column",
            justifyContent: "center",
            alignItems: "center",
            textAlign: "center",
          }}
        >
          <Heading>No Storefront URL Configured</Heading>
          <p>
            Please configure your storefront settings to display the storefront.
          </p>
          <Button
            onClick={() => (window.location.href = "/app/settings/store")}
            variant="primary"
            style={{ marginTop: "16px" }}
          >
            Go to Settings
          </Button>
        </Container>
      </>
    );
  }

  return (
    <div style={{ width: "100%", height: "100vh" }}>
      <AdminPageHelmet />
      <PermissionBasedSidebarHider />
      <Toaster />

      <Container className=" h-screen p-0 m-0 ">
        <div className="flex items-center justify-between border-b px-6 py-4">
          <Heading level="h2" className="text-lg font-semibold">
            Storefront
          </Heading>
          <div className="flex justify-end space-x-4 h-8">
            <Button
              onClick={triggerRedeploy}
              isLoading={loading}
              variant="secondary"
              className="flex items-center gap-4 px-4"
            >
              {loading ? "Redeploying..." : "Clear Cache"}
              <BroomSparkle />
            </Button>
            <Button
              variant="secondary"
              className="flex items-center gap-4 px-4"
              onClick={() => window.open(storefrontUrl, "_blank")}
            >
              Open in new tab <ArrowUpRightOnBox />
            </Button>
          </div>
        </div>

        <iframe
          src={storefrontUrl}
          style={{ width: "100%", height: "100%", border: "none" }}
          title="Storefront"
        />
      </Container>
    </div>
  );
};

export const config = defineRouteConfig({
  label: "Marketing Site",
  icon: ShoppingCart,
});

export default StorefrontPage;
