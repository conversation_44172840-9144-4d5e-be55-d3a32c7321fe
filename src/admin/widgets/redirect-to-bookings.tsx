import { defineWidgetConfig } from "@camped-ai/admin-sdk";
import { useEffect } from "react";
import { useNavigate, useLocation } from "react-router-dom";

/**
 * Widget to redirect from orders and root paths to bookings
 * This ensures the app always lands on bookings instead of orders
 */
const RedirectToBookings = () => {
  const navigate = useNavigate();
  const location = useLocation();

  useEffect(() => {
    const currentPath = location.pathname;
    
    // Define paths that should redirect to bookings
    const shouldRedirect = 
      currentPath === "/" ||
      currentPath === "/app" ||
      currentPath === "/app/" ||
      currentPath === "" ||
      currentPath === "/orders" ||
      currentPath.startsWith("/orders/") ||
      currentPath === "/app/orders" ||
      currentPath.startsWith("/app/orders/");

    if (shouldRedirect) {
      console.log(`[RedirectToBookings] Redirecting from ${currentPath} to bookings`);
      navigate("/hotel-management/bookings", { replace: true });
    }
  }, [navigate, location.pathname]);

  return null;
};

export const config = defineWidgetConfig({
  zone: [
    "order.list.before",
    "order.details.before",
  ],
});

export default RedirectToBookings;
