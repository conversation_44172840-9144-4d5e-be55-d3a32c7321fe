import { defineRouteConfig } from "@camped-ai/admin-sdk";
import { Suspense } from "react";
import { Container } from "@camped-ai/ui";
import AdminPageHelmet from "../../../components/AdminPageHelmet";
import { LazyLoadErrorBoundary, createSafeLazyComponent } from "../../../components/common/lazy-load-error-boundary";

// Dynamically import page client for better performance with Router context safety
const PageClient = createSafeLazyComponent(() => import("./page-client"));

const BookingPage = () => {
  return (
    <>
      <AdminPageHelmet />
      <LazyLoadErrorBoundary fallbackMessage="Failed to load bookings page.">
        <Suspense
          fallback={
            <Container className="p-6">
              <div className="flex items-center justify-center py-12">
                <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
                <span className="ml-3 text-sm text-gray-600">Loading bookings...</span>
              </div>
            </Container>
          }
        >
          <PageClient />
        </Suspense>
      </LazyLoadErrorBoundary>
    </>
  );
};

export default BookingPage;

export const config = defineRouteConfig({
  label: "Bookings",
});
