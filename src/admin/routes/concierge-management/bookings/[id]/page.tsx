import { defineRouteConfig } from "@camped-ai/admin-sdk";
import { LazyLoadErrorBoundary, createSafeLazyComponent } from "../../../../components/common/lazy-load-error-boundary";
import { Heading, Text, Container } from "@camped-ai/ui";
import { Suspense } from "react";

import PermissionBasedSidebarHider from "../../../../widgets/permission-based-sidebar-hider";
import AdminPageHelmet from "../../../../components/AdminPageHelmet";
import { RoleGuard } from "../../../../components/rbac/RoleGuard";

// Dynamically import page client for better performance
const ConciergeBookingDetail = createSafeLazyComponent(() => import("./page-client"));

const ConciergeBookingDetailPage = () => {

  return (
    <>
      <AdminPageHelmet />
      <PermissionBasedSidebarHider />
        <RoleGuard
          requirePermission="concierge_management:view"
          fallback={
            <div className="p-8 text-center">
              <Heading level="h1">Access Denied</Heading>
              <Text className="mt-2">
                You don't have permission to view concierge booking details.
              </Text>
            </div>
          }
        >
          <Suspense
            fallback={
              <Container className="p-6">
                <div className="flex items-center justify-center py-12">
                  <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
                  <span className="ml-3 text-sm text-gray-600">Loading booking details...</span>
                </div>
              </Container>
            }
          >
            <ConciergeBookingDetail />
          </Suspense>
        </RoleGuard>
    </>
  );
};

export const config = defineRouteConfig({
  label: "Concierge Booking Details",
});

export default ConciergeBookingDetailPage;
