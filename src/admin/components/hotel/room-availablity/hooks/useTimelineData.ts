import { useState, useMemo, useEffect } from "react";
import { useQuery, useQueryClient } from "@tanstack/react-query";
import { transformAPIResponseForUI, mapAPIStatusToEnum } from "../dataTransformers";
import { generateDateSlots } from "../timeUtils";
import { format } from "date-fns";
import { cacheSettings } from "../../../../lib/react-query-config";
import { RoomInventoryStatus, RoomAvailability } from "../../types/booking";
import { useOptimizedQueryParams } from "../../../../hooks/useOptimizedQueryParams";

export const useTimelineData = (hotelId?: string) => {
  // URL parameter management
  const {
    updateParams,
    getArrayParam,
  } = useOptimizedQueryParams({ debounceMs: 300 });

  // UI State with URL synchronization
  const [selectedRoomTypesState, setSelectedRoomTypesState] = useState<string[]>([]);
  const [selectedRoomsState, setSelectedRoomsState] = useState<string[]>([]);
  const [selectedStatusesState, setSelectedStatusesState] = useState<
    RoomInventoryStatus[]
  >([]);
  const [hoveredRoomId, setHoveredRoomId] = useState<string | null>(null);

  // Initialize filters from URL on mount
  useEffect(() => {
    const roomTypesFromUrl = getArrayParam("roomTypes");
    const roomsFromUrl = getArrayParam("rooms");
    const statusesFromUrl = getArrayParam("statuses") as RoomInventoryStatus[];

    if (roomTypesFromUrl.length > 0) {
      // URL parameters now contain room configuration IDs
      setSelectedRoomTypesState(roomTypesFromUrl);
    }

    if (roomsFromUrl.length > 0) {
      // URL parameters contain room variant IDs
      setSelectedRoomsState(roomsFromUrl);
    }

    if (statusesFromUrl.length > 0) {
      setSelectedStatusesState(statusesFromUrl);
    }
  }, []);

  // Room types setter that updates both state and URL
  const setSelectedRoomTypes = (roomTypes: string[]) => {
    setSelectedRoomTypesState(roomTypes);
    updateParams({ roomTypes: roomTypes.length > 0 ? roomTypes : null });
  };

  // Rooms setter that updates both state and URL
  const setSelectedRooms = (rooms: string[]) => {
    setSelectedRoomsState(rooms);
    updateParams({ rooms: rooms.length > 0 ? rooms : null });
  };

  // Status setter that updates both state and URL
  const setSelectedStatuses = (statuses: RoomInventoryStatus[]) => {
    setSelectedStatusesState(statuses);
    updateParams({ statuses: statuses.length > 0 ? statuses : null });
  };

  // Date State - Simple initialization
  const [startDate, setStartDate] = useState<Date>(() => {
    const today = new Date();
    today.setHours(0, 0, 0, 0);
    return today;
  });

  const [endDate, setEndDate] = useState<Date>(() => {
    const today = new Date();
    today.setHours(0, 0, 0, 0);
    const defaultEndDate = new Date(today);
    defaultEndDate.setDate(defaultEndDate.getDate() + 13);
    return defaultEndDate;
  });

  const queryClient = useQueryClient();

  // Flag to prevent automatic refetch during manual refresh
  const [isManualRefresh, setIsManualRefresh] = useState(false);

  // API fetch function for TanStack Query
  const fetchHotelAvailability = async (
    fetchStartDate?: Date,
    fetchEndDate?: Date
  ) => {
    const timestamp = new Date().toISOString();

    if (!hotelId) {
      console.log(`[${timestamp}] ❌ FETCH FAILED: No hotel ID provided`);
      throw new Error("Hotel ID is required");
    }

    const formattedStartDate = format(
      fetchStartDate || startDate,
      "yyyy-MM-dd"
    );
    const formattedEndDate = format(fetchEndDate || endDate, "yyyy-MM-dd");

    const url = `/admin/hotel-management/availability?hotel_id=${hotelId}&start_date=${formattedStartDate}&end_date=${formattedEndDate}&consolidate=true`;

    console.log(`[${timestamp}] 📤 FETCHING HOTEL AVAILABILITY:`, {
      hotelId,
      startDate: formattedStartDate,
      endDate: formattedEndDate,
      url,
    });

    const response = await fetch(url);

    console.log(`[${timestamp}] 📥 FETCH RESPONSE:`, {
      status: response.status,
      statusText: response.statusText,
      ok: response.ok,
      headers: Object.fromEntries(response.headers.entries()),
    });

    if (!response.ok) {
      console.log(`[${timestamp}] ❌ FETCH FAILED:`, {
        status: response.status,
        statusText: response.statusText,
      });
      throw new Error(`Failed to fetch availability data: ${response.status}`);
    }

    const apiData = await response.json();
    console.log(`[${timestamp}] ✅ FETCH SUCCESS:`, {
      availabilityCount: apiData.availability?.length || 0,
      bookingsCount: apiData.bookings?.length || 0,
      roomsCount: apiData.rooms?.length || 0,
      roomTypesCount: apiData.room_types?.length || 0,
      dataKeys: Object.keys(apiData),
    });

    return apiData;
  };

  // TanStack Query for hotel availability data
  const {
    data,
    isLoading,
    error,
  } = useQuery({
    queryKey: [
      "hotel-availability",
      hotelId,
      format(startDate, "yyyy-MM-dd"),
      format(endDate, "yyyy-MM-dd"),
    ],
    queryFn: () => fetchHotelAvailability(),
    enabled: !!hotelId && !isManualRefresh, // Only run query when hotelId is available and not during manual refresh
    retry: 2,
    retryDelay: (attemptIndex) => Math.min(1000 * 2 ** attemptIndex, 30000),
    // Use optimized cache settings for real-time availability data
    ...cacheSettings.realTime,
    // Don't refetch on window focus for availability data to reduce server load
    refetchOnWindowFocus: false,
    refetchOnReconnect: true, // Do refetch on reconnect for fresh data
  });

  // Debug log when query data changes
  useEffect(() => {
    const timestamp = new Date().toISOString();
    console.log(`[${timestamp}] 📊 QUERY DATA CHANGED:`, {
      hotelId,
      isLoading,
      hasError: !!error,
      errorMessage: error?.message,
      hasData: !!data,
      dataKeys: data ? Object.keys(data) : [],
      availabilityCount: data?.availability?.length || 0,
      bookingsCount: data?.bookings?.length || 0,
      roomsCount: data?.rooms?.length || 0,
      queryKey: [
        "hotel-availability",
        hotelId,
        format(startDate, "yyyy-MM-dd"),
        format(endDate, "yyyy-MM-dd"),
      ],
    });
  }, [data, isLoading, error, hotelId, startDate, endDate]);

  // Manual refresh function for Apply button
  const refreshData = async (newStartDate: Date, newEndDate: Date) => {
    const timestamp = new Date().toISOString();
    console.log(`[${timestamp}] 🔄 REFRESH DATA TRIGGERED:`, {
      hotelId,
      currentStartDate: format(startDate, "yyyy-MM-dd"),
      currentEndDate: format(endDate, "yyyy-MM-dd"),
      newStartDate: format(newStartDate, "yyyy-MM-dd"),
      newEndDate: format(newEndDate, "yyyy-MM-dd"),
    });

    try {
      // Set manual refresh flag to prevent automatic query
      setIsManualRefresh(true);

      // Update the state first
      setStartDate(newStartDate);
      setEndDate(newEndDate);

      console.log(`[${timestamp}] 📝 DATE STATE UPDATED`);

      // Manually fetch with the new dates
      console.log(`[${timestamp}] 📤 MANUALLY FETCHING WITH NEW DATES`);

      const newData = await fetchHotelAvailability(newStartDate, newEndDate);

      // Update the query cache with the new data
      queryClient.setQueryData([
        "hotel-availability",
        hotelId,
        format(newStartDate, "yyyy-MM-dd"),
        format(newEndDate, "yyyy-MM-dd"),
      ], newData);

      console.log(`[${timestamp}] ✅ DATA FETCHED AND CACHE UPDATED SUCCESSFULLY`);
    } catch (error) {
      console.error(`[${timestamp}] ❌ MANUAL FETCH FAILED:`, error);
      // Fallback to invalidation if manual fetch fails
      await queryClient.invalidateQueries({
        queryKey: ["hotel-availability", hotelId],
      });
    } finally {
      // Re-enable automatic queries
      setIsManualRefresh(false);
    }
  };

  // Simple data transformation - just transform once
  const transformedData = useMemo(() => {
    if (!data) {
      // Return default empty structure when data is not available
      return {
        rooms: [],
        roomConfigs: [],
        bookings: [],
        availability: [],
        roomTypes: [],
        roomTypeConfigs: [],
      };
    }
    return transformAPIResponseForUI(data);
  }, [data]);

  const { rooms, bookings, roomTypes, roomTypeConfigs, availability } = transformedData;

  const dateSlots = useMemo(
    () => generateDateSlots(startDate, endDate),
    [startDate, endDate]
  );

  // Helper function to get all statuses for a room
  const getRoomStatuses = (roomId: string): RoomInventoryStatus[] => {
    // Find all bookings for this room within the current date range
    const roomBookings = bookings.filter(
      (booking) => booking.room_id === roomId
    );

    if (roomBookings.length === 0) {
      return [RoomInventoryStatus.AVAILABLE];
    }

    // Get all unique statuses for this room
    const roomStatuses = [
      ...new Set(roomBookings.map((booking) => booking.status)),
    ] as RoomInventoryStatus[];

    console.log(`Room ${roomId} has statuses:`, roomStatuses);
    return roomStatuses;
  };

  // Helper function to check if a room has any of the selected statuses
  const roomHasAnySelectedStatus = (
    roomId: string,
    selectedStatuses: RoomInventoryStatus[]
  ): boolean => {
    const roomStatuses = getRoomStatuses(roomId);
    const hasMatch = roomStatuses.some((status) =>
      selectedStatuses.includes(status)
    );
    console.log(
      `Room ${roomId} statuses:`,
      roomStatuses,
      "selected:",
      selectedStatuses,
      "match:",
      hasMatch
    );
    return hasMatch;
  };

  // Helper function to check if a room has available time periods within the date range
  const roomHasAvailableTimeInRange = (roomId: string): boolean => {
    // Create array of all dates in the range
    const allDates: Date[] = [];
    const currentDate = new Date(startDate);
    while (currentDate <= endDate) {
      allDates.push(new Date(currentDate));
      currentDate.setDate(currentDate.getDate() + 1);
    }

    // Check each date to see if it's available
    for (const date of allDates) {
      const status = getRoomStatusForDate(roomId, date);

      // If we find any date that's available, the room has available time
      if (status === RoomInventoryStatus.AVAILABLE) {
        return true;
      }
    }

    // No available dates found
    return false;
  };

  // Helper function to get room status for a specific date
  const getRoomStatusForDate = (roomId: string, date: Date): RoomInventoryStatus => {
    try {
      // Validate the date
      if (!date || !(date instanceof Date)) {
        return RoomInventoryStatus.UNAVAILABLE;
      }

      // Format the date
      const dateStr = format(date, "yyyy-MM-dd");

      // Find all availability records for this room and date
      // With date ranges, we need to check if the date falls within from_date and to_date
      const matchingRecords = availability?.filter((item: RoomAvailability) => {
        try {
          // Validate the room ID
          if (item.room_id !== roomId) return false;

          // If we have from_date and to_date fields (date ranges)
          if (item.from_date && item.to_date) {
            // Check if this is a 1-day period by comparing from_date and to_date
            const fromDate = new Date(item.from_date);
            const toDate = new Date(item.to_date);
            const diffInDays = Math.round(
              (toDate.getTime() - fromDate.getTime()) / (1000 * 60 * 60 * 24)
            );
            const isOneDayPeriod = diffInDays === 1;

            // Special handling for 1-day periods (where to_date is 1 day after from_date due to noon-to-noon concept)
            if (isOneDayPeriod) {
              // For 1-day periods, match the from_date exactly
              // This ensures 1-day bookings are properly displayed
              return dateStr === item.from_date;
            }

            // If not a 1-day period, check if the date falls within the range (inclusive of from_date, exclusive of to_date)
            return dateStr >= item.from_date && dateStr < item.to_date;
          }

          // No fallback needed - all availability records should have from_date and to_date
          return false;
        } catch (err) {
          // Silently handle errors
          return false;
        }
      }) || [];

      // Prioritize records: bookings/reservations > maintenance > unavailable > available
      // This ensures that if there are overlapping records, the most restrictive status wins
      const statusPriority = {
        'booked': 1,
        'reserved': 2,
        'maintenance': 3,
        'cleaning': 4,
        'unavailable': 5,
        'available': 6
      };

      const availabilityRecord = matchingRecords.sort((a: RoomAvailability, b: RoomAvailability) => {
        const priorityA = statusPriority[a.status as keyof typeof statusPriority] || 999;
        const priorityB = statusPriority[b.status as keyof typeof statusPriority] || 999;
        return priorityA - priorityB;
      })[0];

      // Return the status from the availability record, or "unavailable" if not found
      // This matches SimpleRoomCalendar behavior - only show available when explicitly set
      const status = availabilityRecord?.status || "unavailable";
      const mappedStatus = mapAPIStatusToEnum(status);

      // Debug logging for room 102 to understand the availability data
      if (roomId.includes("variant_01K1SA5JY8H8M7YQSQ0EHADQZV")) {
        console.log(`Room 102 on ${dateStr}: API status="${status}", mapped="${mappedStatus}", hasRecord=${!!availabilityRecord}, matchingRecords=${matchingRecords.length}`);
        if (availabilityRecord) {
          console.log(`  -> Selected Record: from_date="${availabilityRecord.from_date}", to_date="${availabilityRecord.to_date}", status="${availabilityRecord.status}"`);
        }
        if (matchingRecords.length > 1) {
          console.log(`  -> All matching records:`, matchingRecords.map((r: RoomAvailability) => `${r.from_date}-${r.to_date}:${r.status}`));
        }
      }

      return mappedStatus;
    } catch (err) {
      // Silently handle errors and return unavailable
      return RoomInventoryStatus.UNAVAILABLE;
    }
  };



  const filteredRooms = useMemo(() => {
    let filtered = rooms;

    // Filter by room types (using room configuration IDs)
    if (selectedRoomTypesState.length > 0) {
      filtered = filtered.filter((room) =>
        selectedRoomTypesState.includes(room.room_config_id)
      );
    }

    // Filter by specific rooms (using room variant IDs)
    if (selectedRoomsState.length > 0) {
      filtered = filtered.filter((room) =>
        selectedRoomsState.includes(room.id)
      );
    }

    // Filter by room statuses
    if (selectedStatusesState.length > 0) {
      filtered = filtered.filter((room) => {
        // Check if room has any of the selected statuses

        // Special handling for available status
        if (selectedStatusesState.includes(RoomInventoryStatus.AVAILABLE)) {
          // Check if room has available time periods in the date range
          const hasAvailableTimeInRange = roomHasAvailableTimeInRange(room.id);

          // If only "Available" is selected, show rooms with available time periods
          if (
            selectedStatusesState.length === 1 &&
            selectedStatusesState[0] === RoomInventoryStatus.AVAILABLE
          ) {
            return hasAvailableTimeInRange;
          }

          // If "Available" is selected along with other statuses
          const hasOtherSelectedStatus = roomHasAnySelectedStatus(
            room.id,
            selectedStatusesState.filter((s) => s !== RoomInventoryStatus.AVAILABLE)
          );
          return hasAvailableTimeInRange || hasOtherSelectedStatus;
        }

        // For non-available statuses, check if room has any of the selected statuses
        return roomHasAnySelectedStatus(room.id, selectedStatusesState);
      });
    }

    return filtered;
  }, [selectedRoomTypesState, selectedRoomsState, selectedStatusesState, rooms, bookings]);

  const groupedRooms = useMemo(
    () => {
      console.log("Grouping rooms:", filteredRooms.length, "rooms");
      const grouped = filteredRooms.reduce((acc, room) => {
        const configName = room.config_name || `Unknown Config (${room.room_config_id})`;
        console.log(`Room ${room.room_number} (${room.id}) -> config_name: "${configName}"`);
        if (!acc[configName]) {
          acc[configName] = [];
        }
        acc[configName].push(room);
        return acc;
      }, {} as Record<string, any[]>);

      console.log("Grouped rooms result:", Object.keys(grouped).map(key => `${key}: ${grouped[key].length} rooms`));
      return grouped;
    },
    [filteredRooms]
  );

  const weekBookings = useMemo(() => {
    let filteredBookings = bookings.filter((booking) => {
      const bookingDate = new Date(booking.checkIn);
      bookingDate.setHours(0, 0, 0, 0);
      const start = new Date(startDate);
      start.setHours(0, 0, 0, 0);
      const end = new Date(endDate);
      end.setHours(23, 59, 59, 999);
      return bookingDate >= start && bookingDate <= end;
    });

    // If status filter is active, also filter booking blocks by selected statuses
    if (selectedStatusesState.length > 0) {
      // Special handling for "Available" status
      if (selectedStatusesState.includes(RoomInventoryStatus.AVAILABLE)) {
        // If only "Available" is selected, don't show any booking blocks
        // (we want to see the green available background only)
        if (
          selectedStatusesState.length === 1 &&
          selectedStatusesState[0] === RoomInventoryStatus.AVAILABLE
        ) {
          filteredBookings = []; // Remove all booking blocks to show available periods
        } else {
          // If "Available" + other statuses are selected, show non-available booking blocks
          filteredBookings = filteredBookings.filter(
            (booking) =>
              selectedStatusesState.includes(booking.status) &&
              booking.status !== RoomInventoryStatus.AVAILABLE
          );
        }
      } else {
        // For non-available statuses, filter normally
        filteredBookings = filteredBookings.filter((booking) =>
          selectedStatusesState.includes(booking.status)
        );
      }
    }

    return filteredBookings;
  }, [startDate, endDate, bookings, selectedStatusesState]);

  // Generate continuous status blocks for room statuses (always show available blocks as background)
  const availableBlocks = useMemo(() => {
    const blocks: Array<{
      id: string;
      room_id: string;
      date: Date;
      status: RoomInventoryStatus;
      startDate?: Date;
      endDate?: Date;
      span?: number;
    }> = [];

    filteredRooms.forEach((room) => {
      // Debug logging for room 102
      if (room.id.includes("variant_01K1SA5JY8H8M7YQSQ0EHADQZV")) {
        console.log(`Processing room 102 for dates:`, dateSlots.map(d => format(d, "yyyy-MM-dd")));
      }

      // Group consecutive dates with the same status
      let currentStatus: RoomInventoryStatus | null = null;
      let currentBlockStart: Date | null = null;
      let currentBlockDates: Date[] = [];

      const finishCurrentBlock = () => {
        if (currentStatus && currentBlockStart && currentBlockDates.length > 0) {
          // Always show available blocks as background
          // For other statuses, only show if they're selected in filters
          const shouldShowBlock =
            currentStatus === RoomInventoryStatus.AVAILABLE ||
            selectedStatusesState.includes(currentStatus);

          if (shouldShowBlock) {
            // Check if any date in this block has a booking
            const hasBookingInBlock = currentBlockDates.some(date =>
              bookings.some((booking) => {
                const start = new Date(booking.checkIn);
                const end = new Date(booking.checkOut);
                return booking.room_id === room.id && date >= start && date < end;
              })
            );

            if (!hasBookingInBlock) {
              const blockEndDate = currentBlockDates[currentBlockDates.length - 1];
              // Debug logging for room 102
              if (room.room_number === "102" || room.id.includes("variant_01K1SG3Y8BMPY0GQEH4OOZC")) {
                console.log(`Adding continuous ${currentStatus} block for room ${room.room_number} from ${format(currentBlockStart, "yyyy-MM-dd")} to ${format(blockEndDate, "yyyy-MM-dd")} (${currentBlockDates.length} days)`);
              }

              blocks.push({
                id: `status-${currentStatus}-${room.id}-${currentBlockStart.toISOString()}-${blockEndDate.toISOString()}`,
                room_id: room.id,
                date: currentBlockStart, // Use start date as the primary date
                status: currentStatus,
                startDate: currentBlockStart,
                endDate: blockEndDate,
                span: currentBlockDates.length,
              });
            }
          }
        }
      };

      dateSlots.forEach((date) => {
        const status = getRoomStatusForDate(room.id, date);

        if (status === currentStatus) {
          // Continue current block
          currentBlockDates.push(date);
        } else {
          // Finish previous block and start new one
          finishCurrentBlock();

          currentStatus = status;
          currentBlockStart = date;
          currentBlockDates = [date];
        }
      });

      // Finish the last block
      finishCurrentBlock();
    });

    console.log(`Generated ${blocks.length} continuous status blocks (${blocks.filter(b => b.status === RoomInventoryStatus.AVAILABLE).length} available)`);
    return blocks;
  }, [filteredRooms, selectedStatusesState, startDate, endDate, bookings, dateSlots, availability]);

  return {
    selectedRoomTypes: selectedRoomTypesState,
    setSelectedRoomTypes,
    selectedRooms: selectedRoomsState,
    setSelectedRooms,
    selectedStatuses: selectedStatusesState,
    setSelectedStatuses,
    hoveredRoomId,
    setHoveredRoomId,

    startDate,
    setStartDate,
    endDate,
    setEndDate,
    dateSlots,
    filteredRooms,
    groupedRooms,
    weekBookings,
    availableBlocks,
    roomTypes,
    roomTypeConfigs, // Add room type configurations for ID-to-name mapping
    rooms, // Add all rooms for the filter component
    TIMELINE_WIDTH: dateSlots.length * 150 + 20, // Dynamic width based on actual date range + padding

    // API state
    isLoading,
    error: error?.message || null,
    hasRealData: !!data,
    rawApiData: data, // Provide raw API data for unallocated bookings processing

    // Manual refresh function
    refreshData,
  };
};
