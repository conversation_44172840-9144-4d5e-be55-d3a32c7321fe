import React, { useState, useMemo, useEffect } from "react";
import {
  Heading,
  Text,
  Button,
  Select,
  IconButton,
  Input,
  toast,
} from "@camped-ai/ui";
import {
  ChevronLeft,
  ChevronRight,
  Calendar,
  Edit,
} from "lucide-react";
import {
  format,
  startOfMonth,
  endOfMonth,
  eachDayOfInterval,
  addMonths,
  subMonths,
  isSameMonth,
  isToday,
  startOfWeek,
  endOfWeek,
} from "date-fns";
import {
  type PricingContext,
  type BasePriceRule,
  type SeasonalPriceRule,
  type DailyPricingInfo,
} from "../../../utils/pricing-calendar-utils";
import {
  useCalendarSeasonalPricing,
} from "../../../hooks/hotel/use-calendar-seasonal-pricing";
import {
  usePricingCalendarAvailability,
  isRoomConfigAvailableOnDate,
  getRoomConfigAvailabilityStatus,
  getAvailableRoomCount,
} from "../../../hooks/hotel/use-pricing-calendar-availability";

type RoomConfig = {
  id: string;
  title: string;
  handle?: string;
  description?: string;
};

type OccupancyConfig = {
  id: string;
  name: string;
};

type MealPlan = {
  id: string;
  name: string;
};

type PricingCalendarViewProps = {
  hotelId: string;
  roomConfigs: RoomConfig[];
  occupancyConfigs: OccupancyConfig[];
  mealPlans: MealPlan[];
  basePriceRules: BasePriceRule[];
  seasonalRules: SeasonalPriceRule[];
  currencyCode: string;
  onPriceUpdate: (
    date: Date,
    price: number,
    context: PricingContext,
    costMarginData?: {
      cost: number;
      fixedMargin: number;
      marginPercentage: number;
    }
  ) => Promise<void>;
  onRefetch?: () => void;
  canEdit?: boolean;
};

const PricingCalendarView: React.FC<PricingCalendarViewProps> = ({
  hotelId,
  roomConfigs,
  occupancyConfigs,
  mealPlans,
  basePriceRules,
  seasonalRules,
  currencyCode,
  onPriceUpdate,
  onRefetch,
  canEdit = false,
}) => {
  const [currentDate, setCurrentDate] = useState(new Date());
  const [selectedRoomConfig, setSelectedRoomConfig] = useState<string>("");
  const [selectedOccupancy, setSelectedOccupancy] = useState<string>("");
  const [selectedMealPlan, setSelectedMealPlan] = useState<string>("");

  // Set initial values when data becomes available
  useEffect(() => {
    if (roomConfigs.length > 0 && !selectedRoomConfig) {
      setSelectedRoomConfig(roomConfigs[0].id);
    }
  }, [roomConfigs, selectedRoomConfig]);

  useEffect(() => {
    if (occupancyConfigs.length > 0 && !selectedOccupancy) {
      setSelectedOccupancy(occupancyConfigs[0].id);
    }
  }, [occupancyConfigs, selectedOccupancy]);

  useEffect(() => {
    if (mealPlans.length > 0 && !selectedMealPlan) {
      setSelectedMealPlan(mealPlans[0].id);
    }
  }, [mealPlans, selectedMealPlan]);
  const [selectedDate, setSelectedDate] = useState<Date | null>(null);
  const [selectedDateRange, setSelectedDateRange] = useState<{ start: Date; end: Date } | null>(null);
  const [selectedDates, setSelectedDates] = useState<Set<string>>(new Set()); // For multi-date selection
  const [isRangeMode, setIsRangeMode] = useState<boolean>(false);
  const [isMultiSelectMode, setIsMultiSelectMode] = useState<boolean>(false);
  const [sidebarOpen, setSidebarOpen] = useState<boolean>(false);
  const [hoverDate, setHoverDate] = useState<Date | null>(null);
  const [rangeSelectionMode, setRangeSelectionMode] = useState<boolean>(false);
  const [isAltKeyHeld, setIsAltKeyHeld] = useState<boolean>(false);

  // Debug: Monitor selectedDates changes
  useEffect(() => {
    console.log('selectedDates changed:', Array.from(selectedDates));
  }, [selectedDates]);

  // Add keyboard event listeners for Alt key detection
  useEffect(() => {
    const handleKeyDown = (event: KeyboardEvent) => {
      if (event.altKey && !isAltKeyHeld) {
        setIsAltKeyHeld(true);
      }
    };

    const handleKeyUp = (event: KeyboardEvent) => {
      if (!event.altKey && isAltKeyHeld) {
        setIsAltKeyHeld(false);
      }
    };

    const handleBlur = () => {
      setIsAltKeyHeld(false);
    };

    window.addEventListener('keydown', handleKeyDown);
    window.addEventListener('keyup', handleKeyUp);
    window.addEventListener('blur', handleBlur);

    return () => {
      window.removeEventListener('keydown', handleKeyDown);
      window.removeEventListener('keyup', handleKeyUp);
      window.removeEventListener('blur', handleBlur);
    };
  }, [isAltKeyHeld]);
  const [editingCost, setEditingCost] = useState<string>("");
  const [editingFixedMargin, setEditingFixedMargin] = useState<string>("");
  const [editingMarginPercentage, setEditingMarginPercentage] = useState<string>("");

  // Get current pricing context
  const pricingContext: PricingContext = {
    roomConfigId: selectedRoomConfig,
    occupancyTypeId: selectedOccupancy,
    mealPlanId: selectedMealPlan,
  };

  // Calculate month boundaries and create proper calendar grid
  const monthStart = startOfMonth(currentDate);
  const monthEnd = endOfMonth(currentDate);

  // Create calendar grid starting with Monday (weekStartsOn: 1)
  const calendarStart = startOfWeek(monthStart, { weekStartsOn: 1 });
  const calendarEnd = endOfWeek(monthEnd, { weekStartsOn: 1 });
  const calendarDays = eachDayOfInterval({ start: calendarStart, end: calendarEnd });

  // Fetch seasonal pricing data for the calendar date range
  // Always enable the hook but pass empty roomConfigId when not available
  const { data: seasonalPricingData, refetch: refetchCalendarData } = useCalendarSeasonalPricing({
    roomConfigId: pricingContext.roomConfigId || "",
    startDate: calendarStart,
    endDate: calendarEnd,
    occupancyTypeId: pricingContext.occupancyTypeId || undefined,
    mealPlanId: pricingContext.mealPlanId || undefined,
    enabled: true, // Always enabled, but the hook will handle empty roomConfigId
  });

  // Fetch availability data for the calendar date range
  const { data: availabilityData, refetch: refetchAvailabilityData } = usePricingCalendarAvailability({
    hotelId,
    startDate: calendarStart,
    endDate: calendarEnd,
    roomConfigId: pricingContext.roomConfigId,
    enabled: !!pricingContext.roomConfigId,
  });

  // Debug logging for availability data
  React.useEffect(() => {
    if (availabilityData) {
      console.log("[Pricing Calendar] Availability data received:", {
        totalRecords: availabilityData.availability.length,
        roomConfigs: availabilityData.room_configs.length,
        sampleRecords: availabilityData.availability.slice(0, 3),
        selectedRoomConfig: pricingContext.roomConfigId,
      });
    }
  }, [availabilityData, pricingContext.roomConfigId]);



  // Calculate comprehensive pricing info for the entire month using new seasonal data
  const monthlyPricingInfo = useMemo(() => {
    const pricingMap = new Map<string, DailyPricingInfo>();

    calendarDays.forEach(date => {
      const dateKey = format(date, 'yyyy-MM-dd');

      // Check availability first - if not available, don't show pricing
      const isAvailable = availabilityData?.availability
        ? isRoomConfigAvailableOnDate(pricingContext.roomConfigId, date, availabilityData.availability)
        : true; // Default to available if no availability data (fallback to original behavior)

      // Debug logging for specific dates
      if (dateKey === "2025-08-01" || dateKey === "2025-08-15") {
        console.log(`[Pricing Calendar] Availability check for ${dateKey}:`, {
          roomConfigId: pricingContext.roomConfigId,
          isAvailable,
          availabilityRecords: availabilityData?.availability?.filter(a =>
            a.room_config_id === pricingContext.roomConfigId && a.date === dateKey
          ),
          totalAvailabilityRecords: availabilityData?.availability?.length || 0,
        });
      }

      // Get base price from base price rules
      const basePriceRule = basePriceRules.find(rule =>
        rule.room_config_id === pricingContext.roomConfigId &&
        rule.occupancy_type_id === pricingContext.occupancyTypeId &&
        rule.meal_plan_id === pricingContext.mealPlanId
      );

      if (!basePriceRule || !isAvailable) {
        pricingMap.set(dateKey, {
          date,
          cost: null,
          fixedMargin: null,
          marginPercentage: null,
          price: 0,
          isSeasonalOverride: false,
          isAvailable,
        });
        return;
      }

      const dayOfWeek = format(date, "eee").toLowerCase() as "mon" | "tue" | "wed" | "thu" | "fri" | "sat" | "sun";



      // Start with base price and cost/margin data
      let price = basePriceRule.weekday_prices[dayOfWeek] || 0;
      let cost = basePriceRule.weekday_costs?.[dayOfWeek] ?? basePriceRule.default_gross_cost ?? null;
      let fixedMargin = basePriceRule.weekday_fixed_margins?.[dayOfWeek] ?? basePriceRule.default_fixed_margin ?? null;
      let marginPercentage = basePriceRule.weekday_margin_percentages?.[dayOfWeek] ?? basePriceRule.default_margin_percentage ?? null;
      let isSeasonalOverride = false;
      let seasonalRuleName: string | undefined;

      // Check for seasonal overrides using new API data
      if (seasonalPricingData?.seasonal_rules) {
        const applicableRules = seasonalPricingData.seasonal_rules.filter(rule => {
          // Convert ISO dates to date-only strings for comparison
          const ruleStart = rule.start_date.split('T')[0]; // "2025-07-02T00:00:00.000Z" -> "2025-07-02"
          const ruleEnd = rule.end_date.split('T')[0];     // "2025-07-03T00:00:00.000Z" -> "2025-07-03"

          // Only apply seasonal pricing to the START DATE of the range
          const isStartDate = dateKey === ruleStart;
          const contextMatch = rule.base_price_rule.occupancy_type_id === pricingContext.occupancyTypeId &&
                              rule.base_price_rule.meal_plan_id === pricingContext.mealPlanId;

         

          return isStartDate && contextMatch;
        }).sort((a, b) => (b.priority || 100) - (a.priority || 100));

        if (applicableRules.length > 0) {
          const seasonalRule = applicableRules[0];

          // Only mark as seasonal override if there's actually a price for this day
          const hasWeekdayPrice = seasonalRule.weekday_prices && seasonalRule.weekday_prices[dayOfWeek] !== undefined;

          if (hasWeekdayPrice && seasonalRule.weekday_prices) {
            isSeasonalOverride = true;
            seasonalRuleName = seasonalRule.name;
            price = seasonalRule.weekday_prices[dayOfWeek];
          }



          // Use seasonal cost/margin data only if we have a seasonal override
          if (isSeasonalOverride) {
            // Reset to seasonal defaults first (this clears base pricing margins)
            // Keep values in cents - formatCurrency will handle the conversion
            cost = seasonalRule.default_values?.gross_cost || 0;
            fixedMargin = seasonalRule.default_values?.fixed_margin || 0;
            marginPercentage = seasonalRule.default_values?.margin_percentage || 0;

            // Then check for weekday-specific overrides
            if (seasonalRule.weekday_values?.[dayOfWeek]) {
              const dayValues = seasonalRule.weekday_values[dayOfWeek];
              // Check if this weekday has any meaningful cost/margin data
              const hasWeekdayData = (dayValues.gross_cost !== undefined && dayValues.gross_cost !== 0) ||
                                   (dayValues.fixed_margin !== undefined && dayValues.fixed_margin !== 0) ||
                                   (dayValues.margin_percentage !== undefined && dayValues.margin_percentage !== 0);

              if (hasWeekdayData) {
                // Override with weekday-specific values (keep in cents)
                if (dayValues.gross_cost !== undefined && dayValues.gross_cost !== 0) {
                  cost = dayValues.gross_cost;
                }
                if (dayValues.fixed_margin !== undefined && dayValues.fixed_margin !== 0) {
                  fixedMargin = dayValues.fixed_margin;
                }
                if (dayValues.margin_percentage !== undefined && dayValues.margin_percentage !== 0) {
                  marginPercentage = dayValues.margin_percentage;
                }

                console.log(`[CALENDAR] Using weekday values for ${dayOfWeek} (in cents):`, {
                  cost, fixedMargin, marginPercentage
                });
              } else {
                console.log(`[CALENDAR] Using default seasonal values for ${dayOfWeek} (in cents):`, {
                  cost, fixedMargin, marginPercentage
                });
              }
            }
          }
        }
      }


      // Get availability status for this date
      const availabilityStatus = availabilityData?.availability
        ? getRoomConfigAvailabilityStatus(pricingContext.roomConfigId, date, availabilityData.availability)
        : { isAvailable: false, availableRooms: 0, totalRooms: 0 };

      pricingMap.set(dateKey, {
        date,
        cost,
        fixedMargin,
        marginPercentage,
        price: Number(price) || 0,
        isSeasonalOverride,
        seasonalRuleName,
        isAvailable: availabilityStatus.isAvailable,
        availableRooms: availabilityStatus.availableRooms,
        totalRooms: availabilityStatus.totalRooms,
      });
    });

    return pricingMap;
  }, [calendarDays, pricingContext, basePriceRules, seasonalPricingData, availabilityData]);

  const handlePreviousMonth = () => {
    const newDate = subMonths(currentDate, 1);
    
    setCurrentDate(newDate);

    // Refetch calendar data for the new month
    setTimeout(() => {
      refetchCalendarData();
      refetchAvailabilityData();
      if (onRefetch) {
        onRefetch();
      }
    }, 100); // Small delay to ensure state is updated
  };

  const handleNextMonth = () => {
    const newDate = addMonths(currentDate, 1);
    
    setCurrentDate(newDate);

    // Refetch calendar data for the new month
    setTimeout(() => {
      refetchCalendarData();
      refetchAvailabilityData();
      if (onRefetch) {
        onRefetch();
      }
    }, 100); // Small delay to ensure state is updated
  };

  const handleToday = () => {
    const newDate = new Date();
    
    setCurrentDate(newDate);

    // Refetch calendar data for current month
    setTimeout(() => {
      refetchCalendarData();
      if (onRefetch) {
        onRefetch();
      }
    }, 100); // Small delay to ensure state is updated
  };

  // Handle filter changes with data refetching
  const handleRoomConfigChange = (roomConfigId: string) => {
    console.log(`[CALENDAR] Room config changed to ${roomConfigId} - refetching calendar data`);
    setSelectedRoomConfig(roomConfigId);

    // Refetch calendar data for new room config
    setTimeout(() => {
      refetchCalendarData();
      if (onRefetch) {
        onRefetch();
      }
    }, 100);
  };

  const handleOccupancyChange = (occupancyId: string) => {
    console.log(`[CALENDAR] Occupancy changed to ${occupancyId} - refetching calendar data`);
    setSelectedOccupancy(occupancyId);

    // Refetch calendar data for new occupancy
    setTimeout(() => {
      refetchCalendarData();
      if (onRefetch) {
        onRefetch();
      }
    }, 100);
  };

  const handleMealPlanChange = (mealPlanId: string) => {
    console.log(`[CALENDAR] Meal plan changed to ${mealPlanId} - refetching calendar data`);
    setSelectedMealPlan(mealPlanId);

    // Refetch calendar data for new meal plan
    setTimeout(() => {
      refetchCalendarData();
      if (onRefetch) {
        onRefetch();
      }
    }, 100);
  };

  const handleDateClick = (date: Date, event?: React.MouseEvent) => {
    if (!canEdit) return;

    const dateKey = format(date, 'yyyy-MM-dd');
    const pricingInfo = monthlyPricingInfo.get(dateKey);

    if (!pricingInfo) return;

    console.log('Date clicked:', dateKey, {
      altKey: event?.altKey,
      ctrlKey: event?.ctrlKey,
      metaKey: event?.metaKey,
      selectedDate: selectedDate ? format(selectedDate, 'yyyy-MM-dd') : null,
      rangeSelectionMode
    });

    // Check if Ctrl/Cmd key is held for multi-date selection
    if (event?.ctrlKey || event?.metaKey) {
      event.preventDefault(); // Prevent any default behavior
      event.stopPropagation(); // Stop event bubbling

      // Use functional state update to ensure we have the latest state
      setSelectedDates(prevSelectedDates => {
        const newSelectedDates = new Set(prevSelectedDates);

        if (newSelectedDates.has(dateKey)) {
          // Remove date if already selected
          newSelectedDates.delete(dateKey);
          console.log('Removed date from selection:', dateKey);
        } else {
          // Add date to selection
          newSelectedDates.add(dateKey);
          console.log('Added date to selection:', dateKey);
        }

        console.log('New selectedDates:', Array.from(newSelectedDates));

        // Update multi-select mode based on new selection
        setIsMultiSelectMode(newSelectedDates.size > 0);

        // Handle sidebar and form updates
        if (newSelectedDates.size > 0) {
          if (!sidebarOpen || newSelectedDates.size === 1) {
            const firstSelectedDateKey = Array.from(newSelectedDates)[0];
            const firstSelectedInfo = monthlyPricingInfo.get(firstSelectedDateKey);
            if (firstSelectedInfo) {
              setEditingCost(firstSelectedInfo.cost !== null ? (firstSelectedInfo.cost / 100).toString() : "");
              setEditingFixedMargin(firstSelectedInfo.fixedMargin !== null ? (firstSelectedInfo.fixedMargin / 100).toString() : "");
              setEditingMarginPercentage(firstSelectedInfo.marginPercentage?.toString() || "");
            }
          }
          // Always ensure sidebar is open when we have selections
          if (!sidebarOpen) {
            setSidebarOpen(true);
          }
        } else {
          // Close sidebar only if no dates are selected
          setSidebarOpen(false);
        }

        return newSelectedDates;
      });

      // Clear single date and range selections
      setSelectedDate(null);
      setSelectedDateRange(null);
      setIsRangeMode(false);
      setHoverDate(null);
      setRangeSelectionMode(false);
      return;
    }

    // Check if Alt key is held OR range selection mode is active for range selection
    if ((event?.altKey || rangeSelectionMode) && selectedDate) {
      console.log('Range selection triggered:', { altKey: event?.altKey, rangeSelectionMode, selectedDate: format(selectedDate, 'yyyy-MM-dd') });

      if (event?.altKey) {
        event.preventDefault(); // Prevent any default Alt behavior
        event.stopPropagation(); // Stop event bubbling
      }

      // Create date range
      const start = selectedDate < date ? selectedDate : date;
      const end = selectedDate < date ? date : selectedDate;
      setSelectedDateRange({ start, end });
      setIsRangeMode(true);

      // Use the pricing info from the first selected date for editing
      const firstDateKey = format(selectedDate, 'yyyy-MM-dd');
      const firstDateInfo = monthlyPricingInfo.get(firstDateKey);
      if (firstDateInfo) {
        setEditingCost(firstDateInfo.cost !== null ? (firstDateInfo.cost / 100).toString() : "");
        setEditingFixedMargin(firstDateInfo.fixedMargin !== null ? (firstDateInfo.fixedMargin / 100).toString() : "");
        setEditingMarginPercentage(firstDateInfo.marginPercentage?.toString() || "");
      }
      setSidebarOpen(true); // Open sidebar only when range is complete

      // Clear multi-select mode and turn off range selection mode
      setSelectedDates(new Set());
      setIsMultiSelectMode(false);
      setRangeSelectionMode(false);
      return; // Important: exit here to avoid the single date logic below
    }

    // Handle range mode: if clicking the same date twice, treat as single date selection
    if (rangeSelectionMode && selectedDate && format(selectedDate, 'yyyy-MM-dd') === dateKey) {
      console.log('Same date clicked twice in range mode, treating as single date selection');
      // Convert cost and margin from cents to currency units for display
      setEditingCost(pricingInfo.cost !== null ? (pricingInfo.cost / 100).toString() : "");
      setEditingFixedMargin(pricingInfo.fixedMargin !== null ? (pricingInfo.fixedMargin / 100).toString() : "");
      setEditingMarginPercentage(pricingInfo.marginPercentage?.toString() || "");
      setSidebarOpen(true);
      setRangeSelectionMode(false); // Exit range mode
      return;
    }

    // Single date selection (this happens on first click or when no modifiers are held)
    setSelectedDate(date);
    setSelectedDateRange(null);
    setIsRangeMode(false);
    setSelectedDates(new Set());
    setIsMultiSelectMode(false);
    setHoverDate(null);

    // Only open sidebar if we're NOT in range selection mode (to allow range completion)
    if (!rangeSelectionMode) {
      // Convert cost and margin from cents to currency units for display
      setEditingCost(pricingInfo.cost !== null ? (pricingInfo.cost / 100).toString() : "");
      setEditingFixedMargin(pricingInfo.fixedMargin !== null ? (pricingInfo.fixedMargin / 100).toString() : "");
      setEditingMarginPercentage(pricingInfo.marginPercentage?.toString() || "");
      setSidebarOpen(true);
    }
  };

  const handlePriceSave = async () => {
    // Use calculated price based on cost and margin
    const finalPrice = calculateRealTimePrice();

    if (isNaN(finalPrice) || finalPrice < 0) {
      toast.error("Please ensure cost and margin values are correct");
      return;
    }

    try {
      // Prepare cost and margin data (convert from currency units to cents)
      // Implement either/or logic: if one margin type is provided, reset the other to zero
      const fixedMarginValue = parseFloat(editingFixedMargin) || 0;
      const marginPercentageValue = parseFloat(editingMarginPercentage) || 0;

      const costMarginData = {
        cost: parseFloat(editingCost) * 100 || 0, // Convert to cents
        // If fixed margin is provided, use it and reset percentage to 0
        // If percentage is provided, use it and reset fixed margin to 0
        // If both are provided, fixed margin takes priority
        fixedMargin: fixedMarginValue > 0 ? fixedMarginValue * 100 : 0, // Convert to cents
        marginPercentage: fixedMarginValue > 0 ? 0 : marginPercentageValue, // Keep as percentage
      };

      // Handle multi-date selection
      if (isMultiSelectMode && selectedDates.size > 0) {
        // Update all selected dates
        const updatePromises = Array.from(selectedDates).map(dateKey => {
          const date = new Date(dateKey + 'T00:00:00'); // Convert dateKey back to Date
          return onPriceUpdate(date, finalPrice * 100, pricingContext, costMarginData);
        });

        await Promise.all(updatePromises);
        toast.success(`Prices updated for ${selectedDates.size} dates successfully`);

        // Clear multi-selection
        setSelectedDates(new Set());
        setIsMultiSelectMode(false);
      } else if (isRangeMode && selectedDateRange) {
        // Handle date range selection
        const rangeDates = eachDayOfInterval({
          start: selectedDateRange.start,
          end: selectedDateRange.end
        });

        const updatePromises = rangeDates.map(date =>
          onPriceUpdate(date, finalPrice * 100, pricingContext, costMarginData)
        );

        await Promise.all(updatePromises);
        toast.success(`Prices updated for ${rangeDates.length} dates in range successfully`);

        // Clear range selection
        setSelectedDateRange(null);
        setIsRangeMode(false);
        setSelectedDate(null);
      } else if (selectedDate) {
        // Single date update
        await onPriceUpdate(selectedDate, finalPrice * 100, pricingContext, costMarginData);
        toast.success("Price updated successfully");
        setSelectedDate(null);
      } else {
        toast.error("No dates selected");
        return;
      }

      // Trigger calendar data refresh
      await refetchCalendarData();
      if (onRefetch) {
        onRefetch();
      }

      setSidebarOpen(false);
      setEditingCost("");
      setEditingFixedMargin("");
      setEditingMarginPercentage("");
    } catch (error) {
      toast.error("Failed to update price");
      console.error("Price update error:", error);
    }
  };

  const handleSidebarClose = () => {
    console.log('handleSidebarClose called, isMultiSelectMode:', isMultiSelectMode, 'selectedDates.size:', selectedDates.size, 'rangeSelectionMode:', rangeSelectionMode, 'selectedDate:', selectedDate ? format(selectedDate, 'yyyy-MM-dd') : null);

    // Don't close sidebar if we're in multi-select mode with dates selected
    // This prevents accidental closing during multi-select operations
    if (isMultiSelectMode && selectedDates.size > 0) {
      console.log('Preventing sidebar close during multi-select');
      return;
    }

    // Don't close sidebar if we're in range selection mode and have a selected date
    // This allows the user to select the second date for the range
    // Note: In range mode, sidebar only opens after range is complete, so this is mainly for safety
    if (rangeSelectionMode && selectedDate) {
      console.log('Preventing sidebar close during range selection');
      return;
    }

    setSidebarOpen(false);
    setSelectedDate(null);
    setSelectedDates(new Set());
    setIsMultiSelectMode(false);
    setSelectedDateRange(null);
    setIsRangeMode(false);
    setHoverDate(null);
    setEditingCost("");
    setEditingFixedMargin("");
    setEditingMarginPercentage("");
  };

  // Force close sidebar (for explicit close button clicks)
  const handleSidebarForceClose = () => {
    console.log('handleSidebarForceClose called');
    setSidebarOpen(false);
    setSelectedDate(null);
    setSelectedDates(new Set());
    setIsMultiSelectMode(false);
    setSelectedDateRange(null);
    setIsRangeMode(false);
    setHoverDate(null);
    setRangeSelectionMode(false);
    setEditingCost("");
    setEditingFixedMargin("");
    setEditingMarginPercentage("");
  };





  const formatCurrency = (amount: number | null | undefined): string => {
    if (amount === null || amount === undefined || isNaN(amount)) {
      return "0.00";
    }
    // Convert from cents to currency units
    return (Number(amount) / 100).toFixed(2);
  };

  // Calculate price in real-time based on form inputs
  const calculateRealTimePrice = (): number => {
    const cost = parseFloat(editingCost) || 0;
    const fixedMargin = parseFloat(editingFixedMargin) || 0;
    const marginPercentage = parseFloat(editingMarginPercentage) || 0;

    // Use either fixed margin OR percentage margin, not both
    // Priority: if fixed margin is provided, use it; otherwise use percentage
    let calculatedPrice = cost;

    if (fixedMargin > 0) {
      // Use fixed margin (this is a fixed markup amount)
      calculatedPrice = cost + fixedMargin;
    } else if (marginPercentage > 0) {
      // Use percentage margin with correct formula: Selling Price = Cost Price / (1 - Margin %)
      if (marginPercentage >= 100) {
        console.warn(`Invalid margin percentage: ${marginPercentage}%. Margin must be less than 100%.`);
        return cost; // Return cost as fallback
      }
      calculatedPrice = cost / (1 - marginPercentage / 100);
    }

    return calculatedPrice;
  };

  const getCurrencySymbol = () => {
    // You might want to get this from a currency service
    return currencyCode === "GBP" ? "£" : currencyCode === "USD" ? "$" : "€";
  };

  // Handle case where no room configurations are available
  if (roomConfigs.length === 0) {
    return (
      <div className="space-y-6">
        <div className="text-center py-12 bg-muted rounded-lg">
          <Calendar className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
          <Text className="text-muted-foreground mb-4">
            No room configurations available. Please set up room configurations first.
          </Text>
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header with navigation */}
      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-4">
          <IconButton
            variant="transparent"
            onClick={handlePreviousMonth}
          >
            <ChevronLeft className="w-4 h-4" />
          </IconButton>

          <Heading level="h2" className="text-xl font-semibold">
            {format(currentDate, "MMMM yyyy")}
          </Heading>

          <IconButton
            variant="transparent"
            onClick={handleNextMonth}
          >
            <ChevronRight className="w-4 h-4" />
          </IconButton>
          
          <Button
            variant="secondary"
            size="small"
            onClick={handleToday}
          >
            Today
          </Button>
        </div>

        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-2">
            <Calendar className="w-4 h-4 text-muted-foreground" />
            <Text className="text-sm text-muted-foreground">
              Click dates to edit prices
            </Text>
          </div>
        </div>
      </div>

      {/* Filters */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-4 p-4 bg-muted/30 rounded-lg">
        <div>
          <label className="block text-sm font-medium text-foreground mb-2">
            Room Type
          </label>
          <Select
            value={selectedRoomConfig}
            onValueChange={handleRoomConfigChange}
          >
            <Select.Trigger>
              <Select.Value />
            </Select.Trigger>
            <Select.Content>
              {roomConfigs.map((room) => (
                <Select.Item key={room.id} value={room.id}>
                  {room.title}
                </Select.Item>
              ))}
            </Select.Content>
          </Select>
        </div>

        <div>
          <label className="block text-sm font-medium text-foreground mb-2">
            Occupancy Type
          </label>
          <Select
            value={selectedOccupancy}
            onValueChange={handleOccupancyChange}
          >
            <Select.Trigger>
              <Select.Value />
            </Select.Trigger>
            <Select.Content>
              {occupancyConfigs.map((occupancy) => (
                <Select.Item key={occupancy.id} value={occupancy.id}>
                  {occupancy.name}
                </Select.Item>
              ))}
            </Select.Content>
          </Select>
        </div>

        <div>
          <label className="block text-sm font-medium text-foreground mb-2">
            Meal Plan
          </label>
          <Select
            value={selectedMealPlan}
            onValueChange={handleMealPlanChange}
          >
            <Select.Trigger>
              <Select.Value />
            </Select.Trigger>
            <Select.Content>
              {mealPlans.map((mealPlan) => (
                <Select.Item key={mealPlan.id} value={mealPlan.id}>
                  {mealPlan.name}
                </Select.Item>
              ))}
            </Select.Content>
          </Select>
        </div>
      </div>

      {/* Instructions for multi-select */}
      {canEdit && (
        <div className={`${rangeSelectionMode ? 'bg-green-50 dark:bg-green-950/20 border-green-200 dark:border-green-800' : 'bg-blue-50 dark:bg-blue-950/20 border-blue-200 dark:border-blue-800'} border rounded-lg p-3`}>
          <div className="flex items-center justify-between gap-2">
            <div className="flex items-center gap-2">
              <svg className="w-4 h-4 text-blue-600 dark:text-blue-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
              </svg>
              <Text className={`text-sm ${rangeSelectionMode ? 'text-green-900 dark:text-green-100' : 'text-blue-900 dark:text-blue-100'}`}>
                <strong>{rangeSelectionMode ? 'Range Mode Active:' : 'Tip:'}</strong> {rangeSelectionMode ? 'Click two dates for range, same date twice for single, or use "Complete Selection" button.' : 'Single date: just click. Multiple dates: hold'} {!rangeSelectionMode && (<><kbd className="px-1 py-0.5 text-xs bg-blue-200 dark:bg-blue-800 rounded">Ctrl</kbd>/<kbd className="px-1 py-0.5 text-xs bg-blue-200 dark:bg-blue-800 rounded">Cmd</kbd> + click. Date range: <kbd className="px-1 py-0.5 text-xs bg-blue-200 dark:bg-blue-800 rounded">Alt</kbd>/<kbd className="px-1 py-0.5 text-xs bg-blue-200 dark:bg-blue-800 rounded">Option</kbd> + click or use Range Mode.</>)}
                {selectedDates.size > 0 && (
                  <span className="ml-2 font-semibold text-purple-700 dark:text-purple-300">
                    {selectedDates.size} dates selected
                  </span>
                )}
                {isRangeMode && selectedDateRange && (
                  <span className="ml-2 font-semibold text-blue-700 dark:text-blue-300">
                    Range: {format(selectedDateRange.start, "MMM dd")} - {format(selectedDateRange.end, "MMM dd")}
                  </span>
                )}
                {rangeSelectionMode && selectedDate && (
                  <span className="ml-2 font-semibold text-green-700 dark:text-green-300">
                    Selected: {format(selectedDate, "MMM dd")} - Click another date or same date again
                  </span>
                )}
              </Text>
            </div>
            <div className="flex gap-2">
              <Button
                variant={rangeSelectionMode ? "primary" : "secondary"}
                size="small"
                onClick={() => {
                  console.log('Range Mode button clicked, current state:', rangeSelectionMode);
                  setRangeSelectionMode(!rangeSelectionMode);
                  if (!rangeSelectionMode) {
                    // Clear other selections when entering range mode
                    setSelectedDates(new Set());
                    setIsMultiSelectMode(false);
                    console.log('Entering range mode, cleared other selections');
                  }
                }}
                className="text-xs"
              >
                {rangeSelectionMode ? "Range Mode ON" : "Range Mode"}
              </Button>

              {/* Complete Selection button - appears when user has selected a date in range mode */}
              {rangeSelectionMode && selectedDate && (
                <Button
                  variant="primary"
                  size="small"
                  onClick={() => {
                    console.log('Complete Selection clicked for single date');
                    // Treat as single date selection
                    const pricingInfo = monthlyPricingInfo.get(format(selectedDate, 'yyyy-MM-dd'));
                    if (pricingInfo) {
                      setEditingCost(pricingInfo.cost !== null ? (pricingInfo.cost / 100).toString() : "");
                      setEditingFixedMargin(pricingInfo.fixedMargin !== null ? (pricingInfo.fixedMargin / 100).toString() : "");
                      setEditingMarginPercentage(pricingInfo.marginPercentage?.toString() || "");
                      setSidebarOpen(true);
                      setRangeSelectionMode(false); // Exit range mode
                    }
                  }}
                  className="text-xs bg-green-600 hover:bg-green-700"
                >
                  Complete Selection
                </Button>
              )}
            </div>
          </div>
        </div>
      )}

      {/* Availability Legend */}
      {availabilityData?.availability && availabilityData.availability.length > 0 && (
        <div className="bg-muted/20 border border-border rounded-lg p-3 mb-4">
          <div className="flex items-center gap-4 text-sm">
            <div className="flex items-center gap-2">
              <div className="w-4 h-4 bg-white border border-border rounded"></div>
              <span className="text-muted-foreground">Available with pricing</span>
            </div>
            <div className="flex items-center gap-2">
              <div className="w-4 h-4 bg-red-50 dark:bg-red-950/20 border border-red-200 dark:border-red-800 rounded opacity-60"></div>
              <span className="text-muted-foreground">Not available</span>
            </div>
            <div className="flex items-center gap-2">
              <div className="w-4 h-4 bg-orange-50 dark:bg-orange-950/20 border border-orange-200 dark:border-orange-800 rounded"></div>
              <span className="text-muted-foreground">Seasonal pricing</span>
            </div>
          </div>
        </div>
      )}

      {/* Calendar Grid */}
      <div
        className={`bg-background border border-border rounded-lg overflow-hidden transition-all duration-300 select-none ${
          sidebarOpen ? 'mr-[360px]' : 'mr-0'
        }`}
        style={{ userSelect: 'none', WebkitUserSelect: 'none', MozUserSelect: 'none' }}
      >
        {/* Day headers */}
        <div className="grid grid-cols-7 border-b border-border">
          {["Mon", "Tue", "Wed", "Thu", "Fri", "Sat", "Sun"].map((day) => (
            <div
              key={day}
              className="p-3 text-center text-sm font-medium text-gray-600 dark:text-gray-400 bg-gray-50 dark:bg-gray-800"
            >
              {day}
            </div>
          ))}
        </div>

        {/* Calendar days */}
        <div className="grid grid-cols-7">
          {calendarDays.map((date) => {
            const dateKey = format(date, "yyyy-MM-dd");
            const pricingInfo = monthlyPricingInfo.get(dateKey);
            const isCurrentMonth = isSameMonth(date, currentDate);
            const isTodayDate = isToday(date);
            const isSelected = selectedDate && format(selectedDate, "yyyy-MM-dd") === dateKey;
            const isMultiSelected = selectedDates.has(dateKey);

            // Check if date is in selected range
            const isInRange = selectedDateRange &&
              date >= selectedDateRange.start &&
              date <= selectedDateRange.end;

            // Check if date is in hover preview range (when shift is held and we have a selected date)
            const isInHoverRange = selectedDate && hoverDate &&
              date >= (selectedDate < hoverDate ? selectedDate : hoverDate) &&
              date <= (selectedDate < hoverDate ? hoverDate : selectedDate);

            // Check availability status for styling
            const isAvailable = pricingInfo?.isAvailable ?? false;
            const hasAvailabilityData = availabilityData?.availability && availabilityData.availability.length > 0;

            return (
              <div
                key={dateKey}
                className={`
                  p-2 border-r border-b border-border min-h-[120px] cursor-pointer
                  hover:bg-muted/50 transition-colors relative
                  ${!isCurrentMonth ? "bg-muted/20 text-muted-foreground" : ""}
                  ${isTodayDate ? "bg-blue-50 dark:bg-blue-950/20" : ""}
                  ${canEdit && isAvailable ? "hover:bg-blue-50 dark:hover:bg-blue-950/30" : ""}
                  ${pricingInfo?.isSeasonalOverride && isAvailable ? "bg-orange-50 dark:bg-orange-950/20" : ""}
                  ${isSelected ? "bg-green-100 dark:bg-green-950/30 border-2 border-green-500" : ""}
                  ${isMultiSelected ? "bg-purple-100 dark:bg-purple-950/30 border-2 border-purple-500" : ""}
                  ${isInRange ? "bg-blue-100 dark:bg-blue-950/30 border-2 border-blue-500" : ""}
                  ${isInHoverRange ? "bg-blue-50 dark:bg-blue-950/20 border border-blue-300" : ""}
                  ${!isAvailable && hasAvailabilityData ? "bg-red-50 dark:bg-red-950/20 opacity-60" : ""}
                  ${!isAvailable && hasAvailabilityData ? "cursor-not-allowed" : ""}
                `}
                onClick={(e) => {
                  // Only allow clicking on available dates
                  if (isAvailable || !hasAvailabilityData) {
                    handleDateClick(date, e);
                  } else {
                    // Show toast message for unavailable dates
                    toast.error("Unavailable Date", {
                      description: "This room type is not available on this date. Pricing cannot be set for unavailable dates.",
                    });
                  }
                }}
                onMouseEnter={() => {
                  if (selectedDate && !isMultiSelectMode && !isRangeMode && (rangeSelectionMode || selectedDate)) {
                    setHoverDate(date);
                  }
                }}
                onMouseLeave={() => {
                  setHoverDate(null);
                }}
              >
                <div className="flex flex-col h-full">
                  <div className="flex items-center justify-between mb-1">
                    <span
                      className={`text-sm ${
                        isTodayDate ? "font-bold text-blue-600" : ""
                      }`}
                    >
                      {format(date, "d")}
                    </span>
                    <div className="flex items-center gap-1">
                      {isMultiSelected && (
                        <div className="w-4 h-4 bg-purple-500 rounded-full flex items-center justify-center">
                          <svg className="w-2.5 h-2.5 text-white" fill="currentColor" viewBox="0 0 20 20">
                            <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
                          </svg>
                        </div>
                      )}
                      {canEdit && !isMultiSelected && (
                        <Edit className="w-3 h-3 text-muted-foreground opacity-0 group-hover:opacity-100" />
                      )}
                    </div>
                  </div>

                  {/* Show pricing info only for available dates */}
                  {pricingInfo && isAvailable && (
                    <div className="flex-1 space-y-1">
                      {/* Cost - only show in edit mode */}
                      {canEdit && pricingInfo.cost !== null && (
                        <div className="text-xs">
                          <span className="text-muted-foreground">Cost: </span>
                          <span className="font-medium">
                            {getCurrencySymbol()}{formatCurrency(pricingInfo.cost)}
                          </span>
                        </div>
                      )}

                      {/* Margin - only show in edit mode */}
                      {canEdit && (pricingInfo.fixedMargin !== null || pricingInfo.marginPercentage !== null) && (
                        <div className="text-xs">
                          <span className="text-muted-foreground">Margin: </span>
                          <span className="font-medium">
                            {(() => {
                              /* Show fixed margin if it exists and is > 0, otherwise show percentage */
                              /* For existing data with both margins, fixed margin takes priority */
                              if (pricingInfo.fixedMargin !== null && pricingInfo.fixedMargin > 0) {
                                return `${getCurrencySymbol()}${formatCurrency(pricingInfo.fixedMargin)}`;
                              } else if (pricingInfo.marginPercentage !== null && pricingInfo.marginPercentage > 0) {
                                return `${pricingInfo.marginPercentage}%`;
                              } else {
                                return `${getCurrencySymbol()}0.00`;
                              }
                            })()}
                          </span>
                        </div>
                      )}

                      {/* Price */}
                      <div className="text-sm font-bold">
                        <span className="text-muted-foreground">Price: </span>
                        <span className="text-green-600 dark:text-green-400">
                          {getCurrencySymbol()}{formatCurrency(pricingInfo.price)}
                        </span>
                      </div>

                      {/* Seasonal indicator */}
                      {pricingInfo.isSeasonalOverride && (
                        <div className="text-xs text-orange-600 dark:text-orange-400">
                          {pricingInfo.seasonalRuleName || "Seasonal"}
                        </div>
                      )}

                      {/* Availability indicator */}
                      {canEdit && pricingInfo.availableRooms !== undefined && pricingInfo.totalRooms !== undefined && (
                        <div className="text-xs text-blue-600 dark:text-blue-400">
                          {pricingInfo.availableRooms}/{pricingInfo.totalRooms} rooms available
                        </div>
                      )}
                    </div>
                  )}

                  {/* Show unavailable message for unavailable dates */}
                  {pricingInfo && !isAvailable && hasAvailabilityData && (
                    <div className="flex-1 flex items-center justify-center">
                      <div className="text-center">
                        <div className="text-xs text-red-600 dark:text-red-400 font-medium">
                          Not Available
                        </div>
                        <div className="text-xs text-muted-foreground mt-1">
                          No rooms available
                        </div>
                      </div>
                    </div>
                  )}
                </div>
              </div>
            );
          })}
        </div>
      </div>

      {/* Edit Price Sidebar */}
      {sidebarOpen && (
        <div className="fixed inset-0 z-50 pointer-events-none">
          {/* Backdrop - disabled during multi-select to allow calendar interaction */}
          <div
            className={`absolute inset-0 bg-black/20 ${(isMultiSelectMode && selectedDates.size > 0) || (rangeSelectionMode && selectedDate) ? 'pointer-events-none' : 'pointer-events-auto'}`}
            onClick={(isMultiSelectMode && selectedDates.size > 0) || (rangeSelectionMode && selectedDate) ? undefined : handleSidebarClose}
          />

          {/* Sidebar - positioned from the right */}
          <div className="absolute right-0 top-0 w-[360px] bg-white dark:bg-gray-900 shadow-xl h-full overflow-y-auto flex flex-col border-l border-border pointer-events-auto">
            {/* Header */}
            <div className="flex items-center justify-between p-6 border-b border-border">
              <Heading level="h3" className="text-lg font-semibold text-gray-900 dark:text-gray-100">
                {isMultiSelectMode && selectedDates.size > 0
                  ? `Edit Pricing for ${selectedDates.size} Selected Dates`
                  : isRangeMode && selectedDateRange
                    ? `Edit Pricing for Date Range`
                    : selectedDate
                      ? `Edit Pricing for ${format(selectedDate, "MMM dd, yyyy")}`
                      : "Edit Pricing"
                }
              </Heading>
              <Button
                variant="transparent"
                onClick={handleSidebarForceClose}
                className="p-1 hover:bg-gray-100 dark:hover:bg-gray-800 rounded-md text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-200"
              >
                <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                </svg>
              </Button>
            </div>

            {/* Content */}
            <div className="flex-1 p-6 space-y-6">
              {/* Multi-select info and clear button */}
              {isMultiSelectMode && selectedDates.size > 0 && (
                <div className="bg-purple-50 dark:bg-purple-950/20 border border-purple-200 dark:border-purple-800 rounded-lg p-4">
                  <div className="flex items-center justify-between">
                    <div>
                      <Text className="text-sm font-semibold text-purple-900 dark:text-purple-100">
                        {selectedDates.size} dates selected
                      </Text>
                      <Text className="text-xs text-purple-700 dark:text-purple-300 mt-1">
                        Changes will be applied to all selected dates
                      </Text>
                    </div>
                    <Button
                      variant="secondary"
                      size="small"
                      onClick={() => {
                        setSelectedDates(new Set());
                        setIsMultiSelectMode(false);
                      }}
                      className="text-purple-700 dark:text-purple-300"
                    >
                      Clear Selection
                    </Button>
                  </div>
                </div>
              )}

              {/* Range selection info and clear button */}
              {isRangeMode && selectedDateRange && (
                <div className="bg-blue-50 dark:bg-blue-950/20 border border-blue-200 dark:border-blue-800 rounded-lg p-4">
                  <div className="flex items-center justify-between">
                    <div>
                      <Text className="text-sm font-semibold text-blue-900 dark:text-blue-100">
                        Date range selected
                      </Text>
                      <Text className="text-xs text-blue-700 dark:text-blue-300 mt-1">
                        {format(selectedDateRange.start, "MMM dd, yyyy")} - {format(selectedDateRange.end, "MMM dd, yyyy")}
                        ({eachDayOfInterval({ start: selectedDateRange.start, end: selectedDateRange.end }).length} days)
                      </Text>
                    </div>
                    <Button
                      variant="secondary"
                      size="small"
                      onClick={() => {
                        setSelectedDateRange(null);
                        setIsRangeMode(false);
                      }}
                      className="text-blue-700 dark:text-blue-300"
                    >
                      Clear Range
                    </Button>
                  </div>
                </div>
              )}

              {/* Current pricing info */}
              {selectedDate && (
                <div className="bg-blue-50 dark:bg-blue-950/20 border border-blue-200 dark:border-blue-800 rounded-lg p-4">
                  <div className="flex items-center gap-2 mb-3">
                    <svg className="w-4 h-4 text-blue-600 dark:text-blue-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                    </svg>
                    <Text className="text-sm font-semibold text-blue-900 dark:text-blue-100">Current Pricing</Text>
                  </div>
                  {(() => {
                    // Show real-time values from form inputs if available, otherwise show stored values
                    const dateKey = format(selectedDate, 'yyyy-MM-dd');
                    const currentInfo = monthlyPricingInfo.get(dateKey);

                    if (!currentInfo) {
                      return null;
                    }

                    // Use form values if they exist, otherwise fall back to stored values
                    const displayCost = editingCost ? parseFloat(editingCost) : (currentInfo.cost ? currentInfo.cost / 100 : null);
                    const displayFixedMargin = editingFixedMargin ? parseFloat(editingFixedMargin) : (currentInfo.fixedMargin ? currentInfo.fixedMargin / 100 : null);
                    const displayMarginPercentage = editingMarginPercentage ? parseFloat(editingMarginPercentage) : currentInfo.marginPercentage;
                    const displayPrice = calculateRealTimePrice();

                    return (
                      <div className="space-y-2 text-sm">
                        {/* Cost - only show in edit mode */}
                        {canEdit && displayCost !== null && (
                          <div className="flex justify-between">
                            <span className="text-gray-600 dark:text-gray-400">Cost:</span>
                            <span className="font-medium text-gray-900 dark:text-gray-100">{getCurrencySymbol()}{displayCost.toFixed(2)}</span>
                          </div>
                        )}
                        {/* Fixed Markup - only show in edit mode */}
                        {canEdit && displayFixedMargin !== null && (
                          <div className="flex justify-between">
                            <span className="text-gray-600 dark:text-gray-400">Fixed Markup:</span>
                            <span className="font-medium text-gray-900 dark:text-gray-100">{getCurrencySymbol()}{displayFixedMargin.toFixed(2)}</span>
                          </div>
                        )}
                        {/* Margin Percentage - only show in edit mode */}
                        {canEdit && displayMarginPercentage !== null && (
                          <div className="flex justify-between">
                            <span className="text-gray-600 dark:text-gray-400">Margin %:</span>
                            <span className="font-medium text-gray-900 dark:text-gray-100">{displayMarginPercentage}%</span>
                          </div>
                        )}
                        <div className={`flex justify-between ${canEdit ? 'pt-2 border-t border-blue-200 dark:border-blue-800' : ''}`}>
                          <span className="font-semibold text-gray-900 dark:text-gray-100">Current Price:</span>
                          <span className="font-bold text-green-600 dark:text-green-400">
                            {getCurrencySymbol()}{displayPrice.toFixed(2)}
                          </span>
                        </div>
                        {currentInfo.isSeasonalOverride && (
                          <div className="mt-2 px-2 py-1 bg-orange-100 dark:bg-orange-900/30 text-orange-700 dark:text-orange-300 text-xs rounded">
                            {currentInfo.seasonalRuleName || "Seasonal Override"}
                          </div>
                        )}
                      </div>
                    );
                  })()}
                </div>
              )}

              {/* Edit form */}
              <div className="space-y-6">
                <div>
                  <Text className="text-base font-semibold mb-4 block text-gray-900 dark:text-gray-100">
                    Update Pricing Components
                  </Text>

                  <div className="space-y-4">
                    <div>
                      <label className="block text-sm font-medium text-gray-900 dark:text-gray-100 mb-2">
                        Cost ({getCurrencySymbol()})
                      </label>
                      <Input
                        type="number"
                        step="0.01"
                        min="0"
                        value={editingCost}
                        onChange={(e) => setEditingCost(e.target.value)}
                        placeholder="Enter cost"
                        className="w-full"
                      />
                      <Text className="text-xs text-gray-600 dark:text-gray-400 mt-1">
                        Base cost for this room/service
                      </Text>
                    </div>

                    <div>
                      <label className="block text-sm font-medium text-gray-900 dark:text-gray-100 mb-2">
                        Fixed Markup ({getCurrencySymbol()})
                        {parseFloat(editingFixedMargin) > 0 && (
                          <span className="ml-2 text-xs bg-blue-100 dark:bg-blue-900 text-blue-700 dark:text-blue-300 px-2 py-1 rounded">
                            Active
                          </span>
                        )}
                      </label>
                      <Input
                        type="number"
                        step="0.01"
                        min="0"
                        value={editingFixedMargin}
                        onChange={(e) => {
                          setEditingFixedMargin(e.target.value);
                          // If user enters a fixed markup, clear percentage margin
                          if (parseFloat(e.target.value) > 0) {
                            setEditingMarginPercentage("");
                          }
                        }}
                        placeholder="Enter fixed markup"
                        className="w-full"
                      />
                      <Text className="text-xs text-gray-600 dark:text-gray-400 mt-1">
                        Fixed amount added to cost (clears percentage when entered)
                      </Text>
                    </div>

                    <div>
                      <label className="block text-sm font-medium text-gray-900 dark:text-gray-100 mb-2">
                        Margin Percentage (%)
                        {parseFloat(editingMarginPercentage) > 0 && parseFloat(editingFixedMargin) === 0 && (
                          <span className="ml-2 text-xs bg-blue-100 dark:bg-blue-900 text-blue-700 dark:text-blue-300 px-2 py-1 rounded">
                            Active
                          </span>
                        )}
                        {parseFloat(editingMarginPercentage) > 0 && parseFloat(editingFixedMargin) > 0 && (
                          <span className="ml-2 text-xs bg-orange-100 dark:bg-orange-900 text-orange-700 dark:text-orange-300 px-2 py-1 rounded">
                            Ignored (Fixed Markup takes priority)
                          </span>
                        )}
                      </label>
                      <Input
                        type="number"
                        step="0.1"
                        min="0"
                        max="100"
                        value={editingMarginPercentage}
                        onChange={(e) => {
                          setEditingMarginPercentage(e.target.value);
                          // If user enters a percentage margin, clear fixed markup
                          if (parseFloat(e.target.value) > 0) {
                            setEditingFixedMargin("");
                          }
                        }}
                        placeholder="Enter margin percentage"
                        className="w-full"
                      />
                      <Text className="text-xs text-gray-600 dark:text-gray-400 mt-1">
                        Percentage markup on cost (clears fixed markup when entered)
                      </Text>
                    </div>

                    {/* Calculated Price Display */}
                    <div className="pt-2 border-t border-gray-200 dark:border-gray-700">
                      <div className="bg-green-50 dark:bg-green-950/20 border border-green-200 dark:border-green-800 rounded-lg p-3 mb-4">
                        <div className="flex items-center gap-2 mb-2">
                          <svg className="w-4 h-4 text-green-600 dark:text-green-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 7h6m0 10v-3m-3 3h.01M9 17h.01M9 14h.01M12 14h.01M15 11h.01M12 11h.01M9 11h.01M7 21h10a2 2 0 002-2V5a2 2 0 00-2-2H7a2 2 0 00-2 2v14a2 2 0 002 2z" />
                          </svg>
                          <Text className="text-sm font-semibold text-green-900 dark:text-green-100">Calculated Price</Text>
                        </div>
                        <div className="text-lg font-bold text-green-600 dark:text-green-400">
                          {getCurrencySymbol()}{calculateRealTimePrice().toFixed(2)}
                        </div>
                        <Text className="text-xs text-green-700 dark:text-green-300 mt-1">
                          Cost + (Fixed Markup OR Margin %)
                        </Text>
                        <Text className="text-xs text-green-600 dark:text-green-400 mt-1 italic">
                          Use either Fixed Markup or Margin %, not both
                        </Text>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>

            {/* Actions - Fixed at bottom */}
            <div className="p-6 border-t border-gray-200 dark:border-gray-700 bg-gray-50 dark:bg-gray-800">
              <div className="flex justify-end space-x-3">
                <Button
                  variant="transparent"
                  onClick={handleSidebarClose}
                  className="px-4 py-2 text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700"
                >
                  Cancel
                </Button>
                <Button
                  variant="primary"
                  onClick={handlePriceSave}
                  className="px-4 py-2"
                >
                  Save Price
                </Button>
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default PricingCalendarView;
