import { MedusaRequest, MedusaResponse } from "@camped-ai/framework/http"
import { DESTINATION_MODULE } from "src/modules/hotel-management/destination"

export async function POST(
  req: MedusaRequest,
  res: MedusaResponse
) {
  try {
    const destinationId = req.params.id
    //@ts-ignore
    const { image_id } = req.body

    console.log("Thumbnail API called:", { destinationId, image_id });

    // Validate that image_id is provided
    if (!image_id || image_id.trim() === "") {
      console.error("Invalid image_id:", image_id);
      return res.status(400).json({
        type: "invalid_data",
        message: "image_id is required and cannot be empty"
      });
    }

    const destinationService = req.scope.resolve(DESTINATION_MODULE)
    const thumbnailImage = await destinationService.setDestinationThumbnail(destinationId, image_id)

    console.log("Thumbnail set successfully:", thumbnailImage);
    res.status(200).json({ thumbnail: thumbnailImage })
  } catch (error) {
    console.error("Error setting thumbnail:", error);
    res.status(500).json({
      type: "error",
      message: error.message || "Failed to set thumbnail"
    });
  }
}