import React, { useEffect } from "react";
import { DatePicker, Text, Button } from "@camped-ai/ui";
import { MultiSelect } from "../../common/MultiSelect";
import { StatusBadgeSelector } from "./StatusBadgeSelector";
import { ROOM_STATUSES } from "./constants";

interface ConfigureTabContentProps {
  // Room selection
  selectedRoomConfigs: string[];
  setSelectedRoomConfigs: (configs: string[]) => void;
  selectedBulkRooms: string[];
  setSelectedBulkRooms: (rooms: string[]) => void;

  // Date selection
  bulkStartDate: Date;
  setBulkStartDate: (date: Date) => void;
  bulkEndDate: Date;
  setBulkEndDate: (date: Date) => void;

  // Status and notes
  selectedStatus: any;
  setSelectedStatus: (status: any) => void;
  bulkUpdateNotes: string;
  setBulkUpdateNotes: (notes: string) => void;

  // Data
  groupedRooms: Record<string, any[]>;
  rooms: any[];

  // Actions
  handleBulkUpdate: () => void;

  // UI state
  updateMessage: { type: "success" | "error" | "warning"; text: string } | null;
  isUpdating: boolean;

  // Permissions
  hasEditPermission?: boolean;
}

export const ConfigureTabContent: React.FC<ConfigureTabContentProps> = ({
  selectedRoomConfigs,
  setSelectedRoomConfigs,
  selectedBulkRooms,
  setSelectedBulkRooms,
  bulkStartDate,
  setBulkStartDate,
  bulkEndDate,
  setBulkEndDate,
  selectedStatus,
  setSelectedStatus,
  bulkUpdateNotes,
  setBulkUpdateNotes,
  groupedRooms,
  rooms,
  handleBulkUpdate,
  updateMessage,
  isUpdating,
  hasEditPermission = true,
}) => {
  // Prepare room configuration options
  const roomConfigOptions = Object.keys(groupedRooms).map((configName) => ({
    value: configName,
    label: `${configName} (${groupedRooms[configName].length} rooms)`,
  }));

  // Filter rooms based on selected room configurations
  const filteredRooms =
    selectedRoomConfigs.length > 0
      ? rooms.filter((room) => selectedRoomConfigs.includes(room.config_name))
      : rooms;

  // Prepare individual room options with proper room name and config name
  const roomOptions = filteredRooms.map((room) => ({
    value: room.id,
    label: `${room.name || room.title} (${room.config_name})`,
  }));

  // Clear selected rooms when room configurations change
  useEffect(() => {
    if (selectedRoomConfigs.length > 0) {
      // Filter out rooms that are no longer in the selected configurations
      const validRoomIds = filteredRooms.map((room) => room.id);
      const updatedSelectedRooms = selectedBulkRooms.filter((roomId) =>
        validRoomIds.includes(roomId)
      );

      if (updatedSelectedRooms.length !== selectedBulkRooms.length) {
        setSelectedBulkRooms(updatedSelectedRooms);
      }
    }
  }, [
    selectedRoomConfigs,
    filteredRooms,
    selectedBulkRooms,
    setSelectedBulkRooms,
  ]);

  // Handle smart date validation
  const handleStartDateChange = (date: Date | null) => {
    if (date) {
      setBulkStartDate(date);
      // If start date is after end date, adjust end date
      if (bulkEndDate && date > bulkEndDate) {
        const newEndDate = new Date(date);
        newEndDate.setDate(date.getDate() + 1);
        setBulkEndDate(newEndDate);
      }
    }
  };

  const handleEndDateChange = (date: Date | null) => {
    if (date) {
      setBulkEndDate(date);
      // If end date is before start date, adjust start date
      if (bulkStartDate && date < bulkStartDate) {
        const newStartDate = new Date(date);
        newStartDate.setDate(date.getDate() - 1);
        setBulkStartDate(newStartDate);
      }
    }
  };

  const canUpdate =
    (selectedRoomConfigs.length > 0 || selectedBulkRooms.length > 0) &&
    bulkStartDate &&
    bulkEndDate &&
    selectedStatus &&
    hasEditPermission;

  // Show read-only view for users without edit permissions
  if (!hasEditPermission) {
    return (
      <div className="space-y-6 p-6">
        <div className="bg-ui-bg-subtle border border-ui-border-base rounded-lg p-4">
          <div className="flex items-center gap-2 mb-2">
            <div className="h-2 w-2 bg-yellow-500 rounded-full"></div>
            <Text size="base" weight="plus" family="sans">
              View-Only Mode
            </Text>
          </div>
          <Text size="small" className="text-ui-fg-subtle">
            You have read-only access to room availability. To make changes, you need rooms:edit or rooms:availability permissions.
          </Text>
        </div>

        <div className="space-y-4">
          <div>
            <Text size="base" weight="plus" family="sans" className="mb-2">
              Room Summary
            </Text>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div className="bg-ui-bg-base border border-ui-border-base rounded-lg p-3">
                <Text size="small" weight="plus" className="text-ui-fg-subtle mb-1">
                  Total Room Configurations
                </Text>
                <Text size="base" weight="plus">
                  {Object.keys(groupedRooms).length}
                </Text>
              </div>
              <div className="bg-ui-bg-base border border-ui-border-base rounded-lg p-3">
                <Text size="small" weight="plus" className="text-ui-fg-subtle mb-1">
                  Total Rooms
                </Text>
                <Text size="base" weight="plus">
                  {rooms.length}
                </Text>
              </div>
            </div>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Update Message */}
      {updateMessage && (
        <div
          className={`p-4 rounded-md ${
            updateMessage.type === "success"
              ? "bg-green-50 dark:bg-green-900/20 text-green-800 dark:text-green-200 border border-green-200 dark:border-green-800"
              : updateMessage.type === "warning"
              ? "bg-yellow-50 dark:bg-yellow-900/20 text-yellow-800 dark:text-yellow-200 border border-yellow-200 dark:border-yellow-800"
              : "bg-red-50 dark:bg-red-900/20 text-red-800 dark:text-red-200 border border-red-200 dark:border-red-800"
          }`}
        >
          {updateMessage.text}
        </div>
      )}

      {/* Room Selection */}
      <div className="space-y-4">
        <Text size="large" weight="plus" family="sans">
          Select Rooms
        </Text>

        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          {/* Room Configurations */}
          <div>
            <div className="mb-2">
              <Text size="base" weight="plus" family="sans">
                Room Configurations
              </Text>
            </div>
            <MultiSelect
              options={roomConfigOptions}
              selectedValues={selectedRoomConfigs}
              onChange={setSelectedRoomConfigs}
              placeholder="Select room configurations..."
              showSelectAll={true}
              showSelectedTags={true}
              maxHeight="max-h-40"
            />
          </div>

          {/* Individual Rooms */}
          <div>
            <div className="mb-2">
              <Text size="base" weight="plus" family="sans">
                Specific Rooms
              </Text>
            </div>
            <MultiSelect
              options={roomOptions}
              selectedValues={selectedBulkRooms}
              onChange={setSelectedBulkRooms}
              placeholder="Select specific rooms..."
              showSelectAll={true}
              showSelectedTags={true}
              maxHeight="max-h-40"
            />
          </div>
        </div>
      </div>

      {/* Date Range */}
      <div className="space-y-4">
        <Text size="large" weight="plus" family="sans">
          Date Range
        </Text>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div>
            <div className="mb-2">
              <Text size="base" weight="plus" family="sans">
                Start Date
              </Text>
            </div>
            <DatePicker
              value={bulkStartDate}
              onChange={handleStartDateChange}
            />
          </div>
          <div>
            <div className="mb-2">
              <Text size="base" weight="plus" family="sans">
                End Date
              </Text>
            </div>
            <DatePicker
              value={bulkEndDate}
              onChange={handleEndDateChange}
              minValue={bulkStartDate}
            />
          </div>
        </div>
      </div>

      {/* Status Selection */}
      <div>
        <div className="mb-2">
          <Text size="base" weight="plus" family="sans">
            New Status
          </Text>
        </div>
        <StatusBadgeSelector
          selectedStatus={selectedStatus}
          onStatusChange={setSelectedStatus}
          options={ROOM_STATUSES}
          disabled={isUpdating}
        />
      </div>

      {/* Notes - Full Width */}
      <div>
        <div className="mb-2">
          <Text size="base" weight="plus" family="sans">
            Notes (Optional)
          </Text>
        </div>
        <textarea
          value={bulkUpdateNotes}
          onChange={(e) => setBulkUpdateNotes(e.target.value)}
          placeholder="Add notes about this update..."
          className="w-full min-h-[80px] px-3 py-2 border border-input rounded-md shadow-sm bg-background text-foreground placeholder:text-muted-foreground focus:outline-none focus:ring-2 focus:ring-primary focus:border-primary resize-vertical"
          rows={3}
        />
        <div className="mt-1">
          <Text size="base" weight="regular" family="sans">
            These notes will be saved with the availability update for audit
            purposes.
          </Text>
        </div>
      </div>

      {/* Update Button */}
      <div className="pt-4 border-t border-border flex justify-end">
        <Button
          onClick={handleBulkUpdate}
          disabled={!canUpdate || isUpdating}
          variant="primary"
          size="small"
        >
          {isUpdating ? "Updating..." : "Update Availability"}
        </Button>
      </div>
    </div>
  );
};

// Export types for reuse
export type { ConfigureTabContentProps };
