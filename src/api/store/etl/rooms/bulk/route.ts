import {
  MedusaRequest,
  MedusaResponse,
} from "@camped-ai/framework/http";
import { ContainerRegistrationKeys } from "@camped-ai/framework/utils";
import { z } from "zod";
import { CreateRoomWorkflow } from "../../../../../workflows/hotel-management/room/create-room";

// Validation schema for creating a single room via ETL
const StoreCreateRoomSchema = z.object({
  name: z.string().min(1, "Name is required"),
  room_number: z.string().min(1, "Room number is required"),
  status: z.enum(["available", "occupied", "maintenance", "cleaning"]).default("available"),
  floor: z.string().min(1, "Floor is required"),
  notes: z.string().optional(),
  is_active: z.boolean().default(true),
  // Room relationships (using room numbers for ETL)
  left_room: z.string().optional(),
  opposite_room: z.string().optional(),
  connected_room: z.string().optional(),
  right_room: z.string().optional(),
  // References
  room_config_id: z.string().min(1, "Room config ID is required"),
  hotel_id: z.string().min(1, "Hotel ID is required"),
  // Optional pricing
  price: z.number().min(0).optional(),
  currency_code: z.string().optional(),
  // Availability type
  availability_type: z.enum(["standard", "on_demand"]).default("standard"),
  // Additional Options (room-level overrides)
  max_extra_beds: z.number().min(0).max(5).optional(),
  max_cots: z.number().min(0).max(3).optional(),
  max_adults_beyond_capacity: z.number().min(0).max(5).optional(),
  // ETL specific flat fields
  salesforce_id: z.string().optional(),
  import_batch: z.string().optional(),
  room_view: z.string().optional(),
  has_balcony: z.boolean().optional(),
});

// Validation schema for bulk room creation
const StoreBulkCreateRoomsSchema = z.object({
  rooms: z.array(StoreCreateRoomSchema).min(1, "At least one room is required").max(100, "Maximum 100 rooms per batch"),
});

export type StoreCreateRoomType = z.infer<typeof StoreCreateRoomSchema>;
export type StoreBulkCreateRoomsType = z.infer<typeof StoreBulkCreateRoomsSchema>;

/**
 * POST /store/etl/rooms/bulk
 * 
 * Create multiple rooms via ETL in a single transaction
 */
export const POST = async (
  req: MedusaRequest<StoreBulkCreateRoomsType>,
  res: MedusaResponse
) => {
  try {
    // Validate request body
    const validationResult = StoreBulkCreateRoomsSchema.safeParse(req.body);
    if (!validationResult.success) {
      return res.status(400).json({
        success: false,
        message: "Invalid bulk room data",
        errors: validationResult.error.errors,
      });
    }

    const { rooms } = validationResult.data;
    const query = req.scope.resolve(ContainerRegistrationKeys.QUERY);

    // Prepare results tracking
    const results: Array<{
      index: number;
      success: boolean;
      data?: any;
      message?: string;
      errors?: any[];
    }> = [];

    let created = 0;
    let skipped = 0;
    let failed = 0;

    // Get all hotels for validation
    const { data: allHotels } = await query.graph({
      entity: "hotel",
      fields: ["id", "name"],
    });

    const hotelMap = new Map(
      allHotels?.map(hotel => [hotel.id, hotel]) || []
    );

    // Get all room configs (products) for validation
    const { data: allRoomConfigs } = await query.graph({
      entity: "product",
      fields: ["id", "title", "metadata"],
    });

    const roomConfigMap = new Map(
      allRoomConfigs?.filter(product => product.metadata?.hotel_id)
        .map(product => [product.id, product]) || []
    );

    // Get existing rooms to check for duplicates
    const { data: existingRooms } = await query.graph({
      entity: "product_variant",
      fields: ["id", "title", "metadata", "product_id"],
    });

    // Process each room
    for (let i = 0; i < rooms.length; i++) {
      const roomData = rooms[i];
      
      try {
        // Check if hotel exists
        const hotel = hotelMap.get(roomData.hotel_id);
        if (!hotel) {
          results.push({
            index: i,
            success: false,
            message: `Hotel with ID '${roomData.hotel_id}' not found`,
            errors: [`Invalid hotel_id: ${roomData.hotel_id}`],
          });
          failed++;
          continue;
        }

        // Check if room config exists
        const roomConfig = roomConfigMap.get(roomData.room_config_id);
        if (!roomConfig) {
          results.push({
            index: i,
            success: false,
            message: `Room config with ID '${roomData.room_config_id}' not found`,
            errors: [`Invalid room_config_id: ${roomData.room_config_id}`],
          });
          failed++;
          continue;
        }

        // Verify room config belongs to the specified hotel
        if (roomConfig.metadata?.hotel_id !== roomData.hotel_id) {
          results.push({
            index: i,
            success: false,
            message: `Room config '${roomData.room_config_id}' does not belong to hotel '${roomData.hotel_id}'`,
            errors: [`Room config hotel mismatch`],
          });
          failed++;
          continue;
        }

        // Check if room with same room number already exists for this hotel
        const existingRoom = existingRooms?.find(room => 
          room.metadata?.room_number === roomData.room_number &&
          room.metadata?.hotel_id === roomData.hotel_id
        );

        if (existingRoom) {
          // Skip if already exists
          results.push({
            index: i,
            success: true,
            data: {
              id: existingRoom.id,
              name: existingRoom.title,
              room_number: existingRoom.metadata?.room_number,
              hotel_id: existingRoom.metadata?.hotel_id,
            },
            message: `Room with number '${roomData.room_number}' already exists for this hotel - skipped`,
          });
          skipped++;
          continue;
        }

        // Prepare additional metadata for ETL tracking
        const additionalMetadata: Record<string, any> = {
          created_via: "etl_bulk",
          created_at: new Date().toISOString(),
          batch_index: i,
        };

        // Add flat ETL fields to metadata
        if (roomData.salesforce_id) additionalMetadata.salesforce_id = roomData.salesforce_id;
        if (roomData.import_batch) additionalMetadata.import_batch = roomData.import_batch;
        if (roomData.room_view) additionalMetadata.room_view = roomData.room_view;
        if (roomData.has_balcony !== undefined) additionalMetadata.has_balcony = roomData.has_balcony;

        // Create room using workflow
        const { result: room } = await CreateRoomWorkflow(req.scope).run({
          input: {
            name: roomData.name,
            room_number: roomData.room_number,
            status: roomData.status,
            floor: roomData.floor,
            notes: roomData.notes,
            is_active: roomData.is_active,
            left_room: roomData.left_room,
            opposite_room: roomData.opposite_room,
            connected_room: roomData.connected_room,
            right_room: roomData.right_room,
            room_config_id: roomData.room_config_id,
            hotel_id: roomData.hotel_id,
            price: roomData.price,
            currency_code: roomData.currency_code,
            availability_type: roomData.availability_type,
            // Additional Options (room-level overrides)
            max_extra_beds: roomData.max_extra_beds,
            max_cots: roomData.max_cots,
            max_adults_beyond_capacity: roomData.max_adults_beyond_capacity,
            additional_metadata: additionalMetadata,
          },
        });

        results.push({
          index: i,
          success: true,
          data: room,
          message: "Room created successfully",
        });
        created++;

        // Add to existing rooms to prevent duplicates within the same batch
        existingRooms?.push({
          id: room.id,
          title: room.name,
          metadata: {
            room_number: room.room_number,
            hotel_id: room.hotel_id,
          },
          product_id: room.room_config_id,
        } as any);

      } catch (error) {
        console.error(`Error creating room at index ${i}:`, error);
        results.push({
          index: i,
          success: false,
          message: `Failed to create room: ${error instanceof Error ? error.message : "Unknown error"}`,
          errors: [error instanceof Error ? error.message : "Unknown error"],
        });
        failed++;
      }
    }

    const summary = {
      total: rooms.length,
      created,
      skipped,
      failed,
    };

    return res.status(created > 0 || skipped > 0 ? 201 : 400).json({
      success: failed === 0,
      message: `Bulk room creation completed. Created: ${created}, Skipped: ${skipped}, Failed: ${failed}`,
      summary,
      results,
    });

  } catch (error) {
    console.error("Error in bulk room creation:", error);
    return res.status(500).json({
      success: false,
      message: "Failed to process bulk room creation",
      error: error instanceof Error ? error.message : "Unknown error",
    });
  }
};
