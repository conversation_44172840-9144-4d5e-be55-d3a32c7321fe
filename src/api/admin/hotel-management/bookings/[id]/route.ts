import { MedusaRequest, MedusaResponse } from "@camped-ai/framework/http";
import { Modules } from "@camped-ai/framework/utils";
import { BOOKING_ADD_ONS_MODULE } from "../../../../../modules/booking-add-ons";

export async function GET(req: MedusaRequest, res: MedusaResponse) {
  try {
    // Get booking ID from params
    const { id } = req.params;
    console.log(`Fetching booking details for ID: ${id}`);
    const orderService = req.scope.resolve(Modules.ORDER);
    const booking = await orderService.retrieveOrder(id, {
      relations: ["metadata"],
    });

    // Log the booking data for debugging
    console.log(`Retrieved booking ${id}:`, JSON.stringify(booking, null, 2));

    // Ensure metadata is properly formatted
    if (booking.metadata) {
      console.log("Original metadata type:", typeof booking.metadata);

      // If metadata is a string, try to parse it
      if (typeof booking.metadata === "string") {
        try {
          booking.metadata = JSON.parse(booking.metadata);
          console.log("Parsed metadata from string");
        } catch (e) {
          console.error("Failed to parse metadata string:", e);
        }
      }
    }

    // Fetch booking add-ons
    let addOns = [];
    try {
      const bookingAddOnService = req.scope.resolve(BOOKING_ADD_ONS_MODULE);
      const addOnsResult = await bookingAddOnService.getBookingAddOns(id);
      addOns = addOnsResult.booking_add_ons;
      console.log(`✅ Found ${addOns.length} add-ons for booking ${id}`);
    } catch (addOnError) {
      console.error("❌ Error fetching add-ons:", addOnError);
      // Don't fail the entire request if add-ons can't be fetched
    }

    res.json({
      booking,
      add_ons: addOns,
    });
  } catch (error) {
    console.error("Error fetching booking details:", error);
    res.status(400).json({
      message: error instanceof Error ? error.message : "Failed to get booking",
    });
  }
}

export async function PUT(req: MedusaRequest, res: MedusaResponse) {
  try {
    const { id } = req.params;
    const body = req.body as {
      guest_name?: string;
      guest_email?: string;
      guest_phone?: string;
      number_of_guests?: number;
      special_requests?: string;
      notes?: string;
      metadata?: Record<string, any>;
    };

    // Get the order service
    const orderService = req.scope.resolve(Modules.ORDER);

    // Get the current booking
    const booking = await orderService.retrieveOrder(id);

    // Prepare updated metadata
    const updatedMetadata = {
      ...booking.metadata,
      guest_name: body.guest_name || booking.metadata?.guest_name,
      guest_email: body.guest_email || booking.metadata?.guest_email,
      guest_phone: body.guest_phone || booking.metadata?.guest_phone,
      number_of_guests:
        body.number_of_guests || booking.metadata?.number_of_guests,
      special_requests:
        body.special_requests || booking.metadata?.special_requests,
      notes: body.notes || booking.metadata?.notes,
      ...(body.metadata || {}),
    };

    // Update booking
    const updatedBooking = await orderService.updateOrders(id, {
      metadata: updatedMetadata,
    });

    res.json({
      booking: updatedBooking,
    });
  } catch (error) {
    console.error("Error updating booking:", error);
    res.status(400).json({
      message:
        error instanceof Error ? error.message : "Failed to update booking",
    });
  }
}

export async function DELETE(req: MedusaRequest, res: MedusaResponse) {
  try {
    const orderService = req.scope.resolve(Modules.ORDER);
    const roomInventoryService = req.scope.resolve("roomInventoryService");

    const { id } = req.params;
    // We're not using these fields currently, but they might be needed in the future
    // const body = req.body as { reason?: string; cancelled_by?: string };

    // Get booking before cancellation
    const booking = await orderService.retrieveOrder(id);

    // Cancel booking
    const cancelledBooking = await orderService.cancel(id);

    // Update room inventory to mark as available
    try {
      // First check if we have room_reservation in metadata
      const reservations = booking.metadata?.reservations as any;
      if (Array.isArray(reservations)) {
        console.log("Admin cancelling reservations:", reservations);

        for (const reservation of reservations) {
          try {
            // Update room inventory to available using the reservation data
            await roomInventoryService.updateInventoryStatus(
              reservation.room_id,
              new Date(reservation.from_date),
              new Date(reservation.to_date),
              "available",
              `Booking ${id} cancelled by admin`,
              null, // bookingInfo
              null, // expiresAt
              null, // Clear the order_id
              null // No cart_id
            );
          } catch (inventoryError) {
            console.error("Error updating room inventory:", inventoryError);
            // Continue even if inventory update fails
          }
        }
      }
    } catch (inventoryError) {
      console.error("Failed to update inventory:", inventoryError);
      // Continue with the response even if inventory update fails
    }

    // Return the response with the cancelled booking
    res.json({
      booking: cancelledBooking,
      success: true,
      message: "Booking cancelled successfully",
      // Include a flag to indicate that availability should be refreshed
      refresh_availability: true,
    });
  } catch (error) {
    res.status(400).json({
      message:
        error instanceof Error ? error.message : "Failed to cancel booking",
    });
  }
}
