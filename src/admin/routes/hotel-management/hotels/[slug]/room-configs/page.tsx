import { lazy, Suspense } from "react";
import { Container } from "@camped-ai/ui";
import { defineRouteConfig } from "@camped-ai/admin-sdk";
import { Bed } from "lucide-react";
import "../modal-fix.css"; // Import custom CSS to fix z-index issues with modals
import "../../../../../styles/room-config-modal-fix.css"; // Import custom CSS to fix scrolling issues

// Dynamically import page client for better performance
const PageClient = lazy(() => import("./page-client"));

const MainPage = () => {
  return (
    <Suspense
      fallback={
        <Container className="p-6">
          <div className="flex items-center justify-center py-12">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
            <span className="ml-3 text-sm text-gray-600">Loading Hotel...</span>
          </div>
        </Container>
      }
    >
      <PageClient />
    </Suspense>
  );
};

export const config = defineRouteConfig({
  label: "Room Configurations",
  icon: Bed,
});

export default MainPage;
