import { MedusaRequest, MedusaResponse } from "@camped-ai/framework/http";
import { CreateRoomWorkflow } from "src/workflows/hotel-management/room/create-room";
import { z } from "zod";
import { Modules } from "@camped-ai/framework/utils";
import { enhanceRoomsWithAdditionalOptions } from "./utils/resolve-additional-options";

// Validation schema for creating a room
export const PostAdminCreateRoom = z.object({
  name: z.string(),
  room_number: z.string(),
  status: z.enum(["available", "occupied", "maintenance", "cleaning"]),
  floor: z.string(),
  notes: z.string().optional(),
  is_active: z.boolean().default(true),
  // Room relationships
  left_room: z.string().optional(),
  opposite_room: z.string().optional(),
  connected_room: z.string().optional(),
  right_room: z.string().optional(),
  // References
  room_config_id: z.string(),
  hotel_id: z.string(),
  price: z.number().optional(),
  currency_code: z.string().optional(),
  // Additional Options (room-level overrides)
  max_extra_beds: z.number().min(0).max(5).optional(),
  max_cots: z.number().min(0).max(3).optional(),
  max_adults_beyond_capacity: z.number().min(0).max(5).optional(),
});

export type PostAdminCreateRoomType = z.infer<typeof PostAdminCreateRoom>;

// Validation schema for updating a room
export const PostAdminUpdateRoom = z.object({
  id: z.string(),
  name: z.string().optional(),
  room_number: z.string().optional(),
  status: z.enum(["available", "occupied", "maintenance", "cleaning"]).optional(),
  floor: z.string().optional(),
  notes: z.string().optional(),
  is_active: z.boolean().optional(),
  // Room relationships
  left_room: z.string().optional(),
  opposite_room: z.string().optional(),
  connected_room: z.string().optional(),
  right_room: z.string().optional(),
  // Additional Options (room-level overrides)
  max_extra_beds: z.number().min(0).max(5).optional(),
  max_cots: z.number().min(0).max(3).optional(),
  max_adults_beyond_capacity: z.number().min(0).max(5).optional(),
});

export type PostAdminUpdateRoomType = z.infer<typeof PostAdminUpdateRoom>;

// GET endpoint to list rooms
export const GET = async (req: MedusaRequest, res: MedusaResponse) => {
  const query = req.scope.resolve("query");
  const { room_config_id, hotel_id } = req.query;

  // Build filters based on query parameters
  const filters: Record<string, any> = {};

  if (room_config_id) {
    filters["metadata.room_config_id"] = room_config_id;
  }

  if (hotel_id) {
    filters.categories = [hotel_id];
  }

  // First, get products that match our filters
  const { data: products } = await query.graph({
    entity: "product",
    filters,
    fields: ["id", "title", "metadata"],
  });

  if (!products || products.length === 0) {
    return res.json({ rooms: [] });
  }

  // Get all product variants for these products
  const productIds = products.map(p => p.id);
  const { data: variants } = await query.graph({
    entity: "product_variant",
    filters: { product_id: productIds },
    fields: ["id", "title", "product_id", "metadata"],
  });

  // Map variants to room objects
  const rooms = variants.map(variant => {
    const product = products.find(p => p.id === variant.product_id);

    return {
      id: variant.id,
      name: variant.title,
      room_number: variant.metadata?.room_number || "",
      status: variant.metadata?.status || "available",
      floor: variant.metadata?.floor || "",
      notes: variant.metadata?.notes || "",
      is_active: variant.metadata?.is_active !== false,
      left_room: variant.metadata?.left_room || "",
      opposite_room: variant.metadata?.opposite_room || "",
      connected_room: variant.metadata?.connected_room || "",
      right_room: variant.metadata?.right_room || "",
      room_config_id: product?.metadata?.room_config_id || "",
      hotel_id: product?.categories?.[0] || "",
      variant, // Include variant data for additional options resolution
    };
  });

  // Enhance rooms with additional options (with fallback to room type)
  const enhancedRooms = await enhanceRoomsWithAdditionalOptions(req.scope, rooms);

  res.json({ rooms: enhancedRooms });
};

// POST endpoint to create a room
export const POST = async (req: MedusaRequest<PostAdminCreateRoomType>, res: MedusaResponse) => {
  try {
    const { result } = await CreateRoomWorkflow(req.scope).run({
      input: req.body,
    });

    res.json({ room: result });
  } catch (error) {
    res.status(400).json({
      message: error instanceof Error ? error.message : "Failed to create room",
    });
  }
};

// PUT endpoint to update a room
export const PUT = async (req: MedusaRequest<PostAdminUpdateRoomType>, res: MedusaResponse) => {
  try {
    const { id, ...updateData } = req.body;

    // Get the product variant
    const productModuleService = req.scope.resolve(Modules.PRODUCT);
    const variant = await productModuleService.retrieveProductVariant(id);

    if (!variant) {
      return res.status(404).json({ message: "Room not found" });
    }

    // Update the variant metadata
    const updatedMetadata = { ...variant.metadata };

    if (updateData.room_number) updatedMetadata.room_number = updateData.room_number;
    if (updateData.floor) updatedMetadata.floor = updateData.floor;
    if (updateData.notes !== undefined) updatedMetadata.notes = updateData.notes;
    if (updateData.status) updatedMetadata.status = updateData.status;
    if (updateData.is_active !== undefined) updatedMetadata.is_active = updateData.is_active;
    if (updateData.left_room !== undefined) updatedMetadata.left_room = updateData.left_room;
    if (updateData.opposite_room !== undefined) updatedMetadata.opposite_room = updateData.opposite_room;
    if (updateData.connected_room !== undefined) updatedMetadata.connected_room = updateData.connected_room;
    if (updateData.right_room !== undefined) updatedMetadata.right_room = updateData.right_room;

    // Update additional options (room-level overrides)
    if (updateData.max_extra_beds !== undefined) updatedMetadata.max_extra_beds = updateData.max_extra_beds;
    if (updateData.max_cots !== undefined) updatedMetadata.max_cots = updateData.max_cots;
    if (updateData.max_adults_beyond_capacity !== undefined) updatedMetadata.max_adults_beyond_capacity = updateData.max_adults_beyond_capacity;

    // Update the variant
    const updatedVariant = await productModuleService.updateProductVariants({
      id,
      title: updateData.name,
      metadata: updatedMetadata
    });

    // Get the product to access room_config_id
    const product = await productModuleService.retrieveProduct(variant.product_id, {
      relations: ["categories"]
    });

    // Map the updated variant to a room object
    const room = {
      id: updatedVariant.id,
      name: updatedVariant.title,
      room_number: updatedVariant.metadata?.room_number || "",
      status: updatedVariant.metadata?.status || "available",
      floor: updatedVariant.metadata?.floor || "",
      notes: updatedVariant.metadata?.notes || "",
      is_active: updatedVariant.metadata?.is_active !== false,
      left_room: updatedVariant.metadata?.left_room || "",
      opposite_room: updatedVariant.metadata?.opposite_room || "",
      connected_room: updatedVariant.metadata?.connected_room || "",
      right_room: updatedVariant.metadata?.right_room || "",
      room_config_id: product?.metadata?.room_config_id || "",
      hotel_id: product?.categories?.[0] || "",
      variant: updatedVariant, // Include variant data for additional options resolution
    };

    // Enhance room with additional options (with fallback to room type)
    const [enhancedRoom] = await enhanceRoomsWithAdditionalOptions(req.scope, [room]);

    res.json({ room: enhancedRoom });
  } catch (error) {
    res.status(400).json({
      message: error instanceof Error ? error.message : "Failed to update room",
    });
  }
};

// DELETE endpoint to delete a room
export const DELETE = async (req: MedusaRequest, res: MedusaResponse) => {
  try {
    const { ids } = req.body;

    if (!ids) {
      return res.status(400).json({ message: "Room IDs are required" });
    }

    const roomIds = ids.split(",");
    const productModuleService = req.scope.resolve(Modules.PRODUCT);

    // Delete each variant (room)
    for (const id of roomIds) {
      await productModuleService.deleteProductVariants(id);
    }

    res.json({ success: true, ids: roomIds });
  } catch (error) {
    res.status(400).json({
      message: error instanceof Error ? error.message : "Failed to delete room",
    });
  }
};
