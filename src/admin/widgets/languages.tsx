import { defineWidgetConfig } from "@camped-ai/admin-sdk";
import {
  Container,
  Heading,
  Text,
  Badge,
  Skeleton,
  Alert,
} from "@camped-ai/ui";
import { useProjectLanguages } from "../hooks/languages/useProjectLanguages";


const InternationalizationWidget = () => {

  // Fetch languages from Tolgee
  const { languages: tolgeeLanguages, loading: languagesLoading, error: languagesError } = useProjectLanguages();

  // Fallback mock data if Tolgee is not available
  const mockLanguages = [
    {
      id: 1488911118,
      name: "English",
      tag: "en",
      originalName: "English",
      flagEmoji: "🇬🇧",
      base: true
    },
    {
      id: 1488911116,
      name: "German",
      tag: "de",
      originalName: "Deutsch",
      flagEmoji: "🇩🇪",
      base: false
    },
    {
      id: 1488911117,
      name: "Japanese",
      tag: "ja",
      originalName: "日本語",
      flagEmoji: "🇯🇵",
      base: false
    }
  ];

  // Use Tolgee data if available, otherwise use mock data
  const availableLanguagesData = tolgeeLanguages.length > 0 ? tolgeeLanguages : mockLanguages;





  // Handle loading state
  if (languagesLoading) {
    return (
      <Container className="p-6">
        <Skeleton className="mb-4 h-10" />
        <Skeleton className="mb-2 h-5" />
        <Skeleton className="mb-2 h-5" />
      </Container>
    );
  }

  // Handle error state
  if (languagesError) {
    return (
      <Container className="p-4">
        <Alert variant="error">
          Failed to load languages: {languagesError.message}
        </Alert>
      </Container>
    );
  }



  return (
    <Container className="divide-y p-0">
      <div className="flex items-center justify-between px-6 py-4">
        <div className="flex items-center gap-2">
          <Heading level="h2">Languages</Heading>
        </div>
      </div>

      <div className="px-6 py-6 space-y-6">
        <div className="flex flex-wrap gap-4">
          {availableLanguagesData.map((language) => (
            <Badge
              key={language.tag}
              className={`inline-flex items-center gap-2 px-3 py-1.5 text-sm font-medium rounded-full ${language.base
                ? "bg-green-100 text-green-800 border border-green-300"
                : "bg-gray-100 text-gray-800"
                }`}
            >
              <span className="text-base">{language.flagEmoji}</span>
              {language.name}
              {language.base && (
                <span className="text-xs bg-green-200 px-1 rounded">BASE</span>
              )}
            </Badge>
          ))}
        </div>
      </div>
    </Container>
  );
};

export const config = defineWidgetConfig({
  zone: "store.details.after",
});

export default InternationalizationWidget;
