import { Migration } from '@mikro-orm/migrations';

/**
 * Migration to change valid_from and valid_to columns from timestamptz to date
 * 
 * This migration changes the column types to store only dates without time components:
 * - valid_from: timestamptz -> date
 * - valid_to: timestamptz -> date
 */
export class Migration20250805130000 extends Migration {

  override async up(): Promise<void> {
    // Change valid_from and valid_to columns from timestamptz to date
    this.addSql(`
      ALTER TABLE "product_service" 
      ALTER COLUMN "valid_from" TYPE date USING valid_from::date,
      ALTER COLUMN "valid_to" TYPE date USING valid_to::date;
    `);
  }

  override async down(): Promise<void> {
    // Revert back to timestamptz
    this.addSql(`
      ALTER TABLE "product_service" 
      ALTER COLUMN "valid_from" TYPE timestamptz USING valid_from::timestamptz,
      ALTER COLUMN "valid_to" TYPE timestamptz USING valid_to::timestamptz;
    `);
  }
}
