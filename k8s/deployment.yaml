apiVersion: apps/v1
kind: Deployment
metadata:
  name: powderbyrneprod-backend-v2
spec:
  replicas: 1
  selector:
    matchLabels:
      app: powderbyrneprod-backend-v2
  template:
    metadata:
      labels:
        app: powderbyrneprod-backend-v2
    spec:
      containers:
        - name: powderbyrneprod-backend-v2
          image: powderbyrneprod.azurecr.io/powderbyrneprod-backend:latest
          imagePullPolicy: Always
          ports:
            - containerPort: 9000
          env:
            - name: DATABASE_URL
              value: 'postgres://medusa:@<EMAIL>:5432/powderbyrne_prod?sslmode=require'
            - name: PORT
              value: '9000'
            - name: ADMIN_CORS
              value: 'http://localhost:8000,https://localhost:3000'
            - name: AUTH_CORS
              value: 'https://dev-perfect-piste.flinkk.io,https://perfectpiste-3.flinkk.io/'
            - name: MEDUSA_ADMIN_BACKEND_URL
              value: 'https://powderbyrne.ops.flinkk.io'
            - name: MEDUSA_BACKEND_URL
              value: 'https://powderbyrne.ops.flinkk.io'
            - name: MEDUSA_ADMIN_ONBOARDING_TYPE
              value: 'nextjs'
            - name: MEDUSA_ADMIN_ONBOARDING_NEXTJS_DIRECTORY
              value: 'my-medusa-store-storefront'
            - name: STORE_CORS
              value: 'https://perfectpiste-editor.shop.flinkk.io,https://perfectpiste.flinkk.io/,https://perfectpiste.shop.flinkk.io,https://perfectpiste-3.flinkk.io,https://plugins.storyblok.com,https://e-commerce-storefront-v2-astro.vercel.app,http://localhost:8000,https://localhost:3000,https://dev-perfect-piste.flinkk.io'
            - name: NODE_ENV
              value: 'production'
            - name: NPM_CONFIG_PRODUCTION
              value: 'false'
            - name: JWT_SECRET
              value: 'camped'
            - name: COOKIE_SECRET
              value: 'medusa'
            - name: PROJECT_NAME
              value: 'powderbyrne-prod'
            - name: VITE_VERCEL_DEPLOY_HOOK_URL
              value: 'https://api.vercel.com/v1/integrations/deploy/prj_v7jgAYRErnmihxWZz7DwEn2wk4Hn/RlOz2XfVer'
            - name: SMTP_PASS
              valueFrom:
                secretKeyRef:
                  name: shop-powderbyrneprod-secrets
                  key: SMTP_PASS
            - name: MAIL_URL
              value: 'https://mail.flinkk.io/api/client/email/send'
            - name: RAZORPAY_ID
              value: 'rzp_test_YOSJOK3VtsSBWc'
            - name: RAZORPAY_SECRET
              valueFrom:
                secretKeyRef:
                  name: shop-powderbyrneprod-secrets
                  key: RAZORPAY_SECRET
            - name: RAZORPAY_ACCOUNT
              value: 'IP4WH1EHNnt1nz'
            - name: RAZORPAY_WEBHOOK_SECRET
              valueFrom:
                secretKeyRef:
                  name: shop-powderbyrneprod-secrets
                  key: RAZORPAY_WEBHOOK_SECRET
            - name: S3_FILE_URL
              value: 'https://assets.store.flinkk.io'
            - name: S3_ACCESS_KEY_ID
              valueFrom:
                secretKeyRef:
                  name: shop-powderbyrneprod-secrets
                  key: S3_ACCESS_KEY_ID
            - name: S3_SECRET_ACCESS_KEY
              valueFrom:
                secretKeyRef:
                  name: shop-powderbyrneprod-secrets
                  key: S3_SECRET_ACCESS_KEY
            - name: S3_REGION
              value: 'ap-south-1'
            - name: S3_BUCKET
              value: 'flinkk-store-prod'
            - name: S3_ENDPOINT
              value: 'https://s3.ap-south-1.amazonaws.com'
            - name: MEDUSA_STOREFRONT_URL
              value: 'https://perfectpiste-3.flinkk.io/'
            - name: STRIPE_API_KEY
              valueFrom:
                secretKeyRef:
                  name: shop-powderbyrneprod-secrets
                  key: STRIPE_API_KEY
            - name: STRIPE_WEBHOOK_SECRET_KEY
              valueFrom:
                secretKeyRef:
                  name: shop-powderbyrneprod-secrets
                  key: STRIPE_WEBHOOK_SECRET_KEY
            - name: VITE_MEDUSA_BACKEND_URL
              value: 'https://powderbyrne.ops.flinkk.io'
            - name: VITE_USER_MANUAL_URL
              value: 'https://docs.shop.flinkk.io'
            - name: GA4_PROPERTY_ID
              value: '*********'
            - name: GA4_TYPE
              value: 'service_account'
            - name: GA4_PROJECT_ID
              value: 'oval-terrain-393310'
            - name: GA4_PRIVATE_KEY_ID
              value: 'acfea5fe57536944114503daca917b54013f8676'
            - name: GA4_PRIVATE_KEY
***************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************
            - name: GA4_CLIENT_EMAIL
              value: '<EMAIL>'
            - name: GA4_CLIENT_ID
              value: '115419411138167670842'
            - name: GA4_AUTH_URI
              value: 'https://accounts.google.com/o/oauth2/auth'
            - name: GA4_TOKEN_URI
              value: 'https://oauth2.googleapis.com/token'
            - name: WHATSAPP_PHONE_NUMBER_ID
              value: '***************'
            - name: WHATSAPP_ACCESS_TOKEN
              value: 'EAAJNpS4Shg0BOzZCawMJqVetRZCtRr1D1ZCY856ESvaD2HZAm2l4U5IwgFOWkVh3sYgSOrWNaFeX4VHRVxAv0mVYkBIdIOCR8nj4n6eKzKedhbDwQUbRYtWw5Jb3HgiPbCRAiUIWeQI6V6ZBTUEfvUV3COVTCk46d8koQ1hdpc4VlSG2YpOgghMy8BLJUHAZDZD'
            - name: WHATSAPP_BUSINESS_ACCOUNT_ID
              value: '***************'
            - name: WHATSAPP_WEBHOOK_VERIFY_TOKEN
              value: 'camped'
            - name: WHATSAPP_WEBHOOK_SECRET
              value: "camped"
            - name: AZURE_OPENAI_DEPLOYMENT
              value: "camped"
            - name: AZURE_OPENAI_API_KEY
              valueFrom:
                secretKeyRef:
                  name: shop-powderbyrneprod-secrets
                  key: AZURE_OPENAI_API_KEY
            - name: AZURE_OPENAI_API_VERSION
              value: "2024-08-01-preview"
            - name: AZURE_OPENAI_API_ENDPOINT
              value: "https://camped-gpt4o.openai.azure.com"
            - name: AZURE_OPENAI_MODEL
              value: "gpt-4o"
            - name: VITE_TOLGEE_BASE_URL
              value: "https://app.tolgee.io"
            - name: VITE_TOLGEE_PROJECT_ID
              value: "19149"
            - name: VITE_TOLGEE_API_KEY
              valueFrom:
                secretKeyRef:
                  name: shop-powderbyrneprod-secrets
                  key: VITE_TOLGEE_API_KEY

          readinessProbe:
            httpGet:
              path: /health
              port: 9000
            initialDelaySeconds: 60
            periodSeconds: 15
            failureThreshold: 5
            timeoutSeconds: 5