import { AuthenticatedMedusaRequest, MedusaResponse } from "@camped-ai/framework/http";
import { ContainerRegistrationKeys, Modules } from "@camped-ai/framework/utils";
import { RBAC_MODULE } from "../../../../../modules/rbac";
import RbacModuleService from "../../../../../modules/rbac/service";
import { UserRole } from "../../../../../modules/rbac/types";

/**
 * GET /admin/hotel-management/hotels/list-basic
 * Get basic hotel information for dropdowns and selection lists
 * Returns only essential fields: id, name, destination_id
 */
export const GET = async (
  req: AuthenticatedMedusaRequest,
  res: MedusaResponse
) => {
  try {
    const query = req.scope.resolve(ContainerRegistrationKeys.QUERY);
    const rbacService: RbacModuleService = req.scope.resolve(RBAC_MODULE);
    const userService = req.scope.resolve(Modules.USER);

    const { limit = 100, offset = 0, destination_id } = req.query || {};
    const filters: Record<string, any> = {};

    // Filter by destination if specified
    if (destination_id) {
      filters.destination_id = destination_id;
    }

    // Get current user with RBAC data
    let userWithRole = null;
    if (req.auth_context?.actor_id) {
      try {
        userWithRole = await userService.retrieveUser(req.auth_context.actor_id);
      } catch (error) {
        console.warn("Could not load user RBAC data:", error);
      }
    }

    // Get basic hotel data
    const {
      data: allHotels,
      metadata: { count: totalCount },
    } = await query.graph({
      entity: "hotel",
      fields: [
        "id",
        "name", 
        "destination_id",
        "is_active"
      ],
      filters: {
        ...filters,
        is_active: true, // Only show active hotels
      },
      pagination: {
        skip: Number(offset),
        take: Number(limit),
      },
    });

    // Filter hotels based on user permissions
    let filteredHotels = allHotels;
    let filteredCount = totalCount;

    if (userWithRole) {
      const rbacData = userWithRole.metadata?.rbac as any;
      
      // All users with valid roles can see all hotels
      // Access control is now permission-based, not hotel-based
      if (rbacData?.role !== UserRole.ADMIN) {
        // Non-admin users see all hotels but with permission-based operations
        filteredHotels = allHotels;
        filteredCount = allHotels.length;
      }
    }

    // Return minimal data structure
    const basicHotels = filteredHotels.map((hotel: any) => ({
      id: hotel.id,
      name: hotel.name,
      destination_id: hotel.destination_id,
    }));

    res.json({
      hotels: basicHotels,
      count: filteredCount,
      limit: Number(limit),
      offset: Number(offset),
    });
  } catch (error) {
    console.error("Error in basic hotel listing:", error);
    res.status(500).json({
      type: "server_error",
      message: "Failed to list hotels",
    });
  }
};
