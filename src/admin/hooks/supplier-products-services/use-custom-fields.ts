import { useQuery, useMutation, useQueryClient } from "@tanstack/react-query";
import { useAdminClient } from "../use-admin-client";
import { toast } from "@camped-ai/ui";

export interface CustomField {
  id: string;
  name: string;
  field_type: "dropdown" | "text" | "number" | "date" | "time" | "time-range" | "date-range" | "boolean" | "addons";
  options?: string[];
  is_required: boolean;
  default_value?: string;
  validation_rules?: Record<string, any>;
  field_context: "supplier" | "customer";
  is_active: boolean;
  created_at: string;
  updated_at: string;
}

export interface CreateCustomFieldInput {
  name: string;
  field_type: "dropdown" | "text" | "number" | "date" | "time" | "time-range" | "date-range" | "boolean" | "addons";
  options?: string[];
  is_required?: boolean;
  default_value?: string;
  validation_rules?: Record<string, any>;
  field_context?: "supplier" | "customer";
  is_active?: boolean;
}

export interface UpdateCustomFieldInput {
  name?: string;
  field_type?: "dropdown" | "text" | "number" | "date" | "time" | "time-range" | "date-range" | "boolean" | "addons";
  options?: string[];
  is_required?: boolean;
  default_value?: string;
  validation_rules?: Record<string, any>;
  field_context?: "supplier" | "customer";
  is_active?: boolean;
}

export interface CustomFieldFilters {
  name?: string;
  field_type?: "dropdown" | "text" | "number" | "date" | "boolean";
  field_context?: "supplier" | "customer";
  is_active?: boolean;
  limit?: number;
  offset?: number;
}

export interface CustomFieldListResponse {
  custom_fields: CustomField[];
  count: number;
  limit: number;
  offset: number;
}

const QUERY_KEYS = {
  all: ["supplier-products-services", "custom-fields"] as const,
  lists: () => [...QUERY_KEYS.all, "list"] as const,
  list: (filters: CustomFieldFilters) =>
    [...QUERY_KEYS.lists(), filters] as const,
  details: () => [...QUERY_KEYS.all, "detail"] as const,
  detail: (id: string) => [...QUERY_KEYS.details(), id] as const,
};

export const useCustomFields = (filters: CustomFieldFilters = {}) => {
  const client = useAdminClient();

  return useQuery({
    queryKey: QUERY_KEYS.list(filters),
    queryFn: async (): Promise<CustomFieldListResponse> => {
      const params = new URLSearchParams();

      if (filters.name) params.append("name", filters.name);
      if (filters.field_type) params.append("field_type", filters.field_type);
      if (filters.field_context)
        params.append("field_context", filters.field_context);
      if (filters.is_active !== undefined)
        params.append("is_active", filters.is_active.toString());
      if (filters.limit) params.append("limit", filters.limit.toString());
      if (filters.offset) params.append("offset", filters.offset.toString());

      const response = await client.client.fetch(
        `/admin/supplier-management/products-services/custom-fields?${params.toString()}`
      );

      if (!response.ok) {
        throw new Error("Failed to fetch custom fields");
      }

      return response.json();
    },
  });
};

export const useCustomField = (id: string) => {
  const client = useAdminClient();

  return useQuery({
    queryKey: QUERY_KEYS.detail(id),
    queryFn: async (): Promise<{ custom_field: CustomField }> => {
      const response = await client.client.fetch(
        `/admin/supplier-management/products-services/custom-fields/${id}`
      );

      if (!response.ok) {
        throw new Error("Failed to fetch custom field");
      }

      return response.json();
    },
    enabled: !!id,
  });
};

export const useCreateCustomField = () => {
  const client = useAdminClient();
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: async (
      data: CreateCustomFieldInput
    ): Promise<{ custom_field: CustomField }> => {
      const response = await client.client.fetch(
        "/admin/supplier-management/products-services/custom-fields",
        {
          method: "POST",
          body: data,
        }
      );

      if (!response.ok) {
        const error = await response.json();
        throw new Error(error.message || "Failed to create custom field");
      }

      return response.json();
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: QUERY_KEYS.lists() });
      toast.success("Custom field created successfully");
    },
    onError: (error: Error) => {
      toast.error(error.message);
    },
  });
};

export const useUpdateCustomField = () => {
  const client = useAdminClient();
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: async ({
      id,
      data,
    }: {
      id: string;
      data: UpdateCustomFieldInput;
    }): Promise<{ custom_field: CustomField }> => {
      const response = await client.client.fetch(
        `/admin/supplier-management/products-services/custom-fields/${id}`,
        {
          method: "PUT",
          body: data,
        }
      );

      if (!response.ok) {
        const error = await response.json();
        throw new Error(error.message || "Failed to update custom field");
      }

      return response.json();
    },
    onSuccess: (_, { id }) => {
      queryClient.invalidateQueries({ queryKey: QUERY_KEYS.lists() });
      queryClient.invalidateQueries({ queryKey: QUERY_KEYS.detail(id) });
      toast.success("Custom field updated successfully");
    },
    onError: (error: Error) => {
      toast.error(error.message);
    },
  });
};

export const useDeleteCustomField = () => {
  const client = useAdminClient();
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: async (
      id: string
    ): Promise<{ deleted: boolean; id: string }> => {
      const response = await client.client.fetch(
        `/admin/supplier-management/products-services/custom-fields/${id}`,
        {
          method: "DELETE",
        }
      );

      if (!response.ok) {
        const error = await response.json();
        throw new Error(error.message || "Failed to delete custom field");
      }

      return response.json();
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: QUERY_KEYS.lists() });
      toast.success("Custom field deleted successfully");
    },
    onError: (error: Error) => {
      toast.error(error.message);
    },
  });
};
