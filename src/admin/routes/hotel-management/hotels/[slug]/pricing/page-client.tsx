import { usePara<PERSON>, useNavigate } from "react-router-dom";
import { useMemo } from "react";
import { Container, Heading, Text, Button, Toaster } from "@camped-ai/ui";
import { ArrowLeft, Tags } from "lucide-react";
import HotelPricingManager from "../../../../../components/hotel/pricing/hotel-pricing-manager-new";
import { useRbac } from "../../../../../hooks/use-rbac";
import { useHotels } from "../../../../../hooks/supplier-products-services/use-hotels";
import PermissionBasedSidebarHider from "../../../../../widgets/permission-based-sidebar-hider";
import { RoleGuard } from "../../../../../components/rbac/RoleGuard";

const PageClient = () => {
  const { slug } = useParams();
  const navigate = useNavigate();
  const { hasPermission, loading: rbacLoading } = useRbac();

  // Use the hotels hook to get hotel details
  const { data: hotelsData, isLoading: isLoadingHotels } = useHotels({
    limit: 100,
    is_active: true,
  });

  // Find the hotel by slug
  const hotel = useMemo(() => {
    if (!hotelsData?.hotels || !slug) return null;
    return hotelsData.hotels.find((h: any) => h.id === slug || h.slug === slug) as any | null;
  }, [hotelsData?.hotels, slug]);

  // Check permissions
  const canEdit = hasPermission("rooms:availability");
  const canCreate = hasPermission("rooms:availability");
  const canDelete = hasPermission("rooms:availability");

  // Loading state
  if (isLoadingHotels || rbacLoading) {
    return (
      <Container className="py-6">
        <div className="flex items-center justify-center py-12">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
          <span className="ml-3 text-sm text-gray-600">Loading...</span>
        </div>
      </Container>
    );
  }

  // Hotel not found
  if (!hotel) {
    return (
      <Container className="py-6">
        <div className="text-center py-12 bg-muted rounded-lg">
          <Tags className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
          <Heading level="h1">Hotel Not Found</Heading>
          <Text className="text-muted-foreground mb-4">
            The hotel you're looking for doesn't exist or has been removed.
          </Text>
          <Button
            variant="primary"
            size="small"
            onClick={() => navigate("/hotel-management/hotels")}
          >
            Back to Hotels
          </Button>
        </div>
      </Container>
    );
  }


  return (
    <>
      <PermissionBasedSidebarHider />
      <RoleGuard
        requirePermission="rooms:availability"
        fallback={
          <Container className="py-8">
            <div className="text-center py-12 bg-muted rounded-lg">
              <Tags className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
              <Heading level="h1">Access Denied</Heading>
              <Text className="text-muted-foreground mb-4">
                You don't have permission to view hotel pricing.
                <br />
                Required permission: rooms:availability
              </Text>
              <Button
                variant="primary"
                size="small"
                onClick={() => navigate("/hotel-management/hotels")}
              >
                Back to Hotels
              </Button>
            </div>
          </Container>
        }
      >
        <Toaster />
        <Container className="py-6">
          {/* Header with back button */}
          <div className="mb-6">
            <div className="flex items-center justify-between space-x-4 mb-4">
              <div>
                <Heading level="h1" className="text-2xl font-bold">
                  {hotel.name} - Pricing Management
                </Heading>
                <Text className="text-muted-foreground">
                  Manage pricing for all room types and seasonal periods
                </Text>
              </div>
              <Button
                variant="secondary"
                size="small"
                onClick={() => navigate(`/hotel-management/hotels/${slug}`)}
                className="flex items-center gap-2"
              >
                <ArrowLeft className="w-4 h-4" />
                Back to Hotel
              </Button>
            </div>
          </div>

          {/* Use the same HotelPricingManager component as supplier management */}
          <HotelPricingManager
            hotelId={hotel.id}
            hotelName={hotel.name}
            onBack={() => navigate(`/hotel-management/hotels/${slug}`)}
            canEdit={canEdit}
            canCreate={canCreate}
            canDelete={canDelete}
            hideBackButton={true}
          />
        </Container>
      </RoleGuard>
    </>
  );
};



export default PageClient;
