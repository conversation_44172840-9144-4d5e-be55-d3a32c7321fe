import { useQuery, useMutation, useQueryClient } from "@tanstack/react-query";
import { toast } from "@camped-ai/ui";

export interface SupplierOrderItem {
  id: string;
  item_type: "product" | "service";
  item_id: string;
  item_name: string;
  item_description?: string;
  quantity: number;
  unit_price: number;
  total_price: number;
  service_date?: string;
  service_duration_minutes?: number;
  product_sku?: string;
  specifications?: Record<string, any>;
  status: string;
  notes?: string;
}

export interface SupplierOrder {
  id: string;
  supplier_id: string;
  supplier_name?: string;
  supplier_type?: "Company" | "Individual";
  order_number: string;
  status: string;
  order_type: "product" | "service" | "mixed";
  subtotal: number;
  tax_amount: number;
  total_amount: number;
  currency_code: string;
  requested_delivery_date?: string;
  actual_delivery_date?: string;
  delivery_address?: string;
  notes?: string;
  internal_notes?: string;
  customer_name?: string;
  customer_email?: string;
  customer_phone?: string;
  hotel_id?: string;
  booking_id?: string;
  items: SupplierOrderItem[];
  items_count?: number;
  created_at: string;
  updated_at: string;
  metadata?: Record<string, any>;
}

export interface CreateSupplierOrderData {
  supplier_id: string;
  order_type: "product" | "service" | "mixed";
  requested_delivery_date?: string;
  delivery_address?: string;
  notes?: string;
  internal_notes?: string;
  customer_name?: string;
  customer_email?: string;
  customer_phone?: string;
  hotel_id?: string;
  booking_id?: string;
  items: {
    item_type: "product" | "service";
    item_id: string;
    item_name: string;
    item_description?: string;
    quantity: number;
    unit_price: number;
    service_date?: string;
    service_duration_minutes?: number;
    product_sku?: string;
    specifications?: Record<string, any>;
    notes?: string;
  }[];
}

export interface UpdateSupplierOrderData
  extends Partial<CreateSupplierOrderData> {
  id: string;
  status?: "pending" | "confirmed" | "in_progress" | "completed" | "cancelled";
  notes?: string;
}

export interface SupplierOrderFilters {
  supplier_id?: string;
  status?: "pending" | "confirmed" | "in_progress" | "completed" | "cancelled";
  order_type?: "product" | "service" | "mixed";
  order_number?: string;
  search?: string;
  limit?: number;
  offset?: number;
  sort_by?: "order_number" | "status" | "total_amount" | "created_at" | "updated_at" | "supplier_name";
  sort_order?: "asc" | "desc";
}

export interface SupplierOrdersResponse {
  orders: SupplierOrder[];
  count: number;
  offset: number;
  limit: number;
}

export interface SupplierOrderResponse {
  order: SupplierOrder;
}

// Query Keys
export const supplierOrderKeys = {
  all: ["supplier-orders"] as const,
  lists: () => [...supplierOrderKeys.all, "list"] as const,
  list: (filters: SupplierOrderFilters) =>
    [...supplierOrderKeys.lists(), filters] as const,
  details: () => [...supplierOrderKeys.all, "detail"] as const,
  detail: (id: string) => [...supplierOrderKeys.details(), id] as const,
};

// Hooks
export const useSupplierOrders = (filters: SupplierOrderFilters = {}) => {
  return useQuery({
    queryKey: supplierOrderKeys.list(filters),
    queryFn: async (): Promise<SupplierOrdersResponse> => {
      const params = new URLSearchParams();

      if (filters.supplier_id)
        params.append("supplier_id", filters.supplier_id);
      if (filters.status) {
        // Validate status before sending
        const validStatuses = [
          "pending",
          "confirmed",
          "in_progress",
          "completed",
          "cancelled",
        ];
        if (validStatuses.includes(filters.status)) {
          params.append("status", filters.status);
        } else {
          console.warn(
            `Invalid status filter: ${
              filters.status
            }. Valid values: ${validStatuses.join(", ")}`
          );
        }
      }
      if (filters.order_type) params.append("order_type", filters.order_type);
      if (filters.order_number)
        params.append("order_number", filters.order_number);
      if (filters.search) params.append("search", filters.search);
      if (filters.limit) params.append("limit", filters.limit.toString());
      if (filters.offset) params.append("offset", filters.offset.toString());
      if (filters.sort_by) params.append("sort_by", filters.sort_by);
      if (filters.sort_order) params.append("sort_order", filters.sort_order);

      const url = `/admin/supplier-management/orders${
        params.toString() ? `?${params.toString()}` : ""
      }`;

      const response = await fetch(url, {
        credentials: "include",
        headers: {
          "Content-Type": "application/json",
        },
      });

      if (!response.ok) {
        throw new Error(`HTTP ${response.status}: ${response.statusText}`);
      }

      return response.json();
    },
  });
};

export const useSupplierOrder = (id: string) => {
  return useQuery({
    queryKey: supplierOrderKeys.detail(id),
    queryFn: async (): Promise<SupplierOrderResponse> => {
      const response = await fetch(`/admin/supplier-management/orders/${id}`, {
        credentials: "include",
        headers: {
          "Content-Type": "application/json",
        },
      });

      if (!response.ok) {
        throw new Error(`HTTP ${response.status}: ${response.statusText}`);
      }

      return response.json();
    },
    enabled: !!id,
  });
};

export const useCreateSupplierOrder = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: async (
      data: CreateSupplierOrderData
    ): Promise<SupplierOrderResponse> => {
      const response = await fetch("/admin/supplier-management/orders", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify(data),
        credentials: "include",
      });

      if (!response.ok) {
        const errorText = await response.text();
        throw new Error(
          `Failed to create supplier order: ${response.statusText} - ${errorText}`
        );
      }

      return response.json();
    },
    onSuccess: (data) => {
      queryClient.invalidateQueries({ queryKey: supplierOrderKeys.lists() });
      toast.success(`Order "${data.order.order_number}" created successfully`);
    },
    onError: (error: Error) => {
      toast.error(`Failed to create order: ${error.message}`);
    },
  });
};

export const useUpdateSupplierOrder = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: async (
      data: UpdateSupplierOrderData
    ): Promise<SupplierOrderResponse> => {
      const { id, ...updateData } = data;
      const response = await fetch(`/admin/supplier-management/orders/${id}`, {
        method: "PUT",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify(updateData),
        credentials: "include",
      });

      if (!response.ok) {
        const error = await response.json();
        throw new Error(
          error.message || `Failed to update order: ${response.statusText}`
        );
      }

      return response.json();
    },
    onSuccess: (data) => {
      queryClient.invalidateQueries({ queryKey: supplierOrderKeys.lists() });
      queryClient.invalidateQueries({
        queryKey: supplierOrderKeys.detail(data.order.id),
      });
      // Invalidate booking add-ons queries to refresh status synchronization
      queryClient.invalidateQueries({ queryKey: ["booking-addons"] });
      toast.success(`Order "${data.order.order_number}" updated successfully`);
    },
    onError: (error: Error) => {
      toast.error(`Failed to update order: ${error.message}`);
    },
  });
};

export const useUpdateSupplierOrderStatus = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: async ({
      id,
      status,
    }: {
      id: string;
      status: string;
    }): Promise<SupplierOrderResponse> => {
      const response = await fetch(`/admin/supplier-management/orders/${id}`, {
        method: "PATCH",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({ status }),
        credentials: "include",
      });

      if (!response.ok) {
        const error = await response.json();
        throw new Error(
          error.message ||
            `Failed to update order status: ${response.statusText}`
        );
      }

      return response.json();
    },
    onSuccess: (data, variables) => {
      queryClient.invalidateQueries({ queryKey: supplierOrderKeys.lists() });
      queryClient.invalidateQueries({
        queryKey: supplierOrderKeys.detail(data.order.id),
      });
      // Invalidate booking add-ons queries to refresh status synchronization
      queryClient.invalidateQueries({ queryKey: ["booking-addons"] });
      toast.success(`Order status updated to ${variables.status} successfully`);
    },
    onError: (error: Error) => {
      toast.error(`Failed to update order status: ${error.message}`);
    },
  });
};

export const useDeleteSupplierOrder = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: async (id: string): Promise<{ results: any }> => {
      const response = await fetch(`/admin/supplier-management/orders/${id}`, {
        method: "DELETE",
        credentials: "include",
      });

      if (!response.ok) {
        const error = await response.json();
        throw new Error(
          error.message || `Failed to delete order: ${response.statusText}`
        );
      }

      return response.json();
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: supplierOrderKeys.lists() });
      toast.success("Order deleted successfully");
    },
    onError: (error: Error) => {
      toast.error(`Failed to delete order: ${error.message}`);
    },
  });
};
