import { MedusaRequest, MedusaResponse } from "@camped-ai/framework/http";
import * as ExcelJS from 'exceljs';

/**
 * GET endpoint to download a comprehensive bulk import template
 * Contains 4 sheets: Destinations, Hotels, Room Configs, and Rooms
 */
export const GET = async (_req: MedusaRequest, res: MedusaResponse) => {
  try {
    // Create a new workbook
    const workbook = new ExcelJS.Workbook();

    // Sheet 1: Destinations
    const destinationsSheet = workbook.addWorksheet('Destinations');
    setupDestinationsSheet(destinationsSheet);

    // Sheet 2: Hotels
    const hotelsSheet = workbook.addWorksheet('Hotels');
    setupHotelsSheet(hotelsSheet);

    // Sheet 3: Room Configs
    const roomConfigsSheet = workbook.addWorksheet('RoomConfigs');
    setupRoomConfigsSheet(roomConfigsSheet);

    // Sheet 4: Rooms
    const roomsSheet = workbook.addWorksheet('Rooms');
    setupRoomsSheet(roomsSheet);

    // Sheet 5: Instructions
    const instructionsSheet = workbook.addWorksheet('Instructions');
    setupInstructionsSheet(instructionsSheet);

    // Set content type and headers for Excel file download
    res.setHeader('Content-Type', 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet');
    res.setHeader('Content-Disposition', 'attachment; filename=bulk-import-template.xlsx');

    // Write the workbook to the response
    await workbook.xlsx.write(res);

  } catch (error) {
    console.error('Error generating bulk import template:', error);
    res.status(500).json({ message: 'Error generating template' });
  }
};

/**
 * Setup Destinations sheet
 */
function setupDestinationsSheet(worksheet: ExcelJS.Worksheet) {
  // Define columns
  worksheet.columns = [
    { header: 'Name*', key: 'name', width: 30 },
    { header: 'Description', key: 'description', width: 50 },
    { header: 'Is Active', key: 'is_active', width: 15 },
    { header: 'Country*', key: 'country', width: 20 },
    { header: 'Location', key: 'location', width: 30 },
    { header: 'Is Featured', key: 'is_featured', width: 15 },
    { header: 'Tags (comma separated)', key: 'tags', width: 30 },
    { header: 'Website', key: 'website', width: 30 },
  ];

  // Style the header row
  const headerRow = worksheet.getRow(1);
  headerRow.font = { bold: true, color: { argb: 'FF000000' } };
  headerRow.fill = { type: 'pattern', pattern: 'solid', fgColor: { argb: 'FFE0E0E0' } };

  // Add sample data
  worksheet.addRow({
    name: 'Paris',
    description: 'The capital of France, known for its art, fashion, and culture',
    is_active: 'TRUE',
    country: 'France',
    location: 'Europe',
    is_featured: 'TRUE',
    tags: 'europe,city,romantic,culture',
    website: 'https://www.paris.fr'
  });

  worksheet.addRow({
    name: 'Tokyo',
    description: 'The bustling capital of Japan, blending tradition with modernity',
    is_active: 'TRUE',
    country: 'Japan',
    location: 'Asia',
    is_featured: 'FALSE',
    tags: 'asia,city,modern,technology',
    website: 'https://www.tokyo.lg.jp'
  });
}

/**
 * Setup Hotels sheet
 */
function setupHotelsSheet(worksheet: ExcelJS.Worksheet) {
  // Define columns
  worksheet.columns = [
    { header: 'Name*', key: 'name', width: 30 },
    { header: 'Destination*', key: 'destination', width: 25 },
    { header: 'Description', key: 'description', width: 50 },
    { header: 'Is Active', key: 'is_active', width: 15 },
    { header: 'Address', key: 'address', width: 40 },
    { header: 'City', key: 'city', width: 25 },
    { header: 'Phone Number', key: 'phone_number', width: 20 },
    { header: 'Email', key: 'email', width: 30 },
    { header: 'Website', key: 'website', width: 30 },
    { header: 'Check In Time', key: 'check_in_time', width: 15 },
    { header: 'Check Out Time', key: 'check_out_time', width: 15 },
    { header: 'Rating', key: 'rating', width: 10 },
    { header: 'Is Pets Allowed', key: 'is_pets_allowed', width: 15 },
    { header: 'Amenities (comma separated)', key: 'amenities', width: 40 },
    { header: 'Rules (comma separated)', key: 'rules', width: 40 },
    { header: 'Safety Measures (comma separated)', key: 'safety_measures', width: 40 },
    { header: 'Notes', key: 'notes', width: 50 },
    { header: 'Tags (comma separated)', key: 'tags', width: 30 },
  ];

  // Style the header row
  const headerRow = worksheet.getRow(1);
  headerRow.font = { bold: true, color: { argb: 'FF000000' } };
  headerRow.fill = { type: 'pattern', pattern: 'solid', fgColor: { argb: 'FFE0E0E0' } };

  // Add sample data
  worksheet.addRow({
    name: 'Hotel Paris Central',
    destination: 'Paris',
    description: 'Luxury hotel in the heart of Paris',
    is_active: 'TRUE',
    address: '123 Champs-Élysées, 75008 Paris',
    city: 'Paris',
    phone_number: '+33 1 23 45 67 89',
    email: '<EMAIL>',
    website: 'https://www.hotelpariscentral.com',
    check_in_time: '15:00',
    check_out_time: '11:00',
    rating: '4.5',
    is_pets_allowed: 'FALSE',
    amenities: 'WiFi,Pool,Spa,Restaurant,Bar',
    rules: 'No smoking,Quiet hours 22:00-08:00',
    safety_measures: 'CCTV,Security guards,Fire safety',
    notes: 'Historic building with modern amenities',
    tags: 'luxury,central,historic'
  });

  worksheet.addRow({
    name: 'Tokyo Grand Hotel',
    destination: 'Tokyo',
    description: 'Modern hotel with traditional Japanese hospitality',
    is_active: 'TRUE',
    address: '1-1-1 Shibuya, Tokyo 150-0002',
    city: 'Tokyo',
    phone_number: '+81 3 1234 5678',
    email: '<EMAIL>',
    website: 'https://www.tokyograndhotel.com',
    check_in_time: '14:00',
    check_out_time: '12:00',
    rating: '4.8',
    is_pets_allowed: 'TRUE',
    amenities: 'WiFi,Gym,Restaurant,Business center',
    rules: 'No smoking in rooms,Shoes off in traditional areas',
    safety_measures: 'Earthquake safety,CCTV,24/7 security',
    notes: 'Blend of modern and traditional Japanese design',
    tags: 'modern,traditional,business'
  });
}

/**
 * Setup Room Configs sheet
 */
function setupRoomConfigsSheet(worksheet: ExcelJS.Worksheet) {
  // Define columns
  worksheet.columns = [
    { header: 'Hotel Name*', key: 'hotel_name', width: 25 },
    { header: 'Name*', key: 'name', width: 25 },
    { header: 'Description', key: 'description', width: 50 },
    { header: 'Max Adults*', key: 'max_adults', width: 12 },
    { header: 'Max Children', key: 'max_children', width: 12 },
    { header: 'Max Infants', key: 'max_infants', width: 12 },
    { header: 'Max Extra Beds', key: 'max_extra_beds', width: 15 },
    { header: 'Max Extra Cots', key: 'max_extra_cots', width: 15 },
    { header: 'Max Adults Beyond Capacity', key: 'max_adults_beyond_capacity', width: 25 },
    { header: 'Bed Types (comma separated)', key: 'bed_types', width: 30 },
    { header: 'Room Size (sqm)', key: 'room_size', width: 15 },
    { header: 'View Type', key: 'view_type', width: 20 },
    { header: 'Amenities (comma separated)', key: 'amenities', width: 40 },
    { header: 'Tags (comma separated)', key: 'tags', width: 30 },
  ];

  // Style the header row
  const headerRow = worksheet.getRow(1);
  headerRow.font = { bold: true, color: { argb: 'FF000000' } };
  headerRow.fill = { type: 'pattern', pattern: 'solid', fgColor: { argb: 'FFE0E0E0' } };

  // Add sample data
  worksheet.addRow({
    hotel_name: 'Hotel Paris Central',
    name: 'Deluxe Suite',
    description: 'Spacious suite with city view',
    max_adults: '2',
    max_children: '1',
    max_infants: '1',
    max_extra_beds: '1',
    max_extra_cots: '1',
    max_adults_beyond_capacity: '0',
    bed_types: 'King',
    room_size: '45',
    view_type: 'City View',
    amenities: 'Balcony,Mini bar,Safe,Air conditioning',
    tags: 'suite,luxury,city-view'
  });

  worksheet.addRow({
    hotel_name: 'Tokyo Grand Hotel',
    name: 'Standard Room',
    description: 'Comfortable room with modern amenities',
    max_adults: '2',
    max_children: '0',
    max_infants: '1',
    max_extra_beds: '0',
    max_extra_cots: '1',
    max_adults_beyond_capacity: '0',
    bed_types: 'Queen',
    room_size: '25',
    view_type: 'Garden View',
    amenities: 'Mini bar,Safe,Air conditioning',
    tags: 'standard,comfortable'
  });
}

/**
 * Setup Rooms sheet
 */
function setupRoomsSheet(worksheet: ExcelJS.Worksheet) {
  // Define columns
  worksheet.columns = [
    { header: 'Hotel Name*', key: 'hotel_name', width: 25 },
    { header: 'Room Config Name*', key: 'room_config_name', width: 25 },
    { header: 'Room Number*', key: 'room_number', width: 15 },
    { header: 'Name', key: 'name', width: 25 },
    { header: 'Floor', key: 'floor', width: 10 },
    { header: 'Notes', key: 'notes', width: 40 },
    { header: 'Left Room', key: 'left_room', width: 15 },
    { header: 'Right Room', key: 'right_room', width: 15 },
    { header: 'Opposite Room', key: 'opposite_room', width: 15 },
    { header: 'Connected Room', key: 'connected_room', width: 15 },
  ];

  // Style the header row
  const headerRow = worksheet.getRow(1);
  headerRow.font = { bold: true, color: { argb: 'FF000000' } };
  headerRow.fill = { type: 'pattern', pattern: 'solid', fgColor: { argb: 'FFE0E0E0' } };

  // Add sample data
  worksheet.addRow({
    hotel_name: 'Hotel Paris Central',
    room_config_name: 'Deluxe Suite',
    room_number: '101',
    name: 'Deluxe Suite 101',
    floor: '1',
    notes: 'Corner suite with panoramic city view',
    left_room: '',
    right_room: '102',
    opposite_room: '201',
    connected_room: ''
  });

  worksheet.addRow({
    hotel_name: 'Tokyo Grand Hotel',
    room_config_name: 'Standard Room',
    room_number: '301',
    name: 'Standard Room 301',
    floor: '3',
    notes: 'Quiet room facing the garden',
    left_room: '',
    right_room: '302',
    opposite_room: '',
    connected_room: ''
  });
}

/**
 * Setup Instructions sheet
 */
function setupInstructionsSheet(worksheet: ExcelJS.Worksheet) {
  worksheet.columns = [
    { header: 'Section', key: 'section', width: 25 },
    { header: 'Field', key: 'field', width: 25 },
    { header: 'Required', key: 'required', width: 15 },
    { header: 'Description', key: 'description', width: 60 }
  ];

  // Style the header row
  const headerRow = worksheet.getRow(1);
  headerRow.font = { bold: true, color: { argb: 'FF000000' } };
  headerRow.fill = { type: 'pattern', pattern: 'solid', fgColor: { argb: 'FFE0E0E0' } };

  // Add instructions
  const instructions = [
    { section: 'OVERVIEW', field: 'Import Order', required: '', description: 'Data is imported in this order: Destinations → Hotels → Room Configs → Rooms' },
    { section: 'OVERVIEW', field: 'Dependencies', required: '', description: 'Hotels reference Destinations, Room Configs reference Hotels, Rooms reference Room Configs' },
    { section: 'OVERVIEW', field: 'Handle Generation', required: '', description: 'URL-friendly handles are automatically generated from names' },
    { section: '', field: '', required: '', description: '' },
    
    { section: 'DESTINATIONS', field: 'Name', required: 'Yes', description: 'Destination name (handle auto-generated)' },
    { section: 'DESTINATIONS', field: 'Country', required: 'Yes', description: 'Country where destination is located' },
    { section: 'DESTINATIONS', field: 'Description', required: 'No', description: 'Detailed description of the destination' },
    { section: 'DESTINATIONS', field: 'Is Active', required: 'No', description: 'TRUE or FALSE (defaults to TRUE)' },
    { section: 'DESTINATIONS', field: 'Location', required: 'No', description: 'Geographic region or area' },
    { section: 'DESTINATIONS', field: 'Is Featured', required: 'No', description: 'TRUE or FALSE (defaults to FALSE)' },
    { section: 'DESTINATIONS', field: 'Tags', required: 'No', description: 'Comma-separated tags (e.g., "beach,summer,family")' },
    { section: 'DESTINATIONS', field: 'Website', required: 'No', description: 'Official website URL' },
    { section: '', field: '', required: '', description: '' },
    
    { section: 'HOTELS', field: 'Name', required: 'Yes', description: 'Hotel name (handle auto-generated)' },
    { section: 'HOTELS', field: 'Destination', required: 'Yes', description: 'Must match exactly with a destination name from Destinations sheet' },
    { section: 'HOTELS', field: 'Description', required: 'No', description: 'Hotel description' },
    { section: 'HOTELS', field: 'Address', required: 'No', description: 'Full hotel address' },
    { section: 'HOTELS', field: 'Check In Time', required: 'No', description: 'Format: HH:MM (e.g., "15:00")' },
    { section: 'HOTELS', field: 'Check Out Time', required: 'No', description: 'Format: HH:MM (e.g., "11:00")' },
    { section: 'HOTELS', field: 'Rating', required: 'No', description: 'Hotel rating (e.g., "4.5")' },
    { section: 'HOTELS', field: 'Amenities', required: 'No', description: 'Comma-separated amenities' },
    { section: '', field: '', required: '', description: '' },
    
    { section: 'ROOM CONFIGS', field: 'Hotel Name', required: 'Yes', description: 'Must match exactly with a hotel name from Hotels sheet' },
    { section: 'ROOM CONFIGS', field: 'Name', required: 'Yes', description: 'Room configuration name (handle auto-generated as hotel-handle-room-config-name)' },
    { section: 'ROOM CONFIGS', field: 'Max Adults', required: 'Yes', description: 'Maximum number of adults' },
    { section: 'ROOM CONFIGS', field: 'Bed Types', required: 'No', description: 'Comma-separated bed types (e.g., "King,Queen")' },
    { section: 'ROOM CONFIGS', field: 'Room Size', required: 'No', description: 'Room size in square meters' },
    { section: '', field: '', required: '', description: '' },
    
    { section: 'ROOMS', field: 'Hotel Name', required: 'Yes', description: 'Must match exactly with a hotel name from Hotels sheet' },
    { section: 'ROOMS', field: 'Room Config Name', required: 'Yes', description: 'Must match exactly with a room config name from RoomConfigs sheet' },
    { section: 'ROOMS', field: 'Room Number', required: 'Yes', description: 'Unique room number within the hotel' },
    { section: 'ROOMS', field: 'Floor', required: 'No', description: 'Floor number or identifier' },
    { section: 'ROOMS', field: 'Left/Right/Opposite/Connected Room', required: 'No', description: 'Room numbers for spatial relationships' },
  ];

  instructions.forEach(instruction => {
    worksheet.addRow(instruction);
  });
}
