import { MedusaRequest, MedusaResponse } from "@camped-ai/framework/http";

export const GET = async (req: MedusaRequest, res: MedusaResponse) => {
  const query = req.scope.resolve("query");
  console.log("Fetching destination with ID:", req.params.id);

  const { data: destinations } = await query.graph({
    entity: "destination",
    filters: {
      id: req.params.id,
    },
    fields: ["*", "images.*", "faqs.*"],
  });

  console.log("Query result:", destinations);

  if (!destinations || destinations.length === 0) {
    return res.status(404).json({ error: "Destination not found" });
  }

  const destination = destinations[0];
  console.log("Returning destination:", destination);
  res.json({ destination });
};
