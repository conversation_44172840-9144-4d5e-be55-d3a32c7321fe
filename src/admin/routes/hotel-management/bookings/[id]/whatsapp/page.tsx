import { defineRouteConfig } from "@camped-ai/admin-sdk";
import { Container } from "@camped-ai/ui";
import { useParams } from "react-router-dom";
import PermissionBasedSidebarHider from "../../../../../widgets/permission-based-sidebar-hider";
import WhatsAppMessagesPanel from "../../../../../components/booking/whatsapp-messages-panel";
import AdminPageHelmet from "../../../../../components/AdminPageHelmet";

const BookingWhatsAppPage = () => {
  const { id } = useParams();

  return (
    <>
          <AdminPageHelmet />
      <PermissionBasedSidebarHider />
      <Container>
        <WhatsAppMessagesPanel bookingId={id} />
      </Container>
    </>
  );
};

export const config = defineRouteConfig({
  label: "WhatsApp Messages",
});

export default BookingWhatsAppPage;
