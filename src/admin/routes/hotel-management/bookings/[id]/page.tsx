import { defineRouteConfig } from "@camped-ai/admin-sdk";
import { lazy, Suspense } from "react";
import { Container } from "@camped-ai/ui";
import AdminPageHelmet from "../../../../components/AdminPageHelmet";

// Dynamically import page client for better performance
const PageClient = lazy(() => import("./page-client"));

const BookingDetailsPage = () => {
  return (
    <>
      <AdminPageHelmet />
    <Suspense
      fallback={
        <Container className="p-6">
          <div className="flex items-center justify-center py-12">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
            <span className="ml-3 text-sm text-gray-600">Loading bookings...</span>
          </div>
        </Container>
      }
    >
      <PageClient />
    </Suspense>
    </>
  );
};

export default BookingDetailsPage;

export const config = defineRouteConfig({
  label: "Booking Details",
});
