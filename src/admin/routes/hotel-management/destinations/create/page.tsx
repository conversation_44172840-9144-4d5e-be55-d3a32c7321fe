import { Toaster, toast, Container } from "@camped-ai/ui";
import { useState, Suspense } from "react";
import { useNavigate } from "react-router-dom";
import PermissionBasedSidebarHider from "../../../../widgets/permission-based-sidebar-hider";
import { useRbac } from "../../../../hooks/use-rbac";
import { LazyLoadErrorBoundary, createSafeLazyComponent } from "../../../../components/common/lazy-load-error-boundary";

// Dynamically import page client for better performance
const DestinationCreateForm = createSafeLazyComponent(() => import("./page-client"));

const CreateDestinationPage = () => {
  const navigate = useNavigate();
  const { hasPermission } = useRbac();
  const [isSubmitting, setIsSubmitting] = useState(false);

  // Check permission and redirect if not authorized
  if (!hasPermission("destinations:create")) {
    navigate("/hotel-management/destinations");
    return null;
  }

  // Handle form submission
  const handleSubmit = async (data: any) => {
    setIsSubmitting(true);
    try {
      // Format tags properly
      let formattedTags = data.tags;
      if (typeof data.tags === "string") {
        try {
          formattedTags = JSON.parse(data.tags as string);
        } catch (e) {
          formattedTags = (data.tags as string)
            .split(",")
            .map((tag) => tag.trim());
        }
      }

      // Helper function to only include non-empty values
      const cleanValue = (value: any) => {
        if (value === null || value === undefined || value === "") {
          return undefined;
        }
        return value;
      };

      // Transform data for API
      const destinationData = {
        name: data.name,
        handle: data.handle,
        description: cleanValue(data.description),
        is_active: data.is_active,
        is_featured: data.is_featured,
        country: data.country,
        location: cleanValue(data.location),
        tags: formattedTags,
        website: cleanValue(data.website),
        faqs: data.faqs || [],
        // New fields for currency/margin enhancement
        internal_web_link: cleanValue(data.internal_web_link),
        external_web_link: cleanValue(data.external_web_link),
        currency_code: cleanValue(data.currency_code),
        margin: cleanValue(data.margin),
      };

      // Validate FAQs
      if (destinationData.faqs && destinationData.faqs.length > 0) {
        const invalidFaqs = destinationData.faqs.filter(
          (faq: any) => !faq.question?.trim() || !faq.answer?.trim()
        );

        if (invalidFaqs.length > 0) {
          toast.error("Invalid FAQ Data", {
            description:
              "All FAQ entries must have both question and answer filled out.",
          });
          return;
        }
      }

      const response = await fetch("/admin/hotel-management/destinations", {
        method: "POST",
        credentials: "include",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify(destinationData),
      });

      const result = await response.json();

      if (response.ok && result.destination) {
        const destinationId = result.destination.id;

        // Handle media upload if present
        if (data.media && data.media.length > 0) {
          const validImages = data.media.filter((media: any) => {
            if (!media.file) {
              return false;
            }

            const maxSize = 10 * 1024 * 1024; // 10MB
            if (media.file.size > maxSize) {
              toast.error("Image Too Large", {
                description: `"${media.file.name}" is too large. Maximum size is 10MB.`,
              });
              return false;
            }

            const allowedTypes = [
              "image/jpeg",
              "image/jpg",
              "image/png",
              "image/webp",
            ];
            if (!allowedTypes.includes(media.file.type)) {
              toast.error("Invalid Image Type", {
                description: `"${media.file.name}" is not a supported image format. Use JPEG, PNG, or WebP.`,
              });
              return false;
            }

            return true;
          });

          let uploadedCount = 0;
          let failedCount = 0;

          for (const media of validImages) {
            if (media.file) {
              const formData = new FormData();
              formData.append("files", media.file);

              const metadata = {
                isThumbnail: media.isThumbnail,
              };
              formData.append("metadata", JSON.stringify(metadata));

              try {
                const uploadResponse = await fetch(
                  `/admin/hotel-management/destinations/${destinationId}/upload`,
                  {
                    method: "POST",
                    credentials: "include",
                    body: formData,
                  }
                );

                if (!uploadResponse.ok) {
                  failedCount++;
                  toast.error("Image Upload Failed", {
                    description: `Failed to upload "${media.file.name}". Please try again.`,
                  });
                } else {
                  uploadedCount++;
                }
              } catch (uploadError) {
                failedCount++;
                toast.error("Upload Error", {
                  description: `Network error while uploading "${media.file.name}". Please check your connection.`,
                });
              }
            }
          }

          if (uploadedCount > 0 && failedCount > 0) {
            toast.warning("Partial Upload Success", {
              description: `${uploadedCount} images uploaded successfully, ${failedCount} failed.`,
            });
          } else if (uploadedCount > 0 && failedCount === 0) {
            toast.success("Images Uploaded", {
              description: `Successfully uploaded ${uploadedCount} image${
                uploadedCount > 1 ? "s" : ""
              }.`,
            });
          } else if (validImages.length > 0 && uploadedCount === 0) {
            toast.error("Upload Failed", {
              description: "All image uploads failed. Please try again.",
            });
          }
        }

        // Handle translation data if present
        if (data.translationData) {
          try {
            await data.translationData.saveFunction(destinationId);
          } catch (translationError) {
            console.error("Error saving translations:", translationError);
          }
        }

        toast.success("Destination created successfully!");
        navigate("/hotel-management/destinations");
        return { destination_id: destinationId };
      } else {
        console.error("Destination creation failed:", {
          status: response.status,
          statusText: response.statusText,
          data: result,
        });

        let errorMessage = "Failed to create destination";
        if (result.message) {
          errorMessage = result.message;
        } else if (response.status === 400) {
          errorMessage =
            "Invalid data provided. Please check all fields and try again.";
        } else if (response.status === 403) {
          errorMessage = "You don't have permission to create destinations.";
        } else if (response.status === 409) {
          errorMessage = "A destination with this handle already exists.";
        } else if (response.status >= 500) {
          errorMessage = "Server error occurred. Please try again later.";
        }

        toast.error(errorMessage);
      }
    } catch (error: any) {
      console.error("Error creating destination:", error);
      toast.error(error.message || "Failed to create destination");
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleCancel = () => {
    navigate("/hotel-management/destinations");
  };

  return (
    <>
      <PermissionBasedSidebarHider />
      <Toaster />
      {/* Form */}
      <LazyLoadErrorBoundary fallbackMessage="Failed to load destination creation form.">
        <Suspense
          fallback={
            <Container className="p-6">
              <div className="flex items-center justify-center py-12">
                <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
                <span className="ml-3 text-sm text-gray-600">
                  Loading destination form...
                </span>
              </div>
            </Container>
          }
        >
          <DestinationCreateForm
            onSubmit={handleSubmit}
            onCancel={handleCancel}
            isSubmitting={isSubmitting}
          />
        </Suspense>
      </LazyLoadErrorBoundary>
    </>
  );
};

export default CreateDestinationPage;
