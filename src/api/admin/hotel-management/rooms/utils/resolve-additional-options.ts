import { MedusaContainer } from "@camped-ai/framework/types";

export interface AdditionalOptions {
  max_extra_beds: number;
  max_cots: number;
  max_adults_beyond_capacity: number;
}

export interface RoomWithAdditionalOptions {
  id: string;
  name: string;
  room_number: string;
  status: string;
  floor: string;
  notes: string;
  is_active: boolean;
  left_room: string;
  opposite_room: string;
  connected_room: string;
  right_room: string;
  room_config_id: string;
  hotel_id: string;
  // Additional options with fallback logic
  max_extra_beds: number;
  max_cots: number;
  max_adults_beyond_capacity: number;
  // Indicators for UI
  has_additional_options_override: boolean;
}

/**
 * Resolves additional options for a room with fallback to room type defaults
 * @param container - Medusa container for dependency injection
 * @param roomVariant - Room variant data with metadata
 * @param roomConfigId - Room configuration ID for fallback
 * @returns Promise<AdditionalOptions> - Resolved additional options
 */
export async function resolveAdditionalOptions(
  container: MedusaContainer,
  roomVariant: any,
  roomConfigId: string
): Promise<AdditionalOptions> {
  const query = container.resolve("query");

  // Check if room has overrides in metadata
  const roomOverrides = {
    max_extra_beds: roomVariant.metadata?.max_extra_beds,
    max_cots: roomVariant.metadata?.max_cots,
    max_adults_beyond_capacity: roomVariant.metadata?.max_adults_beyond_capacity,
  };

  // If all values are overridden at room level, return them
  if (
    roomOverrides.max_extra_beds !== undefined &&
    roomOverrides.max_cots !== undefined &&
    roomOverrides.max_adults_beyond_capacity !== undefined
  ) {
    return {
      max_extra_beds: roomOverrides.max_extra_beds,
      max_cots: roomOverrides.max_cots,
      max_adults_beyond_capacity: roomOverrides.max_adults_beyond_capacity,
    };
  }

  // Get room configuration defaults for fallback
  const { data: roomConfigs } = await query.graph({
    entity: "room_config",
    filters: { id: [roomConfigId] },
    fields: ["max_extra_beds", "max_cots", "max_adults_beyond_capacity"],
  });

  const roomConfig = roomConfigs?.[0];
  const defaults = {
    max_extra_beds: roomConfig?.max_extra_beds || 0,
    max_cots: roomConfig?.max_cots || 0,
    max_adults_beyond_capacity: roomConfig?.max_adults_beyond_capacity || 0,
  };

  // Return room overrides with fallback to room config defaults
  return {
    max_extra_beds: roomOverrides.max_extra_beds !== undefined 
      ? roomOverrides.max_extra_beds 
      : defaults.max_extra_beds,
    max_cots: roomOverrides.max_cots !== undefined 
      ? roomOverrides.max_cots 
      : defaults.max_cots,
    max_adults_beyond_capacity: roomOverrides.max_adults_beyond_capacity !== undefined 
      ? roomOverrides.max_adults_beyond_capacity 
      : defaults.max_adults_beyond_capacity,
  };
}

/**
 * Checks if a room has any additional options overrides
 * @param roomVariant - Room variant data with metadata
 * @returns boolean - True if room has any overrides
 */
export function hasAdditionalOptionsOverride(roomVariant: any): boolean {
  return (
    roomVariant.metadata?.max_extra_beds !== undefined ||
    roomVariant.metadata?.max_cots !== undefined ||
    roomVariant.metadata?.max_adults_beyond_capacity !== undefined
  );
}

/**
 * Enhances room data with resolved additional options
 * @param container - Medusa container for dependency injection
 * @param rooms - Array of room data
 * @returns Promise<RoomWithAdditionalOptions[]> - Enhanced room data
 */
export async function enhanceRoomsWithAdditionalOptions(
  container: MedusaContainer,
  rooms: any[]
): Promise<RoomWithAdditionalOptions[]> {
  const enhancedRooms = await Promise.all(
    rooms.map(async (room) => {
      const additionalOptions = await resolveAdditionalOptions(
        container,
        room.variant || room,
        room.room_config_id
      );

      return {
        ...room,
        ...additionalOptions,
        has_additional_options_override: hasAdditionalOptionsOverride(
          room.variant || room
        ),
      };
    })
  );

  return enhancedRooms;
}
