import { MedusaRequest, MedusaResponse } from "@camped-ai/framework/http";
import { CANCELLATION_POLICY_SERVICE } from "src/modules/hotel-management/cancellation-policy";
import CancellationPolicyService from "src/modules/hotel-management/cancellation-policy/service";

export const GET = async (req: MedusaRequest, res: MedusaResponse) => {
  const query = req.scope.resolve("query");
  const cancellationPolicyService: CancellationPolicyService =
    req.scope.resolve(CANCELLATION_POLICY_SERVICE);

  console.log("Fetching hotel with ID:", req.params.id);

  const { data: hotel } = await query.graph({
    entity: "hotel",
    filters: {
      id: [req.params.id],
    },
    fields: ["*", "images.*"],
  });

  console.log("Hotel query result:", hotel);

  // Get cancellation policies for this hotel
  let cancellationPolicies = [];
  if (hotel && hotel.length > 0) {
    try {
      cancellationPolicies =
        await cancellationPolicyService.findCancellationPolicies(
          { hotel_id: hotel[0].id, is_active: true },
          { order: { days_before_checkin: "DESC" } }
        );
    } catch (error) {
      console.error("Error fetching cancellation policies:", error);
      // Continue without cancellation policies if there's an error
    }
  }

  // Format cancellation policies for easier consumption
  const formattedCancellationPolicies = cancellationPolicies.map(
    (policy: any) => ({
      id: policy.id,
      name: policy.name,
      description: policy.description,
      days_before_checkin: policy.days_before_checkin,
      refund_type: policy.refund_type,
      refund_amount: policy.refund_amount,
    })
  );

  // Add cancellation policies to hotel data
  const hotelWithPolicies =
    hotel && hotel.length > 0
      ? {
          ...hotel[0],
          cancellation_policies: formattedCancellationPolicies,
        }
      : null;

  console.log("Final response data:", { hotel: hotelWithPolicies });
  res.json({ hotel: hotelWithPolicies });
};
