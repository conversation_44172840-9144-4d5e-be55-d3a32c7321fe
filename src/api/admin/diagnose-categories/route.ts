import { MedusaRequest, MedusaResponse } from "@camped-ai/framework/http";
import diagnoseCategorySystem from "../../../scripts/diagnose-category-system";

/**
 * Diagnostic endpoint for category system
 * GET /admin/diagnose-categories
 */
export const GET = async (req: MedusaRequest, res: MedusaResponse) => {
  try {
    console.log("🔍 Running category system diagnosis via API...");
    
    const results = await diagnoseCategorySystem(req.scope);
    
    const allPassed = Object.values(results).every(Boolean);
    
    res.json({
      success: allPassed,
      message: allPassed ? "All category system tests passed" : "Some category system tests failed",
      diagnosticResults: results,
      recommendations: allPassed ? [] : generateRecommendations(results)
    });

  } catch (error) {
    console.error("❌ Category diagnosis failed:", error);
    
    res.status(500).json({
      success: false,
      message: "Category system diagnosis failed",
      error: error instanceof Error ? error.message : "Unknown error",
      stack: error instanceof Error ? error.stack : undefined
    });
  }
};

function generateRecommendations(results: Record<string, boolean>): string[] {
  const recommendations: string[] = [];
  
  if (!results.moduleRegistration) {
    recommendations.push("Check if supplier-products-services module is properly registered in medusa-config.ts");
  }
  
  if (!results.serviceResolution) {
    recommendations.push("Verify that the SupplierProductsServicesModuleService is properly exported and configured");
  }
  
  if (!results.databaseConnection) {
    recommendations.push("Check database connection settings in environment variables");
  }
  
  if (!results.tableExists) {
    recommendations.push("Run database migrations or manually create the product_service_category table");
  }
  
  if (!results.categoryCreation) {
    recommendations.push("Check service implementation and database permissions for category creation");
  }
  
  if (!results.categoryListing) {
    recommendations.push("Verify category listing functionality and database query permissions");
  }
  
  if (!results.workflowExecution) {
    recommendations.push("Check workflow configuration and step implementations");
  }
  
  return recommendations;
}
