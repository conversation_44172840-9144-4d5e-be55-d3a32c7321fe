import { Migration } from '@mikro-orm/migrations';

/**
 * Migration to add valid_from and valid_to date fields to product_service table
 * 
 * This migration adds:
 * - valid_from: Date field for when the product/service becomes valid
 * - valid_to: Date field for when the product/service expires
 * - Indexes for performance on date queries
 */
export class Migration20250805120000 extends Migration {

  override async up(): Promise<void> {
    // Add valid_from and valid_to columns to product_service table
    this.addSql(`
      ALTER TABLE "product_service" 
      ADD COLUMN IF NOT EXISTS "valid_from" timestamptz NULL,
      ADD COLUMN IF NOT EXISTS "valid_to" timestamptz NULL;
    `);

    // Add indexes for the new date columns for performance
    this.addSql(`
      CREATE INDEX IF NOT EXISTS "IDX_product_service_valid_from" 
      ON "product_service" (valid_from) 
      WHERE deleted_at IS NULL AND valid_from IS NOT NULL;
    `);

    this.addSql(`
      CREATE INDEX IF NOT EXISTS "IDX_product_service_valid_to" 
      ON "product_service" (valid_to) 
      WHERE deleted_at IS NULL AND valid_to IS NOT NULL;
    `);

    // Add composite index for date range queries
    this.addSql(`
      CREATE INDEX IF NOT EXISTS "IDX_product_service_validity_range" 
      ON "product_service" (valid_from, valid_to) 
      WHERE deleted_at IS NULL;
    `);
  }

  override async down(): Promise<void> {
    // Drop indexes first
    this.addSql(`DROP INDEX IF EXISTS "IDX_product_service_valid_from";`);
    this.addSql(`DROP INDEX IF EXISTS "IDX_product_service_valid_to";`);
    this.addSql(`DROP INDEX IF EXISTS "IDX_product_service_validity_range";`);

    // Drop columns
    this.addSql(`
      ALTER TABLE "product_service" 
      DROP COLUMN IF EXISTS "valid_from",
      DROP COLUMN IF EXISTS "valid_to";
    `);
  }
}
