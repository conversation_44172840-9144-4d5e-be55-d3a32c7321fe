import { defineRouteConfig } from "@camped-ai/admin-sdk";
import { Container, Heading, Text } from "@camped-ai/ui";
import { useParams } from "react-router-dom";
import UpdateGuestForm from "../../../../../components/booking/update-guest-form";
import PermissionBasedSidebarHider from "../../../../../widgets/permission-based-sidebar-hider";
import { RoleGuard } from "../../../../../components/rbac/RoleGuard";
import AdminPageHelmet from "../../../../../components/AdminPageHelmet";

const UpdateGuestPage = () => {
  const { id } = useParams();

  return (
    <>
          <AdminPageHelmet />
      <PermissionBasedSidebarHider />
      <Container>
        <RoleGuard
          requirePermission="bookings:edit"
          fallback={
            <div className="p-8 text-center">
              <Heading level="h1">Access Denied</Heading>
              <Text className="mt-2">
                You don't have permission to edit booking information.
              </Text>
            </div>
          }
        >
          <UpdateGuestForm bookingId={id} />
        </RoleGuard>
      </Container>
    </>
  );
};

export const config = defineRouteConfig({
  label: "Update Traveler Information",
});

export default UpdateGuestPage;
