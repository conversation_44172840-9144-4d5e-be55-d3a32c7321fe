import { defineRouteConfig } from "@camped-ai/admin-sdk";
import { ArrowLeft } from "@camped-ai/icons";
import { Container, Heading, Button, Toaster, toast } from "@camped-ai/ui";
import { useState } from "react";
import { useNavigate, useParams } from "react-router-dom";
import PermissionBasedSidebarHider from "../../../../../widgets/permission-based-sidebar-hider";
import { RoleGuard } from "../../../../../components/rbac/RoleGuard";
import {
  useProductService,
  useUpdateProductService,
} from "../../../../../hooks/supplier-products-services/use-products-services";
import { emitProductServiceUpdated } from "../../../../../utils/events";
import { ProductServiceEditSkeleton } from "../../../../../components/supplier-management/product-service-edit-skeleton";
import ProductServiceForm, {
  type FormData,
} from "../../create/page-client";

const EditProductServicePage = () => {
  const navigate = useNavigate();
  const { id } = useParams<{ id: string }>();
  const updateProductService = useUpdateProductService();
  const [isSubmitting, setIsSubmitting] = useState(false);

  const {
    data: productServiceData,
    isLoading: productServiceLoading,
    error: productServiceError,
  } = useProductService(id!);

  const productService = productServiceData?.product_service;

  const handleSubmit = async (formData: FormData) => {
    setIsSubmitting(true);
    try {
      // Prepare data for API call
      const updateData: any = {
        name: formData.name,
        description: formData.description,
        base_cost:
          formData.base_cost !== ""
            ? parseFloat(formData.base_cost.toString())
            : undefined,
        valid_from: formData.valid_from || undefined,
        valid_to: formData.valid_to || undefined,
        custom_fields:
          Object.keys(formData.custom_fields).length > 0
            ? formData.custom_fields
            : undefined,
        status: formData.status,
        category_id: formData.category_id,
        unit_type_id: formData.unit_type_id,
        tag_ids: formData.tags,
        change_reason: formData.change_reason || undefined,
      };

      const result = await updateProductService.mutateAsync({
        id: id!,
        data: updateData,
      });

      // Emit custom event for real-time updates
      emitProductServiceUpdated({
        id: id!,
        productService: result.product_service,
      });

      toast.success("Product/Service updated successfully!");
      navigate(`/supplier-management/products-services/${id}`);
    } catch (error) {
      console.error("Error updating product/service:", error);
      // Error handling is done by the mutation hook
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleCancel = () => {
    navigate(`/supplier-management/products-services/${id}`);
  };
  if (productServiceLoading) {
    return (
      <>
        <PermissionBasedSidebarHider />
        <ProductServiceEditSkeleton />
      </>
    );
  }

  if (productServiceError || !productService) {
    return (
      <>
        <PermissionBasedSidebarHider />
        <Container className="p-6">
          <div className="text-center">
            <h2 className="text-lg font-semibold text-red-600">Error</h2>
            <p className="text-gray-600">
              {productServiceError?.message || "Product/Service not found"}
            </p>
            <Button
              onClick={() => navigate("/supplier-management/products-services")}
              className="mt-4"
            >
              Back to Products & Services
            </Button>
          </div>
        </Container>
      </>
    );
  }

  return (
    <>
      <PermissionBasedSidebarHider />
      <RoleGuard
        requirePermission="supplier_management:edit"
        fallback={
          <Container className="p-6">
            <div className="text-center">
              <Heading level="h2">Access Denied</Heading>
              <p className="text-ui-fg-subtle mt-2">
                You don't have permission to edit supplier management.
              </p>
              <Button
                onClick={() => navigate("/supplier-management/products-services")}
                className="mt-4"
              >
                Back to Products & Services
              </Button>
            </div>
          </Container>
        }
      >
        <Container className="divide-y p-0">
          <div className="flex items-center justify-between px-6 py-4">
            <div className="flex items-center gap-3">
              <Button
                variant="secondary"
                size="small"
                onClick={() =>
                  navigate(`/supplier-management/products-services/${id}`)
                }
              >
                <ArrowLeft className="w-4 h-4" />
                Back
              </Button>
              <Heading level="h1">Edit Product/Service</Heading>
            </div>
          </div>
        </Container>

        <ProductServiceForm
          mode="edit"
          initialData={productService}
          onSubmit={handleSubmit}
          onCancel={handleCancel}
          isSubmitting={isSubmitting}
        />
      </RoleGuard>

      <Toaster />
    </>
  );
};

export const config = defineRouteConfig({
  label: "Edit Product/Service",
});

export default EditProductServicePage;
