import { Buildings, PlusMini } from "@camped-ai/icons";

import { Container, Heading, Text, Button, Tooltip, Table } from "@camped-ai/ui";
import { useState, useEffect, useRef, useCallback, useMemo } from "react";
import { useNavigate, useLocation } from "react-router-dom";
import "./[slug]/modal-fix.css"; // Import custom CSS to fix z-index issues with modals
import PermissionBasedSidebarHider from "../../../widgets/permission-based-sidebar-hider";
import { useRbac } from "../../../hooks/use-rbac";
import HotelCard from "../../../components/hotel-management/HotelCard";
import HotelListItem from "../../../components/hotel-management/HotelListItem";
import { useImagePreloader } from "../../../hooks/useImagePreloader";
import { useHotelManagementWithDestinations } from "../../../hooks/hotel-management";
import type { Filter } from "../../../../components/table/data-table";
import { DataTableQuery } from "../../../../components/table/data-table/data-table-query";
import { Table as TableIcon, LayoutGrid } from "lucide-react";
import { Link } from "react-router-dom";
import { useTranslation } from "react-i18next";
import { useReactTable, getCoreRowModel, ColumnDef } from "@tanstack/react-table";

type HotelViewMode = "card" | "list";

interface ViewToggleProps {
  viewMode: HotelViewMode;
  onViewModeChange: (mode: HotelViewMode) => void;
  className?: string;
}

const ViewToggle: React.FC<ViewToggleProps> = ({
  viewMode,
  onViewModeChange,
  className = "",
}) => {
  const getViewIcon = (view: HotelViewMode) => {
    switch (view) {
      case "list":
        return <TableIcon className="w-4 h-4" />;
      case "card":
        return <LayoutGrid className="w-4 h-4" />;
      default:
        return <LayoutGrid className="w-4 h-4" />;
    }
  };

  const getViewLabel = (view: HotelViewMode) => {
    switch (view) {
      case "list":
        return "List View";
      case "card":
        return "Card View";
      default:
        return "Card View";
    }
  };

  const views: HotelViewMode[] = ["card", "list"];

  return (
    <div className={`inline-flex bg-ui-bg-subtle rounded-lg p-1 ${className}`}>
      {views.map((view) => (
        <Tooltip key={view} content={getViewLabel(view)}>
          <Button
            variant="transparent"
            size="small"
            onClick={() => onViewModeChange(view)}
            className={`px-2 py-1.5 rounded-md transition-all duration-200 ${
              viewMode === view
                ? "bg-[#165DFB] shadow-sm text-white font-medium hover:text-white hover:bg-[#336DFD]"
                : "text-ui-black hover:text-white hover:bg-[#336DFD]"
            }`}
          >
            <div className="flex items-center gap-2">{getViewIcon(view)}</div>
          </Button>
        </Tooltip>
      ))}
    </div>
  );
};

const PageClient = () => {
  const navigate = useNavigate();
  const location = useLocation();
  const { hasPermission } = useRbac();
  const { t } = useTranslation();

  const searchParams = new URLSearchParams(location.search);

  // Get current page, page size, and search term from URL params
  const currentPage = parseInt(searchParams.get("page") || "1");
  const pageSize = parseInt(searchParams.get("limit") || "12");
  // q is the canonical source of truth for search
  const searchTerm = searchParams.get("q") || "";

  // Get filter values from URL params
  const filterDestination = searchParams.get("destination") || undefined;
  const filterFeatured =
    searchParams.get("featured") === "true"
      ? true
      : searchParams.get("featured") === "false"
      ? false
      : null;
  const filterStatus =
    searchParams.get("status") === "true"
      ? true
      : searchParams.get("status") === "false"
      ? false
      : null;
  const filterStars = searchParams.get("stars")
    ? searchParams
        .get("stars")!
        .split(",")
        .map(Number)
        .filter((n) => !isNaN(n))
    : [];

  // Use the hotel management hook to fetch data
  const {
    hotels: queryHotels,
    isLoading: queryIsLoading,
    destinationsData,
    totalCount: queryTotalCount,
  } = useHotelManagementWithDestinations({
    limit: pageSize,
    offset: (currentPage - 1) * pageSize,
    // Always pass q from URL as the search string
    search: searchTerm || undefined,
    destination_id: filterDestination,
    is_featured: filterFeatured === null ? undefined : filterFeatured,
    is_active: filterStatus === null ? undefined : filterStatus,
    star_rating: filterStars,
  });

  // Use query data
  const hotels = queryHotels || [];
  const isLoading = queryIsLoading;
  const totalCount = queryTotalCount || 0;
  const destinations = destinationsData?.destinations || [];

  // Create a minimal table instance for pagination
  const dummyColumns: ColumnDef<any>[] = [{ id: 'dummy', header: 'Dummy' }];
  const table = useReactTable({
    data: hotels,
    columns: dummyColumns,
    getCoreRowModel: getCoreRowModel(),
    manualPagination: true,
    pageCount: Math.ceil(totalCount / pageSize),
    state: {
      pagination: {
        pageIndex: currentPage - 1,
        pageSize,
      },
    },
  });

  // DataTable filters to match Core pattern
  const tableFilters: Filter[] = useMemo(
    () => [
      {
        key: "destination",
        label: "Destination",
        type: "select",
        options: [
          { label: "All Destinations", value: "" },
          ...destinations.map((dest) => ({
            label: dest.name,
            value: dest.id,
          })),
        ],
      },
      {
        key: "featured",
        label: "Featured",
        type: "select",
        options: [
          { label: "All", value: "" },
          { label: "Featured", value: "true" },
          { label: "Not Featured", value: "false" },
        ],
      },
      {
        key: "status",
        label: "Status",
        type: "select",
        options: [
          { label: "All", value: "" },
          { label: "Active", value: "true" },
          { label: "Inactive", value: "false" },
        ],
      },
      {
        key: "stars",
        label: "Star Rating",
        type: "select",
        options: [
          { label: "All Ratings", value: "" },
          { label: "1 Star", value: "1" },
          { label: "2 Stars", value: "2" },
          { label: "3 Stars", value: "3" },
          { label: "4 Stars", value: "4" },
          { label: "5 Stars", value: "5" },
        ],
      },
    ],
    [destinations]
  );

  // DataTable sortable columns
  const orderBy = useMemo(
    () => [
      { key: "name", label: "Hotel Name" },
      { key: "destination_name", label: "Destination" },
      { key: "rating", label: "Rating" },
      { key: "created_at", label: "Created At" },
    ],
    []
  );

  // Helper functions to update URL parameters
  const updateParams = useCallback(
    (updates: Record<string, any>) => {
      const newParams = new URLSearchParams(location.search);
      Object.entries(updates).forEach(([key, value]) => {
        if (value === null || value === undefined || value === "") {
          newParams.delete(key);
        } else if (Array.isArray(value)) {
          if (value.length === 0) {
            newParams.delete(key);
          } else {
            newParams.set(key, value.join(","));
          }
        } else {
          newParams.set(key, String(value));
        }
      });
      navigate(`${location.pathname}?${newParams.toString()}`, {
        replace: true,
      });
    },
    [location.pathname, location.search, navigate]
  );

  const updateParamsImmediate = useCallback(
    (updates: Record<string, any>) => {
      updateParams(updates);
    },
    [updateParams]
  );

  // Simple search input state - initialize from URL
  const [searchInput, setSearchInput] = useState(searchTerm);
  const [debouncedSearchQuery, setDebouncedSearchQuery] = useState(searchTerm);

  // View toggle: Card (grid) default
  const viewMode = useMemo<HotelViewMode>(() => {
    const viewParam = searchParams.get("view") as HotelViewMode | null;
    return (viewParam as HotelViewMode) || "card";
  }, [searchParams]);

  // Debounce local input and push to URL (canonical) -> triggers data refetch
  useEffect(() => {
    const handle = setTimeout(() => {
      const currentQ = searchParams.get("q") || "";
      if ((debouncedSearchQuery || "") !== currentQ) {
        const newParams = new URLSearchParams(searchParams);
        if (debouncedSearchQuery) {
          newParams.set("q", debouncedSearchQuery);
          newParams.set("page", "1");
        } else {
          newParams.delete("q");
          newParams.set("page", "1");
        }
        // Only navigate if changed
        if (newParams.toString() !== searchParams.toString()) {
          navigate(`${location.pathname}?${newParams.toString()}`, {
            replace: true,
          });
        }
      }
    }, 300);
    return () => clearTimeout(handle);
  }, [debouncedSearchQuery, searchParams, navigate, location.pathname]);

  // Extract image URLs for preloading
  const actualPriorityImages = hotels
    .slice(0, viewMode === "card" ? 6 : 3)
    .map((hotel) => hotel.image_url)
    .filter((url): url is string => Boolean(url));

  const actualNonPriorityImages = hotels
    .slice(viewMode === "card" ? 6 : 3)
    .map((hotel) => hotel.image_url)
    .filter((url): url is string => Boolean(url));

  // Preload priority images immediately, others with delay
  useImagePreloader({ images: actualPriorityImages, priority: true });
  useImagePreloader({ images: actualNonPriorityImages, priority: false });

  // Legacy fetch functions removed - now handled by TanStack Query hooks
  // fetchDestinations() -> useDestinationsForHotelManagement()
  // fetchRoomTypes() -> removed (not needed for this page)

  // fetchDestinationDetails removed - destinations are now pre-loaded via TanStack Query

  // fetchHotels function removed - now handled by useHotelManagementWithDestinations hook

  // All hotel fetching logic removed - now handled by TanStack Query hooks

  // Keep local input in sync when URL q changes (back/forward, external updates)
  useEffect(() => {
    const urlQ = searchParams.get("q") || "";
    if (urlQ !== searchInput) {
      setSearchInput(urlQ);
      setDebouncedSearchQuery(urlQ);
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [searchTerm]);

  // Create a stable reference for filter values to detect actual changes
  const filterValues = useMemo(
    () => ({
      destination: filterDestination,
      featured: filterFeatured,
      status: filterStatus,
      stars: filterStars,
      search: searchParams.get("q") || "",
    }),
    [filterDestination, filterFeatured, filterStatus, filterStars, searchParams]
  );

  // Track previous filter values to detect changes
  const previousFilters = useRef(filterValues);

  // Reset to page 1 only when filters actually change (not when they're just active)
  useEffect(() => {
    const prev = previousFilters.current;

    // Check if any filter actually changed - using deep comparison for arrays
    const filtersChanged =
      prev.destination !== filterValues.destination ||
      prev.featured !== filterValues.featured ||
      JSON.stringify(prev.stars) !== JSON.stringify(filterValues.stars) ||
      prev.search !== filterValues.search;

    // Only reset to page 1 if filters changed AND we're not already on page 1
    if (filtersChanged && currentPage !== 1) {
      console.log("📄 Resetting to page 1 due to filter change");
      updateParamsImmediate({ page: "1" });
    }

    // Update previous filters for next comparison
    previousFilters.current = filterValues;
  }, [filterValues, currentPage, updateParamsImmediate]);

  // All filtering is server-side
  const filteredHotels = hotels;

  // Override table pagination methods to update URL
  table.nextPage = () => {
    const newPage = currentPage + 1;
    if (newPage <= Math.ceil(totalCount / pageSize)) {
      const newSearchParams = new URLSearchParams(location.search);
      newSearchParams.set('page', newPage.toString());
      navigate(`${location.pathname}?${newSearchParams.toString()}`, { replace: true });
    }
  };

  table.previousPage = () => {
    const newPage = currentPage - 1;
    if (newPage >= 1) {
      const newSearchParams = new URLSearchParams(location.search);
      if (newPage === 1) {
        newSearchParams.delete('page');
      } else {
        newSearchParams.set('page', newPage.toString());
      }
      navigate(`${location.pathname}?${newSearchParams.toString()}`, { replace: true });
    }
  };

  const hasCreate = hasPermission("hotel_management:create");
  const hasImport = hasPermission("hotel_management:import");
  const hasExport = hasPermission("hotel_management:export");

  const path = "/hotel-management/hotels";

  return (
    <>
      <PermissionBasedSidebarHider />

      <Container className="divide-y p-0">
        {/* Header */}
        <div className="flex items-center justify-between px-6 py-3">
          <div>
            <Heading level="h1" className="font-bold">Hotels</Heading>
          </div>
          <div className="flex gap-2 items-center">
            <ViewToggle
              viewMode={viewMode}
              onViewModeChange={(view) => {
                const newSearchParams = new URLSearchParams(searchParams);
                newSearchParams.set("view", view);
                navigate(`${location.pathname}?${newSearchParams.toString()}`);
              }}
            />
            {hasExport && (
              <Button
                variant="secondary"
                size="small"
                onClick={() => navigate(`${path}/export`)}
                className="flex items-center gap-2"
              >
                Export
              </Button>
            )}
            {hasImport && (
              <Button
                variant="secondary"
                size="small"
                onClick={() => navigate(`${path}/import`)}
                className="flex items-center gap-2"
              >
                Import
              </Button>
            )}
            {hasCreate && (
              <Button size="small" asChild>
                <Link to={`${path}/create`}>{t("actions.create")}</Link>
              </Button>
            )}
          </div>
        </div>

        {/* DataTable Query Controls - shown for all view modes */}
        <DataTableQuery
          search="autofocus"
          filters={tableFilters}
          orderBy={viewMode === "list" ? (orderBy as any) : undefined}
        />

        {/* Content based on view mode */}
        {false ? (
          <>
            {/* Build columns for table */}
            {/* Columns are defined outside JSX below */}
          </>
        ) : null}

        {viewMode === "card" && (
          <div className="px-6 py-4">
            {isLoading ? (
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                {[1, 2, 3, 4, 5, 6].map((i) => (
                  <div
                    key={i}
                    className="h-64 animate-pulse bg-muted rounded-lg"
                  />
                ))}
              </div>
            ) : filteredHotels.length > 0 ? (
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                {filteredHotels.map((hotel, index) => (
                  <HotelCard
                    key={hotel.id}
                    hotel={{
                      ...hotel,
                      status: hotel.is_active ? "active" : "inactive",
                    }}
                    onClick={() =>
                      navigate(`/hotel-management/hotels/${hotel.id}`)
                    }
                    priority={index < 6}
                  />
                ))}
              </div>
            ) : (
              <div className="flex flex-col items-center text-center py-12 bg-muted rounded-lg">
                <div className="flex items-center">
                  <Buildings className="w-8 h-8 text-muted-foreground mb-0.5" />
                  <Text className="text-muted-foreground mb-4">
                    {debouncedSearchQuery ||
                    filterDestination ||
                    filterStars.length > 0 ||
                    filterFeatured !== null
                      ? "No hotels match your search criteria"
                      : "No hotels found"}
                  </Text>
                </div>
                {hasPermission("hotel_management:create") && (
                  <Button
                    variant="secondary"
                    size="small"
                    onClick={() => navigate(`/hotel-management/hotels/create`)}
                  >
                    <PlusMini className="w-4 h-4 mr-2" />
                    Add your first hotel
                  </Button>
                )}
              </div>
            )}
          </div>
        )}

        {viewMode === "list" && (
          <div className="px-6 py-4">
            {isLoading ? (
              <div className="space-y-4">
                {[1, 2, 3].map((i) => (
                  <div
                    key={i}
                    className="h-24 animate-pulse bg-muted rounded-lg"
                  />
                ))}
              </div>
            ) : filteredHotels.length > 0 ? (
              <div className="space-y-4">
                {filteredHotels.map((hotel, index) => (
                  <HotelListItem
                    key={hotel.id}
                    hotel={{
                      ...hotel,
                      status: hotel.is_active ? "active" : "inactive",
                    }}
                    onClick={() =>
                      navigate(`/hotel-management/hotels/${hotel.id}`)
                    }
                    priority={index < 3}
                  />
                ))}
              </div>
            ) : (
              <div className="flex flex-col items-center text-center py-12 bg-muted rounded-lg">
                <div className="flex items-center">
                  <Buildings className="w-8 h-8 text-muted-foreground mb-0.5" />
                  <Text className="text-muted-foreground mb-4">
                    {debouncedSearchQuery ||
                    filterDestination ||
                    filterStars.length > 0 ||
                    filterFeatured !== null
                      ? "No hotels match your search criteria"
                      : "No hotels found"}
                  </Text>
                </div>
                {hasPermission("hotel_management:create") && (
                  <Button
                    variant="secondary"
                    size="small"
                    onClick={() => navigate(`/hotel-management/hotels/create`)}
                  >
                    <PlusMini className="w-4 h-4 mr-2" />
                    Add your first hotel
                  </Button>
                )}
              </div>
            )}
          </div>
        )}

        {/* Pagination Controls */}
        {totalCount > 0 && (
          <div className="border-t">
            <Table.Pagination
              canNextPage={table.getCanNextPage()}
              canPreviousPage={table.getCanPreviousPage()}
              nextPage={table.nextPage}
              previousPage={table.previousPage}
              count={totalCount}
              pageIndex={currentPage - 1}
              pageCount={table.getPageCount()}
              pageSize={pageSize}
            />
          </div>
        )}

        {/* Table view content rendered after columns and table setup */}
      </Container>
    </>
  );
};

export default PageClient;
