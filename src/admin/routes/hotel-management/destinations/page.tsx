import { defineRouteConfig } from "@camped-ai/admin-sdk";
import { Suspense } from "react";
import { Container } from "@camped-ai/ui";
import { MapPin } from "lucide-react";
import { ComponentType } from "react";
import AdminPageHelmet from "../../../components/AdminPageHelmet";
import PermissionBasedSidebarHider from "../../../widgets/permission-based-sidebar-hider";
import { Toaster } from "@camped-ai/ui";
import { LazyLoadErrorBoundary, createSafeLazyComponent } from "../../../components/common/lazy-load-error-boundary";

// Dynamically import page client for better performance with Router context safety
const PageClient = createSafeLazyComponent(() => import("./page-client"));

// Create icon component for route config
const DestinationIcon: ComponentType = (props: any) => (
  <MapPin {...props} className="h-4 w-4" />
);

const DestinationsPage = () => {
  return (
    <>
      <AdminPageHelmet />
      <PermissionBasedSidebarHider />
      <Toaster />
      <Suspense
        fallback={
          <Container className="p-6">
            <div className="flex items-center justify-center py-12">
              <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
              <span className="ml-3 text-sm text-gray-600">
                Loading carts...
              </span>
            </div>
          </Container>
        }
      >
        <PageClient />
      </Suspense>
    </>
  );
};

export default DestinationsPage;

export const config = defineRouteConfig({
  label: "Destinations",
  icon: DestinationIcon,
});

export const handle = {
  breadcrumb: () => "Destinations",
};
