import { defineRouteConfig } from "@camped-ai/admin-sdk";
import { useQuery, useQueryClient } from "@tanstack/react-query";
import { useSearchParams } from "react-router-dom";
import { useMemo, Suspense } from "react";
import { Container, Heading, Text } from "@camped-ai/ui";
import PermissionBasedSidebarHider from "../../../widgets/permission-based-sidebar-hider";
import { RoleGuard } from "../../../components/rbac/RoleGuard";
import AdminPageHelmet from "../../../components/AdminPageHelmet";
import { taskScreenLoader, type TaskScreenFilters } from "./loader";
import { LazyLoadErrorBoundary, createSafeLazyComponent } from "../../../components/common/lazy-load-error-boundary";

// Dynamically import page client for better performance with Router context safety
const TasksPageClient = createSafeLazyComponent(() => import("./page-client"));

const ConciergeTasksPage = () => {
  const [searchParams] = useSearchParams();
  const queryClient = useQueryClient();

  // Get current page and page size from URL params
  const currentPage = parseInt(searchParams.get("page") || "1");
  const pageSize = parseInt(searchParams.get("limit") || "20");
  const searchTerm = searchParams.get("q") || "";

  // Build filters for the loader
  const filters: TaskScreenFilters = useMemo(() => {
    const baseFilters: TaskScreenFilters = {
      limit: pageSize,
      offset: (currentPage - 1) * pageSize,
      sort_by: searchParams.get("order")?.replace("-", "") || "created_at",
      sort_order: searchParams.get("order")?.startsWith("-") ? "desc" : "asc",
    };

    // Add filters from URL params
    const statusFilter = searchParams.get("status");
    if (statusFilter) {
      baseFilters.status = statusFilter;
    }

    const priorityFilter = searchParams.get("priority");
    if (priorityFilter) {
      baseFilters.priority = priorityFilter;
    }

    const entityTypeFilter = searchParams.get("entity_type");
    if (entityTypeFilter) {
      baseFilters.entity_type = entityTypeFilter;
    }

    const assignedToFilter = searchParams.get("assigned_to");
    if (assignedToFilter) {
      baseFilters.assigned_to = assignedToFilter;
    }

    if (searchTerm) {
      baseFilters.q = searchTerm;
    }

    return baseFilters;
  }, [searchParams, currentPage, pageSize, searchTerm]);

  // Use the task screen loader to fetch tasks
  const {
    data: taskData,
    isLoading: tasksLoading,
    error: tasksError,
  } = useQuery({
    queryKey: ["task-screen", filters],
    queryFn: () => taskScreenLoader(queryClient)(filters),
    staleTime: 5 * 60 * 1000, // 5 minutes
    gcTime: 10 * 60 * 1000, // 10 minutes
    refetchOnWindowFocus: false,
  });

  // Extract data from queries
  const tasks = taskData?.tasks || [];
  const totalCount = taskData?.count || 0;
  const isLoading = tasksLoading;

  // Handle errors
  if (tasksError) {
    console.error("Error fetching tasks:", tasksError);
  }

  return (
    <>
      <AdminPageHelmet />
      <PermissionBasedSidebarHider />
      <RoleGuard
        requirePermission="concierge_management:view"
        fallback={
          <Container>
            <div className="p-8 text-center">
              <Heading level="h1">Access Denied</Heading>
              <Text className="mt-2">
                You don't have permission to view tasks.
              </Text>
            </div>
          </Container>
        }
      >
        <Suspense
          fallback={
            <Container className="p-6">
              <div className="flex items-center justify-center py-12">
                <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
                <span className="ml-3 text-sm text-gray-600">Loading tasks...</span>
              </div>
            </Container>
          }
        >
          <TasksPageClient
            tasks={tasks}
            isLoading={isLoading}
            totalCount={totalCount}
            pageSize={pageSize}
          />
        </Suspense>
      </RoleGuard>
    </>
  );
};

export const config = defineRouteConfig({
  label: "Tasks",
});

export default ConciergeTasksPage;
