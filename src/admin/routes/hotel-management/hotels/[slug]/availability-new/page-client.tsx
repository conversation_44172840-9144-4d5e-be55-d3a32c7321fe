import { useState, useEffect } from "react";
import {
  Ta<PERSON>,
  Container,
  <PERSON>ing,
  Button,
  Text,
  IconButton,
} from "@camped-ai/ui";
import { Calendar, Settings, Upload, Download, ArrowLeft } from "lucide-react";
import SimpleRoomCalendar from "../../../../../components/hotel/simple-room-calendar";
import { useTimelineData } from "../../../../../components/hotel/room-availablity/hooks/useTimelineData";
import { RoomInventoryStatus } from "../../../../../components/hotel/types/booking";
import { getStatusDisplayName } from "../../../../../components/hotel/room-availablity/statusUtils";
import FilterControls from "../../../../../components/hotel/room-availablity/FilterControls";
import { ConfigureTabContent } from "../../../../../components/hotel/room-availability";
import BulkImportModal from "../../../../../components/room-inventory/bulk-import-modal";
import ExportModal from "../../../../../components/room-inventory/export-modal";
import { useParams, useNavigate } from "react-router-dom";
import PermissionBasedSidebarHider from "../../../../../widgets/permission-based-sidebar-hider";
import { useRbac } from "../../../../../hooks/use-rbac";

// Type definition for hotel
interface Hotel {
  id: string;
  name: string;
  slug: string;
  // Add other hotel properties as needed
}

const PageClient = () => {
  const { slug } = useParams();
  const navigate = useNavigate();
  const { hasPermission, hasAnyPermission } = useRbac();
  const [activeTab, setActiveTab] = useState("timeline");

  // Hotel data state
  const [hotel, setHotel] = useState<Hotel | null>(null);
  const [isLoading, setIsLoading] = useState(false);

  // Fetch hotel details
  const fetchHotelAndAvailability = async () => {
    try {
      setIsLoading(true);
      const hotelResponse = await fetch(
        `/admin/hotel-management/hotels/${slug}`
      );
      if (!hotelResponse.ok) {
        throw new Error("Failed to fetch hotel details");
      }

      const hotelData = await hotelResponse.json();
      // Check if hotel data is in the expected format
      const hotel =
        hotelData.hotel && hotelData.hotel[0]
          ? hotelData.hotel[0]
          : hotelData.hotel;
      setHotel(hotel);
    } finally {
      setIsLoading(false);
    }
  };

  // Load hotel data on component mount
  useEffect(() => {
    if (slug) {
      fetchHotelAndAvailability();
    }
  }, [slug]);

  // Handle back button with browser history and fallback
  const handleBackClick = () => {
    // Check if there's browser history to go back to
    if (window.history.length > 1) {
      window.history.back();
    } else {
      // Fallback to hotel detail page if no history
      navigate(`/hotel-management/hotels/${slug}`);
    }
  };

  // Configure Availability tab state
  const [selectedRoomConfigs, setSelectedRoomConfigs] = useState<string[]>([]);
  const [selectedBulkRooms, setSelectedBulkRooms] = useState<string[]>([]);
  const [bulkStartDate, setBulkStartDate] = useState<Date>(new Date());
  const [bulkEndDate, setBulkEndDate] = useState<Date>(() => {
    const date = new Date();
    date.setDate(date.getDate() + 6);
    return date;
  });
  const [selectedStatus, setSelectedStatus] =
    useState<RoomInventoryStatus | null>(null);
  const [isUpdating, setIsUpdating] = useState(false);
  const [updateMessage, setUpdateMessage] = useState<{
    type: "success" | "error" | "warning";
    text: string;
  } | null>(null);
  const [bulkUpdateNotes, setBulkUpdateNotes] = useState("");

  // Modal states
  const [importModalOpen, setImportModalOpen] = useState(false);
  const [exportModalOpen, setExportModalOpen] = useState(false);

  // Get room types and rooms from timeline data
  const {
    selectedRoomTypes,
    setSelectedRoomTypes,
    selectedRooms,
    setSelectedRooms,
    selectedStatuses,
    setSelectedStatuses,
    startDate,
    endDate,
    dateSlots,
    groupedRooms,
    roomTypes,
    roomTypeConfigs,
    rooms,
    isLoading: timelineLoading,
    refreshData,
  } = useTimelineData(slug);

  // Handle apply date range - refresh data with new dates
  const handleApplyDateRange = (newStartDate: Date, newEndDate: Date) => {
    refreshData?.(newStartDate, newEndDate);
  };

  // Wrapper function to handle status type conversion
  const handleStatusChange = (status: any) => {
    setSelectedStatus(status);
  };

  // Handle bulk status update
  const handleBulkUpdate = async () => {
    if (
      !selectedStatus ||
      (selectedRoomConfigs.length === 0 && selectedBulkRooms.length === 0)
    ) {
      setUpdateMessage({
        type: "error",
        text: "Please select room configurations or specific rooms, and a status.",
      });
      return;
    }

    setIsUpdating(true);
    setUpdateMessage(null);

    try {
      // Get affected rooms - prioritize specific room selection over configuration selection
      let affectedRooms = [];

      // If specific rooms are selected, use only those rooms (ignore config selection)
      if (selectedBulkRooms.length > 0) {
        const specificRooms = rooms.filter((room) =>
          selectedBulkRooms.includes(room.id)
        );
        affectedRooms.push(...specificRooms);
      }
      // Otherwise, if only configurations are selected, use all rooms from those configs
      else if (selectedRoomConfigs.length > 0) {
        const configRooms = Object.entries(groupedRooms)
          .filter(([configName]) => selectedRoomConfigs.includes(configName))
          .flatMap(([, rooms]) => rooms);
        affectedRooms.push(...configRooms);
      }

      // Helper function to convert Date to YYYY-MM-DD string (avoiding timezone issues)
      const formatDateForAPI = (date: Date): string => {
        const year = date.getFullYear();
        const month = String(date.getMonth() + 1).padStart(2, '0');
        const day = String(date.getDate()).padStart(2, '0');
        return `${year}-${month}-${day}`;
      };

      // Convert dates to date-only strings to avoid timezone conversion issues
      const startDateString = formatDateForAPI(bulkStartDate);
      const endDateString = formatDateForAPI(bulkEndDate);

      console.log(`[AVAILABILITY UPDATE] Converting dates: ${bulkStartDate.toDateString()} → ${startDateString}, ${bulkEndDate.toDateString()} → ${endDateString}`);

      // Make API calls for each affected room
      const updatePromises = affectedRooms.map(async (room) => {
        try {
          const response = await fetch(
            "/admin/hotel-management/room-inventory/bulk-update",
            {
              method: "POST",
              headers: {
                "Content-Type": "application/json",
              },
              body: JSON.stringify({
                room_id: room.id,
                start_date: startDateString,
                end_date: endDateString,
                status: selectedStatus,
                metadata: {
                  notes:
                    bulkUpdateNotes ||
                    `Bulk update to ${getStatusDisplayName(selectedStatus)}`,
                  check_in_time: "14:00",
                  check_out_time: "12:00",
                  is_noon_to_noon: true,
                },
              }),
            }
          );

          if (!response.ok) {
            const errorData = await response.json();
            throw new Error(
              errorData.message ||
                `Failed to update room ${room.name || room.id}`
            );
          }

          return { success: true, room: room };
        } catch (error) {
          console.error(`Error updating room ${room.id}:`, error);
          return {
            success: false,
            room: room,
            error: error instanceof Error ? error.message : String(error),
          };
        }
      });

      // Wait for all updates to complete
      const results = await Promise.all(updatePromises);
      const successfulUpdates = results.filter((result) => result.success);
      const failedUpdates = results.filter((result) => !result.success);

      // Check for booking conflicts or other failures
      if (failedUpdates.length > 0) {
        const errorMessages = failedUpdates
          .map(
            (result) => `${result.room.name || result.room.id}: ${result.error}`
          )
          .join(", ");

        if (successfulUpdates.length > 0) {
          setUpdateMessage({
            type: "warning",
            text: `${successfulUpdates.length} rooms updated successfully. ${failedUpdates.length} rooms failed: ${errorMessages}`,
          });
        } else {
          setUpdateMessage({
            type: "error",
            text: `All updates failed: ${errorMessages}`,
          });
          setIsUpdating(false);
          return;
        }
      }

      setUpdateMessage({
        type: "success",
        text: `Successfully updated ${
          successfulUpdates.length
        } rooms to ${getStatusDisplayName(selectedStatus)}.${
          bulkUpdateNotes ? ` Note: ${bulkUpdateNotes}` : ""
        }`,
      });

      // Refresh the data to show updated availability without page reload
      if (refreshData && successfulUpdates.length > 0) {
        refreshData(bulkStartDate, bulkEndDate);
      }

      // Reset form
      setSelectedRoomConfigs([]);
      setSelectedBulkRooms([]);
      setSelectedStatus(null);
      setBulkUpdateNotes("");

      // Auto-hide success message after 5 seconds
      setTimeout(() => {
        setUpdateMessage(null);
      }, 5000);
    } catch (error) {
      setUpdateMessage({
        type: "error",
        text: "Failed to update room availability. Please try again.",
      });
    } finally {
      setIsUpdating(false);
    }
  };



  // Check if user has rooms:availability or rooms:view permission (consolidated room management)
  const hasAvailabilityAccess = hasAnyPermission(["rooms:availability", "rooms:view"]);

  // Check if user has edit permissions (rooms:availability or rooms:edit)
  const hasEditPermission = hasAnyPermission(["rooms:availability", "rooms:edit"]);

  // Show access denied if user doesn't have required permissions
  if (!hasAvailabilityAccess) {
    return (
      <>
        <PermissionBasedSidebarHider />
        <Container className="p-6">
          <div className="text-center">
            <Heading level="h2">Access Denied</Heading>
            <Text className="text-ui-fg-subtle mt-2">
              You don't have permission to manage hotel room availability.
              <br />
              Required permissions: rooms:availability or rooms:view
            </Text>
          </div>
        </Container>
      </>
    );
  }

  return (
    <>
      <PermissionBasedSidebarHider />
      <div className="flex flex-col bg-background">
        {/* Header Section - Outside Container */}
        <div className="mb-6 pt-2">
          <div className="flex items-center gap-4">
            <IconButton
              onClick={handleBackClick}
              title="Go back to previous page"
            >
              <ArrowLeft className="w-4 h-4" />
            </IconButton>
            <Heading level="h1">
              {hotel?.name || "Hotel"} - Room Availability
            </Heading>
          </div>
        </div>

        <Container className="p-0 overflow-hidden">
          <Tabs value={activeTab} onValueChange={setActiveTab}>
            <Tabs.List className="px-4 pt-4">
              <Tabs.Trigger value="timeline">
                <Calendar className="w-4 h-4 mr-2" />
                Calendar View
              </Tabs.Trigger>
              <Tabs.Trigger value="configure">
                <Settings className="w-4 h-4 mr-2" />
                Configure Availability
              </Tabs.Trigger>
            </Tabs.List>

            <Tabs.Content value="timeline" className="flex-1 h-full flex flex-col">
              {/* Filter Controls */}
              <div className="bg-card border-b border-border py-6 px-6 flex-shrink-0">
                <FilterControls
                  selectedRoomTypes={selectedRoomTypes}
                  onRoomTypesChange={setSelectedRoomTypes}
                  selectedRooms={selectedRooms}
                  onRoomsChange={setSelectedRooms}
                  selectedStatuses={selectedStatuses}
                  onStatusesChange={setSelectedStatuses}
                  startDate={startDate}
                  endDate={endDate}
                  roomTypes={roomTypes}
                  roomTypeConfigs={roomTypeConfigs}
                  rooms={rooms}
                  onApplyDateRange={handleApplyDateRange}
                  isLoading={timelineLoading}
                />
              </div>

              {/* Calendar Content */}
              <div className="flex-1">
                {hotel?.id && (
                  <SimpleRoomCalendar
                    hotelId={hotel.id}
                    selectedRoomTypes={selectedRoomTypes}
                    selectedRooms={selectedRooms}
                    selectedStatuses={selectedStatuses}
                    externalStartDate={startDate}
                    externalEndDate={endDate}
                    onDateRangeChange={handleApplyDateRange}
                  />
                )}
              </div>
            </Tabs.Content>

            <Tabs.Content value="configure" className="p-6">
              <Container>
                <div className="flex justify-between items-start mb-6">
                  <div>
                    <Heading level="h2" className="">
                      Configure Room Availability
                    </Heading>
                    <Text size="base" weight="regular" family="sans">
                      Bulk update room availability for selected date ranges and
                      rooms.
                    </Text>
                  </div>
                  <div className="flex gap-2">
                    {hasPermission("rooms:availability") && (
                      <Button
                        variant="secondary"
                        size="small"
                        onClick={() => setExportModalOpen(true)}
                        className="flex items-center gap-2"
                      >
                        <Download className="w-4 h-4" />
                        Export
                      </Button>
                    )}
                    {hasPermission("rooms:availability") && (
                      <Button
                        variant="secondary"
                        size="small"
                        onClick={() => setImportModalOpen(true)}
                        className="flex items-center gap-2"
                      >
                        <Upload className="w-4 h-4" />
                        Import
                      </Button>
                    )}
                  </div>
                </div>

                <ConfigureTabContent
                  selectedRoomConfigs={selectedRoomConfigs}
                  setSelectedRoomConfigs={setSelectedRoomConfigs}
                  selectedBulkRooms={selectedBulkRooms}
                  setSelectedBulkRooms={setSelectedBulkRooms}
                  bulkStartDate={bulkStartDate}
                  setBulkStartDate={setBulkStartDate}
                  bulkEndDate={bulkEndDate}
                  setBulkEndDate={setBulkEndDate}
                  selectedStatus={selectedStatus}
                  setSelectedStatus={handleStatusChange}
                  bulkUpdateNotes={bulkUpdateNotes}
                  setBulkUpdateNotes={setBulkUpdateNotes}
                  groupedRooms={groupedRooms}
                  rooms={rooms}
                  handleBulkUpdate={handleBulkUpdate}
                  updateMessage={updateMessage}
                  isUpdating={isUpdating}
                  hasEditPermission={hasEditPermission}
                />
              </Container>
            </Tabs.Content>

          </Tabs>
        </Container>

        {/* Bulk Import Modal */}
        <BulkImportModal
          open={importModalOpen}
          onClose={() => {
            setImportModalOpen(false);
            // Refresh data when modal closes with a slight delay to ensure server has processed everything
            setTimeout(() => {
              console.log("Refreshing availability data after import");
              // Refresh with current date range or default range
              const today = new Date();
              const nextMonth = new Date(today);
              nextMonth.setMonth(today.getMonth() + 1);
              refreshData(today, nextMonth);
            }, 500);
          }}
          hotelId={slug}
        />

        {/* Export Modal */}
        <ExportModal
          open={exportModalOpen}
          onClose={() => setExportModalOpen(false)}
          hotelId={slug}
          currentHotel={hotel}
        />
      </div>
    </>
  );
};

export default PageClient;
