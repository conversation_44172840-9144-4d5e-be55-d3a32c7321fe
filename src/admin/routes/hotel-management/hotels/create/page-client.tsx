import React, { useEffect, useState } from "react";
import { use<PERSON><PERSON>, Controller } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { z } from "zod";
import { Save, X } from "lucide-react";
import {
  Container,
  Heading,
  Text,
  Button,
  Input,
  Textarea,
  Label,
  toast,
  Badge,
  InlineTip,
  Select,
} from "@camped-ai/ui";
import { useSearchParams } from "react-router-dom";
import { CurrencySelector } from "../../../../components/common/currency-selector";
import VisibilitySettings from "../../../../components/visibility-settings";
import HotelMediaSection from "../../../../components/hotel/hotel-media-section";
import LanguageSelector from "../../../../components/language-selector";
import { TextareaField } from "../../../../components/ai-enhanced-inputs";
import { useProjectLanguages } from "../../../../hooks/languages/useProjectLanguages";
import { useDestinationsForHotelForm, useDestination } from "../../../../hooks/supplier-products-services/use-destinations";
import { isValidUrl } from "../../../../../utils/url-validation";

// Form validation schema
const hotelSchema = z.object({
  // Basic Info
  name: z.string().min(1, "Name is required"),
  handle: z.string().min(1, "Handle is required"),
  description: z.string().optional(),

  // Status
  is_active: z.boolean().default(true),
  is_featured: z.boolean().default(false),

  // Location & Contact
  destination_id: z.string().min(1, "Destination is required"),
  location: z.string().optional(),
  address: z.string().optional(),
  phone_number: z.string().optional(),
  email: z.string().email("Invalid email format").optional().or(z.literal("")),
  website: z
    .string()
    .optional()
    .refine((url) => !url || isValidUrl(url), {
      message:
        "Must be a valid URL (e.g., https://example.com or www.example.com)",
    }),

  // Business Info
  currency_code: z.string().optional(),
  margin: z.number().min(0).max(100).optional(),
  timezone: z.string().optional(),
  tax_type: z.string().optional(),
  tax_number: z.string().optional(),

  // Hotel Specific
  rating: z.number().min(0).max(5).optional(),
  total_reviews: z.number().min(0).optional(),
  check_in_time: z.string().optional(),
  check_out_time: z.string().optional(),
  is_pets_allowed: z.boolean().default(false),

  // Arrays
  tags: z.array(z.string()).optional(),
  amenities: z.array(z.string()).optional(),
  rules: z.array(z.string()).optional(),
  safety_measures: z.array(z.string()).optional(),
  available_languages: z.array(z.string()).optional(),

  // Media and Notes
  media: z.array(z.any()).optional(),
  notes: z.string().optional(),
});

export type HotelFormData = z.infer<typeof hotelSchema>;

interface HotelCreateFormProps {
  onSubmit: (data: HotelFormData) => Promise<void>;
  onCancel: () => void;
  isSubmitting?: boolean;
}

const HotelCreateForm: React.FC<HotelCreateFormProps> = ({
  onSubmit,
  onCancel,
  isSubmitting = false,
}) => {
  const [tags, setTags] = useState<string[]>([]);
  const [tagInput, setTagInput] = useState("");
  const [amenities, setAmenities] = useState<string[]>([]);
  const [amenityInput, setAmenityInput] = useState("");
  const [rules, setRules] = useState<string[]>([]);
  const [ruleInput, setRuleInput] = useState("");
  const [safetyMeasures, setSafetyMeasures] = useState<string[]>([]);
  const [safetyInput, setSafetyInput] = useState("");
  const [selectedLanguage, setSelectedLanguage] = useState("en");

  // Language management
  const { languages: tolgeeLanguages } = useProjectLanguages();
  const isBaseLanguage = selectedLanguage === "en";

  // Destinations data
  const {
    data: destinationsData,
    isLoading: destinationsLoading,
    error: destinationsError,
  } = useDestinationsForHotelForm();
  const destinations = destinationsData?.destinations || [];

 

  // Form setup
  const form = useForm<HotelFormData>({
    resolver: zodResolver(hotelSchema),
    defaultValues: {
      name: "",
      handle: "",
      description: "",
      is_active: true,
      is_featured: false,
      destination_id: "",
      location: "",
      address: "",
      phone_number: "",
      email: "",
      website: "",
      currency_code: "GBP",
      margin: undefined,
      timezone: "",
      tax_type: "",
      tax_number: "",
      rating: undefined,
      total_reviews: undefined,
      check_in_time: "",
      check_out_time: "",
      is_pets_allowed: false,
      tags: [],
      amenities: [],
      rules: [],
      safety_measures: [],
      available_languages: [],
      media: [],
      notes: "",
    },
  });

  const {
    handleSubmit,
    control,
    watch,
    setValue,
    formState: { errors },
  } = form;

  // Watch destination_id for auto-population
  const watchedDestinationId = watch("destination_id");

  // Fetch selected destination details for auto-population
  const {
    data: selectedDestinationData,
    isLoading: destinationDetailsLoading,
  } = useDestination(watchedDestinationId);

  // Prefill destination from query param if present
  const [searchParams] = useSearchParams();
  const prefilledDestinationId = searchParams.get("destination_id") || "";

  useEffect(() => {
    if (prefilledDestinationId) {
      setValue("destination_id", prefilledDestinationId, { shouldDirty: false, shouldTouch: false });
    }
  }, [prefilledDestinationId, setValue]);

  // Auto-generate handle from name
  const watchedName = watch("name");
  React.useEffect(() => {
    if (watchedName) {
      const handle = watchedName
        .toLowerCase()
        .replace(/[^a-z0-9\s-]/g, "")
        .replace(/\s+/g, "-")
        .replace(/-+/g, "-")
        .trim();
      setValue("handle", handle);
    }
  }, [watchedName, setValue]);

  // Auto-populate currency and margin from selected destination (backup mechanism)
  React.useEffect(() => {
    if (watchedDestinationId && !destinationDetailsLoading) {
      // Try to get destination from the list first (fallback)
      const destinationFromList = destinations.find(d => d.id === watchedDestinationId);

      // Use detailed destination data if available, otherwise fallback to list data
      const destination = selectedDestinationData?.destination || destinationFromList;

      if (destination) {
        // Auto-populate currency if destination has one and hotel field is empty or default
        if (destination.currency_code) {
          const currentCurrency = watch("currency_code");
          if (!currentCurrency || currentCurrency === "GBP") {
            setValue("currency_code", destination.currency_code, {
              shouldDirty: true,
              shouldTouch: true,
              shouldValidate: true
            });
          }
        }

        // Auto-populate margin if destination has one and hotel field is empty
        if (destination.margin !== undefined && destination.margin !== null) {
          const currentMargin = watch("margin");
          if (currentMargin === undefined || currentMargin === null) {
            setValue("margin", destination.margin, {
              shouldDirty: true,
              shouldTouch: true,
              shouldValidate: true
            });
          }
        }
      }
    }
  }, [selectedDestinationData, destinationDetailsLoading, setValue, watch, watchedDestinationId, destinations]);

  // Tag management functions
  const addTag = () => {
    if (tagInput.trim() && !tags.includes(tagInput.trim())) {
      const newTags = [...tags, tagInput.trim()];
      setTags(newTags);
      setValue("tags", newTags);
      setTagInput("");
    }
  };

  const removeTag = (tagToRemove: string) => {
    const newTags = tags.filter((tag) => tag !== tagToRemove);
    setTags(newTags);
    setValue("tags", newTags);
  };

  // Amenity management functions
  const addAmenity = () => {
    if (amenityInput.trim() && !amenities.includes(amenityInput.trim())) {
      const newAmenities = [...amenities, amenityInput.trim()];
      setAmenities(newAmenities);
      setValue("amenities", newAmenities);
      setAmenityInput("");
    }
  };

  const removeAmenity = (amenityToRemove: string) => {
    const newAmenities = amenities.filter(
      (amenity) => amenity !== amenityToRemove
    );
    setAmenities(newAmenities);
    setValue("amenities", newAmenities);
  };

  // Rule management functions
  const addRule = () => {
    if (ruleInput.trim() && !rules.includes(ruleInput.trim())) {
      const newRules = [...rules, ruleInput.trim()];
      setRules(newRules);
      setValue("rules", newRules);
      setRuleInput("");
    }
  };

  const removeRule = (ruleToRemove: string) => {
    const newRules = rules.filter((rule) => rule !== ruleToRemove);
    setRules(newRules);
    setValue("rules", newRules);
  };

  // Safety measure management functions
  const addSafetyMeasure = () => {
    if (safetyInput.trim() && !safetyMeasures.includes(safetyInput.trim())) {
      const newSafetyMeasures = [...safetyMeasures, safetyInput.trim()];
      setSafetyMeasures(newSafetyMeasures);
      setValue("safety_measures", newSafetyMeasures);
      setSafetyInput("");
    }
  };

  const removeSafetyMeasure = (safetyToRemove: string) => {
    const newSafetyMeasures = safetyMeasures.filter(
      (safety) => safety !== safetyToRemove
    );
    setSafetyMeasures(newSafetyMeasures);
    setValue("safety_measures", newSafetyMeasures);
  };

  const handleFormSubmit = async (data: HotelFormData) => {
    try {
      await onSubmit(data);
    } catch (error) {
      console.error("Form submission error:", error);
      toast.error("Failed to create hotel. Please try again.");
    }
  };

  return (
    <div className="space-y-3">
      {/* Form Header with Language Selector */}
      <div className="flex items-center justify-between">
        <div>
          <Heading level="h1">Add New Hotel</Heading>
        </div>
        <LanguageSelector
          selectedLanguage={selectedLanguage}
          onLanguageChange={setSelectedLanguage}
        />
      </div>

      {/* Translation Mode Banner */}
      {!isBaseLanguage && (
        <InlineTip label="Translation Mode">
          You are editing in{" "}
          <strong>
            {tolgeeLanguages.find((lang) => lang.tag === selectedLanguage)
              ?.name || selectedLanguage}
          </strong>{" "}
          language. Only translatable fields (name, description, location,
          address, notes, tags, amenities, rules, safety measures) can be
          edited. Other fields show base language values and are read-only.
        </InlineTip>
      )}

      {/* Hotel Information */}
      <Container className="divide-y p-0">
        <div className="flex items-center justify-between px-6 py-4">
          <Heading level="h2">Hotel Information</Heading>
        </div>
        <div className="px-6 py-4">
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <div className="space-y-2">
              <Label>Hotel Name *</Label>
              <Controller
                name="name"
                control={control}
                render={({ field }) => (
                  <>
                    <Input {...field} placeholder="Enter hotel name" />
                    {errors.name && (
                      <Text className="text-red-600 text-sm mt-1">
                        {errors.name.message}
                      </Text>
                    )}
                  </>
                )}
              />
            </div>

            <div className="space-y-2">
              <Label>URL Handle *</Label>
              <Controller
                name="handle"
                control={control}
                render={({ field }) => (
                  <>
                    <Input {...field} placeholder="url-friendly-handle" />
                    {errors.handle && (
                      <Text className="text-red-600 text-sm mt-1">
                        {errors.handle.message}
                      </Text>
                    )}
                  </>
                )}
              />
            </div>

            <div className="space-y-2">
              <Label>Destination *</Label>
              <Controller
                name="destination_id"
                control={control}
                render={({ field }) => (
                  <Select
                    value={field.value || ""}
                    onValueChange={(value) => {
                      field.onChange(value);
                      console.log("Destination changed to:", value);
                      // Force trigger auto-population after a short delay
                      setTimeout(() => {
                        const destinationFromList = destinations.find(d => d.id === value);
                        if (destinationFromList) {
                          console.log("Manual trigger for destination:", destinationFromList);

                          // Auto-populate currency
                          if (destinationFromList.currency_code) {
                            const currentCurrency = watch("currency_code");
                            if (!currentCurrency || currentCurrency === "GBP") {
                              console.log("Setting currency to:", destinationFromList.currency_code);
                              setValue("currency_code", destinationFromList.currency_code, {
                                shouldDirty: true,
                                shouldTouch: true,
                                shouldValidate: true
                              });
                            }
                          }

                          // Auto-populate margin
                          if (destinationFromList.margin !== undefined && destinationFromList.margin !== null) {
                            const currentMargin = watch("margin");
                            if (currentMargin === undefined || currentMargin === null) {
                              console.log("Setting margin to:", destinationFromList.margin);
                              setValue("margin", destinationFromList.margin, {
                                shouldDirty: true,
                                shouldTouch: true,
                                shouldValidate: true
                              });
                            }
                          }
                        }
                      }, 100);
                    }}
                    disabled={destinationsLoading}
                  >
                    <Select.Trigger>
                      <Select.Value
                        placeholder={
                          destinationsLoading
                            ? "Loading destinations..."
                            : destinationsError
                            ? "Failed to load destinations"
                            : "Select a destination"
                        }
                      />
                    </Select.Trigger>
                    <Select.Content>
                      {destinationsLoading && (
                        <div className="flex items-center gap-2 px-3 py-2 text-sm text-ui-fg-subtle">
                          Loading destinations...
                        </div>
                      )}
                      {destinationsError && (
                        <div className="px-3 py-2 text-sm text-red-600">
                          Failed to load destinations
                        </div>
                      )}
                      {!destinationsLoading &&
                        !destinationsError &&
                        destinations.length === 0 && (
                          <div className="px-3 py-2 text-sm text-ui-fg-subtle">
                            No destinations available
                          </div>
                        )}
                      {destinations.map((destination) => (
                        <Select.Item
                          key={destination.id}
                          value={destination.id}
                        >
                          {destination.name}
                          {destination.location
                            ? ` (${destination.location})`
                            : ""}
                          {destination.currency_code && ` [${destination.currency_code}]`}
                          {destination.margin !== undefined && ` (${destination.margin}%)`}
                        </Select.Item>
                      ))}
                    </Select.Content>
                  </Select>
                )}
              />
              {errors.destination_id && (
                <Text className="text-red-600 text-sm mt-1">
                  {errors.destination_id.message}
                </Text>
              )}
              {watchedDestinationId && destinationDetailsLoading && (
                <Text className="text-blue-600 text-sm mt-1">
                  
                </Text>
              )}
              {(() => {
                const destinationFromList = destinations.find(d => d.id === watchedDestinationId);
                const destination = selectedDestinationData?.destination || destinationFromList;

                if (destination && (destination.currency_code || destination.margin !== undefined)) {
                  return (
                    <Text className="text-green-600 text-sm mt-1">
                    </Text>
                  );
                }
                return null;
              })()}
            </div>
          </div>

          <div className="mt-4 space-y-2">
            <Controller
              name="description"
              control={control}
              render={({ field }) => (
                <TextareaField
                  label="Description"
                  value={field.value || ""}
                  onChange={field.onChange}
                  placeholder="Enter hotel description"
                  contentType="description"
                  context={{
                    name: watch("name"),
                    location: watch("location"),
                    destination_id: watch("destination_id"),
                    type: "hotel",
                  }}
                  rows={4}
                />
              )}
            />
          </div>
        </div>
      </Container>

      {/* Location & Contact Details */}
      <Container className="divide-y p-0">
        <div className="flex items-center justify-between px-6 py-4">
          <Heading level="h2">Location & Contact Details</Heading>
        </div>
        <div className="px-6 py-4">
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <div className="space-y-2">
              <Label>Address</Label>
              <Controller
                name="address"
                control={control}
                render={({ field }) => (
                  <Input
                    {...field}
                    placeholder="Full address"
                    value={field.value || ""}
                  />
                )}
              />
            </div>

            <div className="space-y-2">
              <Label>City/Location</Label>
              <Controller
                name="location"
                control={control}
                render={({ field }) => (
                  <Input
                    {...field}
                    placeholder="e.g., City Center, Downtown"
                    value={field.value || ""}
                  />
                )}
              />
            </div>
          </div>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mt-4">
            <div className="space-y-2">
              <Label>Phone Number</Label>
              <Controller
                name="phone_number"
                control={control}
                render={({ field }) => (
                  <Input
                    {...field}
                    placeholder="****** 567 8900"
                    value={field.value || ""}
                  />
                )}
              />
            </div>

            <div className="space-y-2">
              <Label>Email</Label>
              <Controller
                name="email"
                control={control}
                render={({ field }) => (
                  <>
                    <Input
                      {...field}
                      type="email"
                      placeholder="<EMAIL>"
                      value={field.value || ""}
                    />
                    {errors.email && (
                      <Text className="text-red-600 text-sm mt-1">
                        {errors.email.message}
                      </Text>
                    )}
                  </>
                )}
              />
            </div>

            <div className="space-y-2">
              <Label>Website</Label>
              <Controller
                name="website"
                control={control}
                render={({ field }) => (
                  <>
                    <Input
                      {...field}
                      placeholder="https://hotel-website.com"
                      value={field.value || ""}
                    />
                    {errors.website && (
                      <Text className="text-red-600 text-sm mt-1">
                        {errors.website.message}
                      </Text>
                    )}
                  </>
                )}
              />
            </div>
          </div>
        </div>
      </Container>

      {/* Hotel Details */}
      <Container className="divide-y p-0">
        <div className="flex items-center justify-between px-6 py-4">
          <Heading level="h2">Hotel Details</Heading>
        </div>
        <div className="px-6 py-4">
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <div className="space-y-2">
              <Label>Rating (1-5)</Label>
              <Controller
                name="rating"
                control={control}
                render={({ field }) => (
                  <Input
                    {...field}
                    type="number"
                    min="0"
                    max="5"
                    step="0.1"
                    placeholder="4.5"
                    value={field.value || ""}
                    onChange={(e) => {
                      const value = e.target.value;
                      field.onChange(value ? parseFloat(value) : undefined);
                    }}
                  />
                )}
              />
            </div>

            <div className="space-y-2">
              <Label>Total Reviews</Label>
              <Controller
                name="total_reviews"
                control={control}
                render={({ field }) => (
                  <Input
                    {...field}
                    type="number"
                    min="0"
                    placeholder="150"
                    value={field.value || ""}
                    onChange={(e) => {
                      const value = e.target.value;
                      field.onChange(value ? parseInt(value) : undefined);
                    }}
                  />
                )}
              />
            </div>

            <div className="space-y-2">
              <Label>Timezone</Label>
              <Controller
                name="timezone"
                control={control}
                render={({ field }) => (
                  <Input
                    {...field}
                    placeholder="Europe/London"
                    value={field.value || ""}
                  />
                )}
              />
            </div>

            <div className="space-y-2">
              <Label>Check-in Time</Label>
              <Controller
                name="check_in_time"
                control={control}
                render={({ field }) => (
                  <Input {...field} type="time" value={field.value || ""} />
                )}
              />
            </div>

            <div className="space-y-2">
              <Label>Check-out Time</Label>
              <Controller
                name="check_out_time"
                control={control}
                render={({ field }) => (
                  <Input {...field} type="time" value={field.value || ""} />
                )}
              />
            </div>
          </div>

          <div className="mt-4 space-y-2">
            <Label>Notes</Label>
            <Controller
              name="notes"
              control={control}
              render={({ field }) => (
                <Textarea
                  {...field}
                  placeholder="Internal notes about this hotel"
                  rows={3}
                  value={field.value || ""}
                />
              )}
            />
          </div>
        </div>
      </Container>

      {/* Financial Settings */}
      <Container className="divide-y p-0">
        <div className="flex items-center justify-between px-6 py-4">
          <Heading level="h2">Financial Settings</Heading>
        </div>
        <div className="px-6 py-4">
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <div className="space-y-2">
              <Label>Currency</Label>
              <Controller
                name="currency_code"
                control={control}
                render={({ field }) => (
                  <CurrencySelector
                    value={field.value || ""}
                    onValueChange={field.onChange}
                  />
                )}
              />
              {(() => {
                const destinationFromList = destinations.find(d => d.id === watchedDestinationId);
                const destination = selectedDestinationData?.destination || destinationFromList;
                const currentCurrency = watch("currency_code");

                if (destination?.currency_code && currentCurrency === destination.currency_code) {
                  return (
                    <Text className="text-blue-600 text-sm mt-1">
                      
                    </Text>
                  );
                }
                return null;
              })()}
            </div>

            <div className="space-y-2">
              <Label>Margin (%)</Label>
              <Controller
                name="margin"
                control={control}
                render={({ field }) => (
                  <Input
                    {...field}
                    type="number"
                    min="0"
                    max="100"
                    step="0.01"
                    placeholder="0.00"
                    value={field.value || ""}
                    onChange={(e) => {
                      const value = e.target.value;
                      field.onChange(value ? parseFloat(value) : undefined);
                    }}
                  />
                )}
              />
              {(() => {
                const destinationFromList = destinations.find(d => d.id === watchedDestinationId);
                const destination = selectedDestinationData?.destination || destinationFromList;
                const currentMargin = watch("margin");

                if (destination?.margin !== undefined && currentMargin === destination.margin) {
                  return (
                    <Text className="text-blue-600 text-sm mt-1">
                 
                    </Text>
                  );
                }
                return null;
              })()}
            </div>

            <div className="space-y-2">
              <Label>Tax Type</Label>
              <Controller
                name="tax_type"
                control={control}
                render={({ field }) => (
                  <Input
                    {...field}
                    placeholder="VAT, GST, etc."
                    value={field.value || ""}
                  />
                )}
              />
            </div>

            <div className="space-y-2">
              <Label>Tax Number</Label>
              <Controller
                name="tax_number"
                control={control}
                render={({ field }) => (
                  <Input
                    {...field}
                    placeholder="Tax identification number"
                    value={field.value || ""}
                  />
                )}
              />
            </div>
          </div>
        </div>
      </Container>

      {/* Hotel Images */}
      <Container className="divide-y p-0">
        <div className="flex items-center justify-between px-6 py-4">
          <Heading level="h2">Hotel Images</Heading>
          <Button
            type="button"
            variant="secondary"
            size="small"
            onClick={() => {
              // Trigger the hidden file input in HotelMediaSection
              const fileInput = document.getElementById(
                "media-upload"
              ) as HTMLInputElement;
              fileInput?.click();
            }}
          >
            Upload Images
          </Button>
        </div>
        <div className="px-6 py-4">
          <Text className="text-sm text-muted-foreground mb-4">
            Upload images that showcase this hotel. The first image or the one
            marked as thumbnail will be used as the main image.
          </Text>
          <HotelMediaSection form={form as any} />
        </div>
      </Container>

      {/* Tags */}
      <Container className="divide-y p-0">
        <div className="flex items-center justify-between px-6 py-4">
          <Heading level="h2">Tags</Heading>
          <Button
            type="button"
            variant="secondary"
            size="small"
            onClick={addTag}
            disabled={!tagInput.trim()}
          >
            Add Tag
          </Button>
        </div>
        <div className="px-6 py-4">
          <Text className="text-sm text-muted-foreground mb-4">
            Add tags to help categorize and search for this hotel.
          </Text>

          <div className="space-y-4">
            <div className="flex gap-2">
              <Input
                value={tagInput}
                onChange={(e) => setTagInput(e.target.value)}
                placeholder="Enter a tag"
                onKeyDown={(e) => {
                  if (e.key === "Enter") {
                    e.preventDefault();
                    addTag();
                  }
                }}
              />
            </div>

            {tags.length > 0 && (
              <div className="flex flex-wrap gap-2">
                {tags.map((tag, index) => (
                  <Badge
                    key={index}
                    className="flex items-center gap-1 bg-blue-100 text-blue-800 hover:bg-blue-200"
                  >
                    {tag}
                    <button
                      type="button"
                      onClick={() => removeTag(tag)}
                      className="ml-1 hover:bg-blue-200 rounded-full p-0.5 transition-colors"
                    >
                      <X size={12} />
                    </button>
                  </Badge>
                ))}
              </div>
            )}
          </div>
        </div>
      </Container>

      {/* Amenities */}
      <Container className="divide-y p-0">
        <div className="flex items-center justify-between px-6 py-4">
          <Heading level="h2">Amenities</Heading>
          <Button
            type="button"
            variant="secondary"
            size="small"
            onClick={addAmenity}
            disabled={!amenityInput.trim()}
          >
            Add Amenity
          </Button>
        </div>
        <div className="px-6 py-4">
          <Text className="text-sm text-muted-foreground mb-4">
            List the amenities and facilities available at this hotel.
          </Text>

          <div className="space-y-4">
            <div className="flex gap-2">
              <Input
                value={amenityInput}
                onChange={(e) => setAmenityInput(e.target.value)}
                placeholder="Enter an amenity"
                onKeyDown={(e) => {
                  if (e.key === "Enter") {
                    e.preventDefault();
                    addAmenity();
                  }
                }}
              />
            </div>

            {amenities.length > 0 && (
              <div className="flex flex-wrap gap-2">
                {amenities.map((amenity, index) => (
                  <Badge
                    key={index}
                    className="flex items-center gap-1 bg-green-100 text-green-800 hover:bg-green-200"
                  >
                    {amenity}
                    <button
                      type="button"
                      onClick={() => removeAmenity(amenity)}
                      className="ml-1 hover:bg-green-200 rounded-full p-0.5 transition-colors"
                    >
                      <X size={12} />
                    </button>
                  </Badge>
                ))}
              </div>
            )}
          </div>
        </div>
      </Container>

      {/* Hotel Rules */}
      <Container className="divide-y p-0">
        <div className="flex items-center justify-between px-6 py-4">
          <Heading level="h2">Hotel Rules</Heading>
          <Button
            type="button"
            variant="secondary"
            size="small"
            onClick={addRule}
            disabled={!ruleInput.trim()}
          >
            Add Rule
          </Button>
        </div>
        <div className="px-6 py-4">
          <Text className="text-sm text-muted-foreground mb-4">
            Define the rules and policies for guests staying at this hotel.
          </Text>

          <div className="space-y-4">
            <div className="flex gap-2">
              <Input
                value={ruleInput}
                onChange={(e) => setRuleInput(e.target.value)}
                placeholder="Enter a hotel rule"
                onKeyDown={(e) => {
                  if (e.key === "Enter") {
                    e.preventDefault();
                    addRule();
                  }
                }}
              />
            </div>

            {rules.length > 0 && (
              <div className="flex flex-wrap gap-2">
                {rules.map((rule, index) => (
                  <Badge
                    key={index}
                    className="flex items-center gap-1 bg-orange-100 text-orange-800 hover:bg-orange-200"
                  >
                    {rule}
                    <button
                      type="button"
                      onClick={() => removeRule(rule)}
                      className="ml-1 hover:bg-orange-200 rounded-full p-0.5 transition-colors"
                    >
                      <X size={12} />
                    </button>
                  </Badge>
                ))}
              </div>
            )}
          </div>
        </div>
      </Container>

      {/* Safety Measures */}
      <Container className="divide-y p-0">
        <div className="flex items-center justify-between px-6 py-4">
          <Heading level="h2">Safety Measures</Heading>
          <Button
            type="button"
            variant="secondary"
            size="small"
            onClick={addSafetyMeasure}
            disabled={!safetyInput.trim()}
          >
            Add Safety Measure
          </Button>
        </div>
        <div className="px-6 py-4">
          <Text className="text-sm text-muted-foreground mb-4">
            List the safety and security measures implemented at this hotel.
          </Text>

          <div className="space-y-4">
            <div className="flex gap-2">
              <Input
                value={safetyInput}
                onChange={(e) => setSafetyInput(e.target.value)}
                placeholder="Enter a safety measure"
                onKeyDown={(e) => {
                  if (e.key === "Enter") {
                    e.preventDefault();
                    addSafetyMeasure();
                  }
                }}
              />
            </div>

            {safetyMeasures.length > 0 && (
              <div className="flex flex-wrap gap-2">
                {safetyMeasures.map((safety, index) => (
                  <Badge
                    key={index}
                    className="flex items-center gap-1 bg-red-100 text-red-800 hover:bg-red-200"
                  >
                    {safety}
                    <button
                      type="button"
                      onClick={() => removeSafetyMeasure(safety)}
                      className="ml-1 hover:bg-red-200 rounded-full p-0.5 transition-colors"
                    >
                      <X size={12} />
                    </button>
                  </Badge>
                ))}
              </div>
            )}
          </div>
        </div>
      </Container>

      {/* Visibility Settings */}
      <Container className="divide-y p-0">
        <div className="flex items-center justify-between px-6 py-4">
          <Heading level="h2">Visibility Settings</Heading>
        </div>
        <div className="px-6 py-4">
          <VisibilitySettings
            title="Status"
            options={[
              {
                id: "is_active",
                label: "Active",
                description: "When active, the hotel will be visible to users",
                checked: watch("is_active"),
                onChange: (checked: boolean) => setValue("is_active", checked),
              },
              {
                id: "is_featured",
                label: "Featured",
                description:
                  "Featured hotels will be highlighted and shown prominently to users",
                checked: watch("is_featured") || false,
                onChange: (checked: boolean) =>
                  setValue("is_featured", checked),
              },
              {
                id: "is_pets_allowed",
                label: "Pets Allowed",
                description: "Allow guests to bring pets to this hotel",
                checked: watch("is_pets_allowed") || false,
                onChange: (checked: boolean) =>
                  setValue("is_pets_allowed", checked),
              },
            ]}
          />
        </div>
      </Container>

      {/* Form Actions */}
      <div className="flex items-center justify-end gap-3 bg-muted/50">
        <Button
          type="button"
          variant="secondary"
          onClick={onCancel}
          disabled={isSubmitting}
        >
          <X className="w-4 h-4 mr-2" />
          Cancel
        </Button>
        <Button
          type="button"
          onClick={handleSubmit(handleFormSubmit)}
          disabled={isSubmitting}
        >
          <Save className="w-4 h-4 mr-2" />
          {isSubmitting ? "Creating..." : "Create Hotel"}
        </Button>
      </div>
    </div>
  );
};

export default HotelCreateForm;
