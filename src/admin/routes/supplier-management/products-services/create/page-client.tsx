import React, { useState, useEffect } from "react";
import { Save, X } from "lucide-react";
import {
  Container,
  Heading,
  Text,
  Button,
  Input,
  Textarea,
  Select,
  Label,
  toast,
} from "@camped-ai/ui";
import { useCategories } from "../../../../hooks/supplier-products-services/use-categories";
import { useUnitTypes } from "../../../../hooks/supplier-products-services/use-unit-types";
import { useHotels } from "../../../../hooks/supplier-products-services/use-hotels";
import { useDestinations } from "../../../../hooks/supplier-products-services/use-destinations";
import { useProductsServices } from "../../../../hooks/supplier-products-services/use-products-services";

import DynamicFieldRenderer from "../../../../components/supplier-management/dynamic-field-renderer";

import { generateProductServiceName } from "../../../../../modules/supplier-products-services/utils/name-generator";


export interface FormData {
  name?: string; // Auto-generated in create mode
  description: string;
  base_cost: number | ""; // Required field
  valid_from: string; // Required - Date string in YYYY-MM-DD format
  valid_to: string; // Required - Date string in YYYY-MM-DD format
  category_id: string;
  unit_type_id: string;
  tags: string[];
  custom_fields: Record<string, any>;
  status: "active" | "inactive";
  change_reason?: string; // For edit mode
}

export interface ProductService {
  id: string;
  name: string;
  description?: string;
  base_cost?: number;
  valid_from?: string; // Date string in ISO format
  valid_to?: string; // Date string in ISO format
  category_id: string;
  unit_type_id: string;
  tags?: Array<{
    id: string;
    name: string;
    color?: string;
  }>;
  custom_fields?: Record<string, any>;
  status: "active" | "inactive";
  category?: {
    id: string;
    name: string;
    description?: string;
    is_active: boolean;
    dynamic_field_schema?: any[];
  };
  unit_type?: { id: string; name: string };
}

interface ProductServiceFormProps {
  mode: "create" | "edit";
  initialData?: ProductService;
  onSubmit: (data: FormData) => Promise<void>;
  onCancel: () => void;
  isSubmitting?: boolean;
  title?: string;
  submitButtonText?: string;
}

const ProductServiceForm: React.FC<ProductServiceFormProps> = ({
  mode,
  initialData,
  onSubmit,
  onCancel,
  isSubmitting = false,
  submitButtonText,
}) => {
  const { data: categoriesResponse, isLoading: categoriesLoading } =
    useCategories({ is_active: true });
  const { data: unitTypesResponse } = useUnitTypes({ is_active: true });

  // Hooks for name resolution
  const { data: hotelsResponse } = useHotels({ is_active: true, limit: 1000 });
  const { data: destinationsResponse } = useDestinations({ is_active: true, limit: 1000 });
  const { data: productsServicesResponse } = useProductsServices({ status: "active", limit: 1000 });

  // Extract arrays from response objects
  const categories = categoriesResponse?.categories || [];
  const unitTypes = unitTypesResponse?.unit_types || [];
  const hotels = hotelsResponse?.hotels || [];
  const destinations = destinationsResponse?.destinations || [];
  const productsServices = productsServicesResponse?.product_services || [];

  // Initialize form data based on mode
  const [formData, setFormData] = useState<FormData>(() => {
    if (mode === "edit" && initialData) {
      return {
        name: initialData.name,
        description: initialData.description || "",
        base_cost: initialData.base_cost || "",
        valid_from: initialData.valid_from ? initialData.valid_from.split('T')[0] : "",
        valid_to: initialData.valid_to ? initialData.valid_to.split('T')[0] : "",
        category_id: initialData.category_id || "",
        unit_type_id: initialData.unit_type_id || "",
        tags: initialData.tags?.map((tag) => tag.id) || [],
        custom_fields: initialData.custom_fields || {},
        status: initialData.status,
        change_reason: "",
      };
    }
    return {
      description: "",
      base_cost: "",
      valid_from: "",
      valid_to: "",
      category_id: "",
      unit_type_id: "",
      tags: [],
      custom_fields: {},
      status: "active",
    };
  });



  const [errors, setErrors] = useState<Record<string, string>>({});
  const [customFieldErrors, setCustomFieldErrors] = useState<
    Record<string, string>
  >({});
  const [isNameAutoGenerated, setIsNameAutoGenerated] = useState(mode === "create");

  // Function to detect if a name follows the auto-generated pattern
  const isAutoGeneratedName = (name: string, categoryName?: string): boolean => {
    if (!name || !categoryName) return false;

    // Auto-generated names follow the pattern: "Category – Field1 – Field2 – ... – (UnitType)"
    // They start with the category name followed by " – " and additional fields
    // They may end with a unit type in parentheses
    const startsWithCategory = name.startsWith(categoryName + " – ");
    const hasDelimiters = name.includes(" – ");

    // Additional check: if it ends with parentheses, it's likely auto-generated with unit type
    const endsWithParentheses = /\([^)]+\)$/.test(name);

    return startsWithCategory && hasDelimiters && (name.split(" – ").length > 1 || endsWithParentheses);
  };

  // Effect to detect auto-generated names in edit mode
  useEffect(() => {
    if (mode === "edit" && initialData && formData.name && categories.length > 0) {
      const selectedCategory = categories.find(cat => cat.id === formData.category_id);
      if (selectedCategory && isAutoGeneratedName(formData.name, selectedCategory.name)) {
        setIsNameAutoGenerated(true);
      } else {
        setIsNameAutoGenerated(false);
      }
    }
  }, [mode, initialData, formData.name, formData.category_id, categories]);

  // Show all categories since type field is removed
  const filteredCategories = React.useMemo(() => {
    return categories;
  }, [categories]);

  // Get selected category for dynamic fields
  const selectedCategory = categories.find(
    (cat) => cat.id === formData.category_id
  );
  const allDynamicFields = selectedCategory?.dynamic_field_schema || [];

  // Filter dynamic fields to only include fields marked for product/service usage
  const dynamicFieldSchema = allDynamicFields.filter((field: any) => {
    const isSupplierContext = !field.field_context || field.field_context === "supplier";
    const isUsedInProductServices = field.used_in_product_services === true;
    console.log(`Create Form Field ${field.key}: field_context=${field.field_context}, used_in_product_services=${field.used_in_product_services}, isSupplierContext=${isSupplierContext}, isUsedInProductServices=${isUsedInProductServices}`);
    return isSupplierContext && isUsedInProductServices;
  });

  // Create a query service adapter for name resolution
  const createQueryService = () => ({
    graph: async ({ entity, filters, fields }: { entity: string; filters: any; fields: string[] }) => {
      switch (entity) {
        case "hotel":
          const hotel = hotels.find(h => h.id === filters.id);
          return { data: hotel ? [{ id: hotel.id, name: hotel.name }] : [] };

        case "destination":
          const destination = destinations.find(d => d.id === filters.id);
          return { data: destination ? [{ id: destination.id, name: destination.name }] : [] };

        case "product_service":
          const productService = productsServices.find(ps => ps.id === filters.id);
          return { data: productService ? [{ id: productService.id, name: productService.name }] : [] };

        default:
          return { data: [] };
      }
    }
  });

  // Auto-generate name when custom fields, category, or unit type changes
  const generateName = async () => {
    if (!selectedCategory || !formData.category_id) {
      return;
    }

    try {
      const queryService = createQueryService();

      // Get unit type name for inclusion in the generated name
      const selectedUnitType = unitTypes.find(ut => ut.id === formData.unit_type_id);
      const unitTypeName = selectedUnitType?.name;

      const generatedName = await generateProductServiceName(
        selectedCategory.name,
        formData.custom_fields,
        dynamicFieldSchema,
        queryService,
        unitTypeName
      );

      // Only update if the current name is empty or was previously auto-generated
      if (!formData.name || formData.name === selectedCategory.name || isAutoGeneratedName(formData.name, selectedCategory.name) || isNameAutoGenerated) {
        setFormData(prev => ({ ...prev, name: generatedName }));
        setIsNameAutoGenerated(true);
      }
    } catch (error) {
      console.error("❌ Error generating name:", error);
    }
  };



  const handleInputChange = (field: keyof FormData, value: any) => {
    // Prevent editing auto-generated name in both create and edit modes
    if (field === "name" && isNameAutoGenerated) {
      return; // Block the change
    }

    setFormData((prev) => ({ ...prev, [field]: value }));

    // Clear error when user starts typing
    if (errors[field]) {
      setErrors((prev) => ({ ...prev, [field]: "" }));
    }
  };

  const handleCustomFieldChange = (key: string, value: any) => {
    setFormData((prev) => ({
      ...prev,
      custom_fields: { ...prev.custom_fields, [key]: value },
    }));

    // Clear custom field error when user starts typing
    if (customFieldErrors[key]) {
      setCustomFieldErrors((prev) => ({ ...prev, [key]: "" }));
    }
  };



  // Reset custom fields when category changes (create mode only)
  useEffect(() => {
    if (mode === "create" && formData.category_id) {
      setFormData((prev) => ({ ...prev, custom_fields: {} }));
      setCustomFieldErrors({});
    }
  }, [mode, formData.category_id]);

  // Auto-generate name when category changes (create mode or edit mode with auto-generated names)
  useEffect(() => {
    if (formData.category_id && selectedCategory) {
      if (mode === "create" || (mode === "edit" && isNameAutoGenerated)) {
        generateName();
      }
    }
  }, [mode, formData.category_id, selectedCategory, isNameAutoGenerated]);

  // Auto-generate name when custom fields change (create mode or edit mode with auto-generated names)
  useEffect(() => {
    if (formData.category_id && Object.keys(formData.custom_fields).length > 0) {
      if (mode === "create" || (mode === "edit" && isNameAutoGenerated)) {
        // Debounce the name generation to avoid too many calls
        const timeoutId = setTimeout(() => {
          generateName();
        }, 500);

        return () => clearTimeout(timeoutId);
      }
    }
  }, [mode, formData.custom_fields, formData.category_id, isNameAutoGenerated]);

  // Auto-generate name when unit type changes (create mode or edit mode with auto-generated names)
  useEffect(() => {
    if (formData.category_id && formData.unit_type_id) {
      if (mode === "create" || (mode === "edit" && isNameAutoGenerated)) {
        // Debounce the name generation to avoid too many calls
        const timeoutId = setTimeout(() => {
          generateName();
        }, 300);

        return () => clearTimeout(timeoutId);
      }
    }
  }, [mode, formData.unit_type_id, formData.category_id, isNameAutoGenerated]);

  // Auto-generate name when auto-generated flag is detected in edit mode
  useEffect(() => {
    if (mode === "edit" && isNameAutoGenerated && formData.category_id && selectedCategory) {
      // Regenerate the name with current field values to ensure it includes all fields and unit type
      generateName();
    }
  }, [mode, isNameAutoGenerated, formData.category_id, selectedCategory]);

  const validateForm = (): boolean => {
    const newErrors: Record<string, string> = {};
    const newCustomFieldErrors: Record<string, string> = {};

    if (!formData.category_id) {
      newErrors.category_id = "Category is required";
    }

    if (!formData.unit_type_id) {
      newErrors.unit_type_id = "Pricing unit is required";
    }

    // Validate required fields: base_cost, valid_from, valid_to
    if (formData.base_cost === "" || formData.base_cost === null || formData.base_cost === undefined) {
      newErrors.base_cost = "Base price is required";
    } else if (formData.base_cost < 0) {
      newErrors.base_cost = "Base price must be non-negative";
    }

    if (!formData.valid_from) {
      newErrors.valid_from = "Valid from date is required";
    }

    if (!formData.valid_to) {
      newErrors.valid_to = "Valid to date is required";
    }

    // Validate that valid_to is after valid_from
    if (formData.valid_from && formData.valid_to) {
      const fromDate = new Date(formData.valid_from);
      const toDate = new Date(formData.valid_to);
      if (toDate <= fromDate) {
        newErrors.valid_to = "Valid to date must be after valid from date";
      }
    }



    // Validate custom fields - only validate fields marked for product/service usage
    dynamicFieldSchema.forEach((field: any) => {
      if (field.required && (!field.field_context || field.field_context === "supplier") && field.used_in_product_services === true) {
        const value = formData.custom_fields[field.key];
        if (!value || (Array.isArray(value) && value.length === 0)) {
          newCustomFieldErrors[field.key] = `${field.label} is required`;
        }
      }
    });

    setErrors(newErrors);
    setCustomFieldErrors(newCustomFieldErrors);
    return (
      Object.keys(newErrors).length === 0 &&
      Object.keys(newCustomFieldErrors).length === 0
    );
  };

  const handleSubmit = async () => {
    if (!validateForm()) {
      toast.error("Please fill all the required fields");
      return;
    }

    try {
      await onSubmit(formData);
    } catch (error) {
      console.error(
        `Error ${mode === "create" ? "creating" : "updating"} product/service:`,
        error
      );
    }
  };

  const defaultSubmitText =
    mode === "create"
      ? isSubmitting
        ? "Creating..."
        : "Create Product/Service"
      : isSubmitting
        ? "Updating..."
        : "Update Product/Service";

  return (
    <>


      <Container className="divide-y p-0">
        <div className="flex items-center justify-between px-6 py-4">
          <Heading level="h2">Basic Information</Heading>
        </div>
        <div className="px-6 py-4">
          {/* Category and Pricing Unit fields - moved to top */}
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <div className="space-y-2">
              <Label htmlFor="category">Category *</Label>
              <Select
                value={formData.category_id}
                onValueChange={(value) =>
                  handleInputChange("category_id", value)
                }
                disabled={categoriesLoading}
              >
                <Select.Trigger
                  className={errors.category_id ? "border-red-500" : ""}
                >
                  <Select.Value
                    placeholder={
                      categoriesLoading
                        ? "Loading categories..."
                        : filteredCategories.length === 0
                          ? "No categories available"
                          : "Select category"
                    }
                  />
                </Select.Trigger>
                <Select.Content>
                  {filteredCategories.length === 0 ? (
                    <div className="px-2 py-1.5 text-sm text-ui-fg-muted">
                      No categories available
                    </div>
                  ) : (
                    filteredCategories.map((category) => (
                      <Select.Item key={category.id} value={category.id}>
                        <div className="flex items-center gap-2">
                          {category.icon && (
                            <span className="text-sm">{category.icon}</span>
                          )}
                          <span>{category.name}</span>
                          <span className="text-xs text-gray-500 ml-auto">
                            ({category.category_type})
                          </span>
                        </div>
                      </Select.Item>
                    ))
                  )}
                </Select.Content>
              </Select>
              {errors.category_id && (
                <Text size="small" className="text-red-600">
                  {errors.category_id}
                </Text>
              )}
              {filteredCategories.length === 0 && !categoriesLoading && (
                <Text size="small" className="text-amber-600">
                  No categories are configured. Please configure categories in the Categories Configuration
                  page.
                </Text>
              )}
            </div>

            <div className="space-y-2">
              <Label htmlFor="unit_type">Pricing Unit *</Label>
              <Select
                value={formData.unit_type_id}
                onValueChange={(value) =>
                  handleInputChange("unit_type_id", value)
                }
              >
                <Select.Trigger
                  className={errors.unit_type_id ? "border-red-500" : ""}
                >
                  <Select.Value placeholder="Select pricing unit" />
                </Select.Trigger>
                <Select.Content>
                  {unitTypes.map((unitType) => (
                    <Select.Item key={unitType.id} value={unitType.id}>
                      {unitType.name}
                    </Select.Item>
                  ))}
                </Select.Content>
              </Select>
              {errors.unit_type_id && (
                <Text size="small" className="text-red-600">
                  {errors.unit_type_id}
                </Text>
              )}
            </div>
          </div>

          {/* Name field - moved below category and pricing unit */}
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mt-3">
            <div className="space-y-2">
              <Label htmlFor="name">
                Name *
                {isNameAutoGenerated && (
                  <span className="ml-2 text-xs text-blue-600 font-normal">
                    (Auto-generated)
                  </span>
                )}
              </Label>
              <Input
                id="name"
                value={formData.name || ""}
                onChange={(e) => handleInputChange("name", e.target.value)}
                placeholder="E.g. Youth Ski Pass – Arosa (5 Days)"
                className={errors.name ? "border-red-500" : ""}
                disabled={true}
                readOnly={isNameAutoGenerated}
              />
              {errors.name && (
                <Text size="small" className="text-red-600">
                  {errors.name}
                </Text>
              )}
              {isNameAutoGenerated && (
                <Text size="small" className="text-blue-600">
                  Name is automatically generated based on category, Pricing Unit and custom fields and cannot be edited.
                </Text>
              )}
            </div>
          </div>

          <div className="mt-3 space-y-2">
            <Label htmlFor="description">Description</Label>
            <Textarea
              id="description"
              value={formData.description}
              onChange={(e) => handleInputChange("description", e.target.value)}
              placeholder="Add details to help concierge and guests understand the product or service.."
              rows={4}
            />
          </div>

          {/* New fields: Valid From, Valid To, and Base Price */}
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mt-3">
            <div className="space-y-2">
              <Label htmlFor="valid_from">Valid From *</Label>
              <Input
                id="valid_from"
                type="date"
                value={formData.valid_from}
                onChange={(e) => handleInputChange("valid_from", e.target.value)}
                className={errors.valid_from ? "border-red-500" : ""}
              />
              {errors.valid_from && (
                <Text size="small" className="text-red-600">
                  {errors.valid_from}
                </Text>
              )}
            </div>

            <div className="space-y-2">
              <Label htmlFor="valid_to">Valid To *</Label>
              <Input
                id="valid_to"
                type="date"
                value={formData.valid_to}
                onChange={(e) => handleInputChange("valid_to", e.target.value)}
                className={errors.valid_to ? "border-red-500" : ""}
              />
              {errors.valid_to && (
                <Text size="small" className="text-red-600">
                  {errors.valid_to}
                </Text>
              )}
            </div>

            <div className="space-y-2">
              <Label htmlFor="base_cost">Base Price (CHF) *</Label>
              <Input
                id="base_cost"
                type="number"
                step="0.01"
                min="0"
                value={formData.base_cost}
                onChange={(e) => handleInputChange("base_cost", e.target.value === "" ? "" : parseFloat(e.target.value))}
                placeholder="0.00"
                className={errors.base_cost ? "border-red-500" : ""}
              />
              {errors.base_cost && (
                <Text size="small" className="text-red-600">
                  {errors.base_cost}
                </Text>
              )}
            </div>
          </div>
        </div>

      </Container>

      {dynamicFieldSchema.length > 0 && (
        <Container className="divide-y p-0 mt-1">
          <div className="flex items-center justify-between px-6 py-4">
            <Heading level="h2">Additional Information</Heading>
          </div>
          <div className="px-6 py-4">
            <DynamicFieldRenderer
              schema={dynamicFieldSchema}
              values={formData.custom_fields}
              onChange={handleCustomFieldChange}
              errors={customFieldErrors}
              disabled={isSubmitting}
              fieldContext="supplier"
              excludeProductServiceId={mode === "edit" ? initialData?.id : undefined}
            />
          </div>
        </Container>
      )}

      {/* Form Actions */}
      <div className="flex items-center justify-end gap-3 mt-3">
        <Button
          type="button"
          variant="secondary"
          onClick={onCancel}
          disabled={isSubmitting}
        >
          <X className="w-4 h-4 mr-2" />
          Cancel
        </Button>
        <Button type="button" onClick={handleSubmit} disabled={isSubmitting}>
          <Save className="w-4 h-4 mr-2" />
          {submitButtonText || defaultSubmitText}
        </Button>
      </div>
    </>
  );
};

export default ProductServiceForm;
