import { useQuery, useMutation, useQueryClient } from "@tanstack/react-query";
import { toast } from "@camped-ai/ui";
import { useState } from "react";
import * as XLSX from "xlsx";

// Dynamic Field Schema Types
export interface DynamicFieldSchema {
  label: string;
  key: string;
  type:
    | "text"
    | "number"
    | "dropdown"
    | "multi-select"
    | "date"
    | "time"
    | "time-range"
    | "date-range"
    | "boolean"
    | "number-range"
    | "hotels"
    | "destinations"
    | "addons";
  options?: string[];
  required: boolean;
  used_in_filtering?: boolean;
  used_in_supplier_offering?: boolean;
  used_in_product?: boolean;
  used_in_uniqueness?: boolean; // New flag specifically for uniqueness validation
  locked_in_offerings?: boolean;
  order?: number; // Optional field ordering for product name generation
}

// Types
export interface Category {
  id: string;
  name: string;
  description?: string;
  category_type: "Product" | "Service" | "Both";
  icon?: string;
  dynamic_field_schema?: DynamicFieldSchema[];
  is_active: boolean;
  created_at: string;
  updated_at: string;
  product_count?: number;
  service_count?: number;
  total_count?: number;
}

export interface CreateCategoryInput {
  name: string;
  description?: string;
  category_type?: "Product" | "Service" | "Both";
  icon?: string;
  dynamic_field_schema?: DynamicFieldSchema[];
  is_active?: boolean;
}

export interface UpdateCategoryInput {
  name?: string;
  description?: string;
  category_type?: "Product" | "Service" | "Both";
  icon?: string;
  dynamic_field_schema?: DynamicFieldSchema[];
  is_active?: boolean;
}

export interface CategoryFilters {
  name?: string; // Keep for backward compatibility
  search?: string; // New dedicated search parameter
  category_type?: "Product" | "Service" | "Both";
  is_active?: boolean;
  limit?: number;
  offset?: number;
  sort_by?: string;
  sort_order?: "asc" | "desc";
}

export interface CategoryListResponse {
  categories: Category[];
  count: number;
  limit: number;
  offset: number;
}

// Query keys
const QUERY_KEYS = {
  all: ["supplier-products-services", "categories"] as const,
  lists: () => [...QUERY_KEYS.all, "list"] as const,
  list: (filters: CategoryFilters) => [...QUERY_KEYS.lists(), filters] as const,
  details: () => [...QUERY_KEYS.all, "detail"] as const,
  detail: (id: string) => [...QUERY_KEYS.details(), id] as const,
};

// Hook to fetch categories list
export const useCategories = (filters: CategoryFilters = {}) => {
  return useQuery({
    queryKey: QUERY_KEYS.list(filters),
    queryFn: async (): Promise<CategoryListResponse> => {
      const params = new URLSearchParams();

      // Use search parameter if available, otherwise fall back to name for backward compatibility
      const searchParam = filters.search || filters.name;
      if (searchParam) params.append("search", searchParam);
      if (filters.category_type)
        params.append("category_type", filters.category_type);
      if (filters.is_active !== undefined)
        params.append("is_active", filters.is_active.toString());
      if (filters.limit) params.append("limit", filters.limit.toString());
      if (filters.offset) params.append("offset", filters.offset.toString());
      if (filters.sort_by) params.append("sort_by", filters.sort_by);
      if (filters.sort_order) params.append("sort_order", filters.sort_order);

      const url = `/admin/supplier-management/products-services/categories${
        params.toString() ? `?${params.toString()}` : ""
      }`;

      const response = await fetch(url, {
        credentials: "include",
        headers: {
          "Content-Type": "application/json",
        },
      });

      if (!response.ok) {
        throw new Error("Failed to fetch categories");
      }

      return response.json();
    },
  });
};

// Hook to fetch a single category
export const useCategory = (id: string) => {
  return useQuery({
    queryKey: QUERY_KEYS.detail(id),
    queryFn: async (): Promise<{ category: Category }> => {
      const response = await fetch(
        `/admin/supplier-management/products-services/categories/${id}`,
        {
          credentials: "include",
          headers: {
            "Content-Type": "application/json",
          },
        }
      );

      if (!response.ok) {
        throw new Error("Failed to fetch category");
      }

      return response.json();
    },
    enabled: !!id,
  });
};

// Hook to create a category
export const useCreateCategory = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: async (
      data: CreateCategoryInput
    ): Promise<{ category: Category }> => {
      console.log("Creating category with data:", data);

      const response = await fetch(
        "/admin/supplier-management/products-services/categories",
        {
          method: "POST",
          headers: {
            "Content-Type": "application/json",
          },
          body: JSON.stringify(data),
          credentials: "include",
        }
      );

      console.log("Create category response status:", response.status);

      if (!response.ok) {
        const errorData = await response
          .json()
          .catch(() => ({ message: "Unknown error occurred" }));
        console.error("Create category error:", errorData);
        throw new Error(
          errorData.message || `Failed to create category (${response.status})`
        );
      }

      const result = await response.json();
      console.log("Create category success:", result);
      return result;
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: QUERY_KEYS.lists() });
      // Don't show toast here since it's handled in the component
    },
    onError: (error: Error) => {
      console.error("Create category mutation error:", error);
      // Don't show toast here since it's handled in the component
    },
  });
};

// Hook to update a category
export const useUpdateCategory = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: async ({
      id,
      data,
    }: {
      id: string;
      data: UpdateCategoryInput;
    }): Promise<{ category: Category }> => {
      const response = await fetch(
        `/admin/supplier-management/products-services/categories/${id}`,
        {
          method: "PUT",
          headers: {
            "Content-Type": "application/json",
          },
          body: JSON.stringify(data),
          credentials: "include",
        }
      );

      if (!response.ok) {
        const error = await response.json();
        throw new Error(error.message || "Failed to update category");
      }

      return response.json();
    },
    onSuccess: (_, { id }) => {
      queryClient.invalidateQueries({ queryKey: QUERY_KEYS.lists() });
      queryClient.invalidateQueries({ queryKey: QUERY_KEYS.detail(id) });
      toast.success("Category updated successfully");
    },
    onError: (error: Error) => {
      toast.error(error.message);
    },
  });
};

// Hook to check category usage before inactivation
export const useCategoryUsageCheck = () => {
  return useMutation({
    mutationFn: async (
      id: string
    ): Promise<{
      canInactivate: boolean;
      usage: {
        productCount: number;
        serviceCount: number;
        totalCount: number;
        activeProductCount: number;
        activeServiceCount: number;
        activeCount: number;
        inactiveProductCount: number;
        inactiveServiceCount: number;
        inactiveCount: number;
      };
      message: string;
    }> => {
      const response = await fetch(
        `/admin/supplier-management/products-services/categories/${id}/usage-check`,
        {
          method: "GET",
          credentials: "include",
          headers: {
            "Content-Type": "application/json",
          },
        }
      );

      if (!response.ok) {
        const error = await response.json();
        throw new Error(error.message || "Failed to check category usage");
      }

      return response.json();
    },
  });
};

// Hook to delete a category
export const useDeleteCategory = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: async (
      id: string
    ): Promise<{ deleted: boolean; id: string }> => {
      const response = await fetch(
        `/admin/supplier-management/products-services/categories/${id}`,
        {
          method: "DELETE",
          credentials: "include",
        }
      );

      if (!response.ok) {
        const error = await response.json();
        throw new Error(error.message || "Failed to delete category");
      }

      return response.json();
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: QUERY_KEYS.lists() });
      toast.success("Category deleted successfully");
    },
    onError: (error: Error) => {
      toast.error(error.message);
    },
  });
};



// Import/Export Types
export interface ImportValidationError {
  row: number;
  field: string;
  message: string;
  value?: any;
}

export interface ImportProgress {
  processed: number;
  total: number;
  percentage: number;
  currentItem?: string;
}

export interface ImportResult {
  success: boolean;
  imported: number;
  errors: ImportValidationError[];
  message: string;
}

// Template generation utility for categories
export const generateCategoryTemplate = () => {
  const templateData = [
    {
      // Basic fields
      name: "Kids Club",
      description: "Activities and services for children",
      category_type: "Service", // Product, Service, or Both
      icon: "🧒",
      is_active: true,

      // Dynamic field schema examples - JSON string representation
      dynamic_field_schema: JSON.stringify([
        {
          label: "Age Range",
          key: "age_range",
          type: "number-range",
          required: true,
          used_in_filtering: true,
          used_in_supplier_offering: true,
          used_in_product: true,
          locked_in_offerings: false,
          order: 1,
        },
        {
          label: "Activity Type",
          key: "activity_type",
          type: "dropdown",
          options: ["Indoor", "Outdoor", "Mixed"],
          required: false,
          used_in_filtering: true,
          used_in_supplier_offering: true,
          used_in_product: true,
          locked_in_offerings: false,
          order: 2,
        },
      ]),
    },
    {
      name: "Transportation",
      description: "Vehicle and transport services",
      category_type: "Service",
      icon: "🚗",
      is_active: true,
      dynamic_field_schema: JSON.stringify([
        {
          label: "Vehicle Type",
          key: "vehicle_type",
          type: "dropdown",
          options: ["Sedan", "Minivan", "Bus", "Luxury Car"],
          required: true,
          used_in_filtering: true,
          used_in_supplier_offering: true,
          used_in_product: true,
          locked_in_offerings: false,
        },
        {
          label: "Passenger Capacity",
          key: "passenger_capacity",
          type: "number",
          required: true,
          used_in_filtering: true,
          used_in_supplier_offering: true,
          used_in_product: true,
          locked_in_offerings: false,
        },
      ]),
    },
    {
      name: "Accommodation",
      description: "Hotel rooms and lodging services",
      category_type: "Product",
      icon: "🏨",
      is_active: true,
      dynamic_field_schema: JSON.stringify([
        {
          label: "Room Type",
          key: "room_type",
          type: "dropdown",
          options: ["Standard", "Deluxe", "Suite", "Family Room"],
          required: true,
          used_in_filtering: true,
          used_in_supplier_offering: true,
          used_in_product: true,
          locked_in_offerings: false,
        },
        {
          label: "Amenities",
          key: "amenities",
          type: "multi-select",
          options: ["WiFi", "Balcony", "Sea View", "Mini Bar", "Jacuzzi"],
          required: false,
          used_in_filtering: true,
          used_in_supplier_offering: true,
          used_in_product: true,
          locked_in_offerings: false,
        },
        {
          label: "Pet Friendly",
          key: "pet_friendly",
          type: "boolean",
          required: false,
          used_in_filtering: true,
          used_in_supplier_offering: true,
          used_in_product: true,
          locked_in_offerings: true,
        },
      ]),
    },
  ];

  return templateData;
};

// CSV export utility
export const exportToCSV = (data: any[], filename: string) => {
  const csvContent = convertToCSV(data);
  const blob = new Blob([csvContent], { type: "text/csv;charset=utf-8;" });
  const link = document.createElement("a");
  const url = URL.createObjectURL(blob);
  link.setAttribute("href", url);
  link.setAttribute(
    "download",
    `${filename}_${new Date().toISOString().split("T")[0]}.csv`
  );
  link.style.visibility = "hidden";
  document.body.appendChild(link);
  link.click();
  document.body.removeChild(link);
  URL.revokeObjectURL(url);
};

// Excel export utility
export const exportToExcel = (data: any[], filename: string) => {
  const worksheet = XLSX.utils.json_to_sheet(data);
  const workbook = XLSX.utils.book_new();
  XLSX.utils.book_append_sheet(workbook, worksheet, "Categories");

  // Auto-size columns
  const colWidths = Object.keys(data[0] || {}).map((key) => ({
    wch: Math.max(key.length, 15),
  }));
  worksheet["!cols"] = colWidths;

  const excelBuffer = XLSX.write(workbook, { bookType: "xlsx", type: "array" });
  const blob = new Blob([excelBuffer], {
    type: "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet",
  });
  const link = document.createElement("a");
  const url = URL.createObjectURL(blob);
  link.setAttribute("href", url);
  link.setAttribute(
    "download",
    `${filename}_${new Date().toISOString().split("T")[0]}.xlsx`
  );
  link.style.visibility = "hidden";
  document.body.appendChild(link);
  link.click();
  document.body.removeChild(link);
  URL.revokeObjectURL(url);
};

// Helper function to convert data to CSV
const convertToCSV = (data: any[]): string => {
  if (!data.length) return "";

  const headers = Object.keys(data[0]);
  const csvRows = [
    headers.join(","),
    ...data.map((row) =>
      headers
        .map((header) => {
          const value = row[header];
          // Handle JSON strings and special characters
          if (
            typeof value === "string" &&
            (value.includes(",") || value.includes('"') || value.includes("\n"))
          ) {
            return `"${value.replace(/"/g, '""')}"`;
          }
          return value || "";
        })
        .join(",")
    ),
  ];

  return csvRows.join("\n");
};

// Validation function for import data
const validateImportData = (data: any[]): ImportValidationError[] => {
  const errors: ImportValidationError[] = [];

  data.forEach((row, index) => {
    const rowNumber = index + 2; // +2 because Excel rows start at 1 and we have headers

    // Required field validation
    if (!row.name || typeof row.name !== "string" || row.name.trim() === "") {
      errors.push({
        row: rowNumber,
        field: "name",
        message: "Name is required and must be a non-empty string",
        value: row.name,
      });
    }

    // Category type validation
    if (
      row.category_type &&
      !["Product", "Service", "Both"].includes(row.category_type)
    ) {
      errors.push({
        row: rowNumber,
        field: "category_type",
        message: "Category type must be one of: Product, Service, Both",
        value: row.category_type,
      });
    }

    // Boolean validation for is_active
    if (
      row.is_active !== undefined &&
      typeof row.is_active !== "boolean" &&
      !["true", "false", "1", "0", "yes", "no"].includes(
        String(row.is_active).toLowerCase()
      )
    ) {
      errors.push({
        row: rowNumber,
        field: "is_active",
        message: "is_active must be a boolean value (true/false, 1/0, yes/no)",
        value: row.is_active,
      });
    }

    // Dynamic field schema validation
    if (row.dynamic_field_schema) {
      try {
        const schema =
          typeof row.dynamic_field_schema === "string"
            ? JSON.parse(row.dynamic_field_schema)
            : row.dynamic_field_schema;

        if (!Array.isArray(schema)) {
          errors.push({
            row: rowNumber,
            field: "dynamic_field_schema",
            message: "Dynamic field schema must be a JSON array",
            value: row.dynamic_field_schema,
          });
        } else {
          // Validate each field in the schema
          schema.forEach((field: any, fieldIndex: number) => {
            if (!field.label || !field.key || !field.type) {
              errors.push({
                row: rowNumber,
                field: `dynamic_field_schema[${fieldIndex}]`,
                message: "Each dynamic field must have label, key, and type",
                value: field,
              });
            }

            if (
              field.type &&
              ![
                "text",
                "number",
                "dropdown",
                "multi-select",
                "date",
                "boolean",
                "number-range",
              ].includes(field.type)
            ) {
              errors.push({
                row: rowNumber,
                field: `dynamic_field_schema[${fieldIndex}].type`,
                message:
                  "Field type must be one of: text, number, dropdown, multi-select, date, boolean, number-range",
                value: field.type,
              });
            }
          });
        }
      } catch (parseError) {
        errors.push({
          row: rowNumber,
          field: "dynamic_field_schema",
          message: "Dynamic field schema must be valid JSON",
          value: row.dynamic_field_schema,
        });
      }
    }
  });

  return errors;
};

// Custom hook for category import/export operations
export const useCategoryImportExport = () => {
  const [importProgress, setImportProgress] = useState<ImportProgress>({
    processed: 0,
    total: 0,
    percentage: 0,
  });
  const queryClient = useQueryClient();

  // File parsing function
  const parseImportFile = async (
    file: File
  ): Promise<{ data: any[]; errors: ImportValidationError[] }> => {
    try {
      let data: any[] = [];

      if (file.name.endsWith(".csv")) {
        // Parse CSV
        const text = await file.text();
        const lines = text.split("\n").filter((line) => line.trim());
        if (lines.length < 2)
          throw new Error(
            "CSV file must have at least a header row and one data row"
          );

        const headers = lines[0]
          .split(",")
          .map((h) => h.trim().replace(/"/g, ""));
        data = lines.slice(1).map((line) => {
          const values = line.split(",").map((v) => v.trim().replace(/"/g, ""));
          const row: any = {};
          headers.forEach((header, index) => {
            row[header] = values[index] || "";
          });
          return row;
        });
      } else if (file.name.endsWith(".xlsx") || file.name.endsWith(".xls")) {
        // Parse Excel
        const buffer = await file.arrayBuffer();
        const workbook = XLSX.read(buffer, { type: "array" });
        const sheetName = workbook.SheetNames[0];
        const worksheet = workbook.Sheets[sheetName];
        data = XLSX.utils.sheet_to_json(worksheet);
      } else {
        throw new Error(
          "Unsupported file format. Please use CSV or Excel files."
        );
      }

      // Transform data
      const transformedData = data.map((row) => ({
        ...row,
        // Convert string booleans to actual booleans
        is_active:
          row.is_active === undefined
            ? true
            : ["true", "1", "yes"].includes(
                String(row.is_active).toLowerCase()
              ),
        // Parse dynamic_field_schema if it's a string
        dynamic_field_schema: row.dynamic_field_schema
          ? typeof row.dynamic_field_schema === "string"
            ? JSON.parse(row.dynamic_field_schema)
            : row.dynamic_field_schema
          : undefined,
      }));

      // Validate data
      const errors = validateImportData(transformedData);

      return { data: transformedData, errors };
    } catch (error) {
      throw new Error(
        `Failed to parse file: ${
          error instanceof Error ? error.message : "Unknown error"
        }`
      );
    }
  };

  // Import mutation
  const importMutation = useMutation({
    mutationFn: async (categories: any[]): Promise<ImportResult> => {
      try {
        setImportProgress({
          processed: 0,
          total: categories.length,
          percentage: 0,
        });

        const response = await fetch(
          "/admin/supplier-management/products-services/categories/import",
          {
            method: "POST",
            headers: {
              "Content-Type": "application/json",
            },
            credentials: "include",
            body: JSON.stringify({ categories }),
          }
        );

        if (!response.ok) {
          const error = await response.json();
          throw new Error(error.message || "Import failed");
        }

        const result = await response.json();
        setImportProgress({
          processed: categories.length,
          total: categories.length,
          percentage: 100,
        });

        return result;
      } catch (error) {
        throw new Error(
          `Import failed: ${
            error instanceof Error ? error.message : "Unknown error"
          }`
        );
      }
    },
    onSuccess: (result) => {
      queryClient.invalidateQueries({ queryKey: QUERY_KEYS.lists() });
      toast.success(result.message);
    },
    onError: (error: Error) => {
      toast.error(error.message);
    },
  });

  return {
    parseImportFile,
    importCategories: importMutation.mutateAsync,
    isImporting: importMutation.isPending,
    importProgress,
    generateTemplate: generateCategoryTemplate,
    validateData: validateImportData,
    exportToCSV,
    exportToExcel,
  };
};
